<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="1a6273b75e5944d496852e0de95685c9" class="chart-container" style="width:800px; height:400px; "></div>
    <script>
        var chart_1a6273b75e5944d496852e0de95685c9 = echarts.init(
            document.getElementById('1a6273b75e5944d496852e0de95685c9'), 'white', {renderer: 'canvas'});
        var option_1a6273b75e5944d496852e0de95685c9 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "sankey",
            "data": [
                {
                    "name": "Fatal Accidents",
                    "itemStyle": {
                        "color": "#FFBBBB"
                    }
                },
                {
                    "name": "Injury Accidents",
                    "itemStyle": {
                        "color": "#BFBCDA"
                    }
                },
                {
                    "name": "Property Damage Only",
                    "itemStyle": {
                        "color": "#99DAEE"
                    }
                },
                {
                    "name": "Fatigue driving"
                },
                {
                    "name": "Red-light running"
                },
                {
                    "name": "Speeding"
                },
                {
                    "name": "Erratic driving"
                },
                {
                    "name": "Mobile phone driving"
                },
                {
                    "name": "No unsafe behavior"
                }
            ],
            "links": [
                {
                    "source": "Fatal Accidents",
                    "target": "Fatigue driving",
                    "value": 1
                },
                {
                    "source": "Fatal Accidents",
                    "target": "Red-light running",
                    "value": 5
                },
                {
                    "source": "Fatal Accidents",
                    "target": "Speeding",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "Erratic driving",
                    "value": 2
                },
                {
                    "source": "Injury Accidents",
                    "target": "Fatigue driving",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "Mobile phone driving",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "No unsafe behavior",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "Red-light running",
                    "value": 4
                },
                {
                    "source": "Injury Accidents",
                    "target": "Speeding",
                    "value": 2
                },
                {
                    "source": "Property Damage Only",
                    "target": "Erratic driving",
                    "value": 19
                },
                {
                    "source": "Property Damage Only",
                    "target": "Fatigue driving",
                    "value": 4
                },
                {
                    "source": "Property Damage Only",
                    "target": "Mobile phone driving",
                    "value": 9
                },
                {
                    "source": "Property Damage Only",
                    "target": "No unsafe behavior",
                    "value": 13
                },
                {
                    "source": "Property Damage Only",
                    "target": "Red-light running",
                    "value": 7
                },
                {
                    "source": "Property Damage Only",
                    "target": "Speeding",
                    "value": 4
                }
            ],
            "left": "5%",
            "top": "5%",
            "right": "20%",
            "bottom": "5%",
            "nodeWidth": 20,
            "nodeGap": 10,
            "nodeAlign": "justify",
            "orient": "horizontal",
            "draggable": true,
            "label": {
                "show": true,
                "position": "right",
                "color": "black",
                "margin": 8,
                "fontSize": 14,
                "fontFamily": "Times New Roman"
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.3,
                "curveness": 0.5,
                "type": "solid",
                "color": "source"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {}
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    }
};
        chart_1a6273b75e5944d496852e0de95685c9.setOption(option_1a6273b75e5944d496852e0de95685c9);
    </script>
</body>
</html>
