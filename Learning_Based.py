
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error
import shap
import numpy as np
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
import pandas as pd
import optuna
from sklearn.svm import SVR
import matplotlib.pyplot as plt
import os

def train_svr_optuna(X_train, y_train, X_test, y_test, n_trials=50):
    """
    使用 Optuna 进行 SVR 回归模型的超参数调优，并评估其性能。

    参数：
    - X_train, y_train: 训练集特征和目标变量
    - X_test, y_test: 测试集特征和目标变量
    - n_trials: 试验次数，即优化过程中的迭代次数

    返回：
    - best_model: 调优后的最佳 SVR 模型
    - y_pred: 测试集预测值
    """
    def svr_objective(trial):
        # 定义要调优的超参数空间
        C = trial.suggest_loguniform('C', 1e-3, 1e3)
        epsilon = trial.suggest_loguniform('epsilon', 1e-4, 1e1)
        kernel = trial.suggest_categorical('kernel', ['linear', 'rbf', 'poly'])
        
        params = {
            'C': C,
            'epsilon': epsilon,
            'kernel': kernel
        }

        if kernel in ['rbf', 'poly']:
            gamma = trial.suggest_loguniform('gamma', 1e-4, 1e1)
            params['gamma'] = gamma
        if kernel == 'poly':
            degree = trial.suggest_int('degree', 2, 5)
            params['degree'] = degree

        # 创建SVR模型
        svr = SVR(**params)
        svr.fit(X_train, y_train)
        preds = svr.predict(X_test)
        rmse = mean_squared_error(y_test, preds, squared=False)
        return rmse

    # 调整 Optuna 的日志级别，抑制每个 trial 的日志输出
    optuna.logging.set_verbosity(optuna.logging.WARNING)

    # 使用 TPE 采样器进行优化
    sampler = TPESampler(seed=42)
    study = optuna.create_study(direction='minimize', sampler=sampler)
    study.optimize(svr_objective, n_trials=n_trials, n_jobs=8)

    # 获取最佳参数并训练最终模型
    best_params = study.best_params
    kernel = best_params.pop('kernel')
    
    # 处理不同 kernel 的参数
    if kernel == 'poly':
        degree = best_params.pop('degree', 3)
        svr_best = SVR(kernel=kernel, degree=degree, **best_params)
    elif kernel in ['rbf', 'linear']:
        svr_best = SVR(kernel=kernel, **best_params)
    else:
        raise ValueError(f"Unsupported kernel type: {kernel}")

    svr_best.fit(X_train, y_train)

    # 在测试集上进行预测
    y_pred = svr_best.predict(X_test)

    # 输出最佳超参数和性能指标
    print("SVR 最佳超参数:", best_params)
    print(f"SVR kernel: {kernel}")
    if kernel == 'poly':
        print(f"SVR degree: {degree}")
    print("SVR R²:", r2_score(y_test, y_pred))
    print("SVR RMSE:", mean_squared_error(y_test, y_pred, squared=False))

    return svr_best, y_pred


def explain_shap_svr(model, X_train, X_test, feature_names, plot_type="bar"):
    """
    使用 SHAP 解释 SVR 模型。

    参数：
    - model: 训练好的 SVR 模型
    - X_train: 训练集特征（用于构建 SHAP 解释器）
    - X_test: 测试集特征（用于计算 SHAP 值）
    - feature_names: 特征名称列表
    - plot_type: SHAP 图类型（"bar" 或 "summary"）

    返回：
    - shap_values: 计算得到的 SHAP 值
    """
    # 如果 X_train 不是 DataFrame，将其转换为 DataFrame 并添加列名
    if not isinstance(X_train, pd.DataFrame):
        X_train = pd.DataFrame(X_train, columns=feature_names)
    if not isinstance(X_test, pd.DataFrame):
        X_test = pd.DataFrame(X_test, columns=feature_names)

    # 初始化 SHAP 解释器
    explainer = shap.KernelExplainer(model.predict, X_train, link="identity")
    
    # 计算 SHAP 值
    shap_values = explainer.shap_values(X_test, nsamples=100)

    # 绘制摘要图
    if plot_type == "bar":
        shap.summary_plot(shap_values, features=X_test, feature_names=feature_names, plot_type="bar")
    else:
        shap.summary_plot(shap_values, features=X_test, feature_names=feature_names)

    # 可视化单个预测的 SHAP 值（局部解释）
    # 例如，选择测试集中的第一个样本
    # sample_index = 0
    # shap.force_plot(explainer.expected_value, shap_values[sample_index], X_test.iloc[sample_index], matplotlib=True)

    return shap_values

def train_random_forest(X_train, y_train, X_test, y_test,):
    """
    使用 GridSearchCV 自动调参训练随机森林回归模型，并评估其性能。

    参数：
    - X_train, y_train: 训练集特征和目标变量
    - X_test, y_test: 测试集特征和目标变量
    - n_estimators, max_depth, min_samples_split, random_state: 模型超参数, 
    - baseline为n_estimators=100, max_depth=None, min_samples_split=2, random_state=42
    rf = RandomForestRegressor(
        n_estimators=n_estimators,
        max_depth=max_depth,
        min_samples_split=min_samples_split,
        random_state=random_state,
        n_jobs=-1
    )

    返回：
    - best_rf: 最佳的随机森林模型
    - y_pred: 测试集预测值
    """
    # 定义参数网格
    param_grid = {
        'n_estimators': [50, 100, 200, 300],
        'max_depth': [None, 10, 20, 30],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4],
        'max_features': ['auto', 'sqrt', 'log2']
    }

    # 初始化随机森林模型
    rf = RandomForestRegressor(random_state=42)

    # 使用 GridSearchCV 进行超参数搜索
    # 网格搜索可能需要大量的计算资源和时间，特别是当参数网格较大时。你可以通过减少参数范围或使用 RandomizedSearchCV 来缓解。
    grid_search = GridSearchCV(
        estimator=rf,
        param_grid=param_grid,
        cv=5,
        n_jobs=-1,
        scoring='neg_mean_squared_error'
    )

    # 训练模型
    grid_search.fit(X_train, y_train)

    # 获取最佳模型
    best_rf = grid_search.best_estimator_

    # 在测试集上进行预测
    y_pred = best_rf.predict(X_test)

    # 输出最佳超参数和性能指标
    print("rf 最佳超参数:", grid_search.best_params_)
    # print("训练集最优得分 (MSE):", -grid_search.best_score_)
    print("rf R²:", r2_score(y_test, y_pred))
    print("rf RMSE:", np.sqrt(mean_squared_error(y_test, y_pred)))

    return best_rf, y_pred

import optuna
from optuna.samplers import TPESampler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import cross_val_score
import numpy as np

def train_random_forest_optuna(X_train, y_train, X_test, y_test, n_trials=50):
    """
    使用 Optuna 进行随机森林回归模型的超参数调优，并评估其性能。

    参数：
    - X_train, y_train: 训练集特征和目标变量
    - X_test, y_test: 测试集特征和目标变量
    - n_trials: 试验次数，即优化过程中的迭代次数

    返回：
    - best_rf: 调优后的最佳随机森林模型
    - y_pred: 测试集预测值
    """
    def rf_objective(trial):
        # 定义要调优的超参数空间
        param = {
            'n_estimators': trial.suggest_int('n_estimators', 50, 300),
            'max_depth': trial.suggest_categorical('max_depth', [None] + list(range(5, 51))),
            'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
            'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 20),
            'max_features': trial.suggest_categorical('max_features', [1, 'sqrt', 'log2', None]),
            'bootstrap': trial.suggest_categorical('bootstrap', [True, False]),
            'criterion': trial.suggest_categorical('criterion', ['squared_error', 'absolute_error', 'friedman_mse', 'poisson'])
        }

        # 创建模型
        rf = RandomForestRegressor(
            n_estimators=param['n_estimators'],
            max_depth=param['max_depth'],
            min_samples_split=param['min_samples_split'],
            min_samples_leaf=param['min_samples_leaf'],
            max_features=param['max_features'],
            bootstrap=param['bootstrap'],
            criterion=param['criterion'],
            random_state=42,
            n_jobs=-1
        )

        try:
            # 使用交叉验证评估模型性能
            score = cross_val_score(
                rf, X_train, y_train,
                cv=5,
                scoring='neg_mean_squared_error',
                n_jobs=-1
            ).mean()
        except ValueError as e:
            # 如果模型配置有误，返回一个极差的分数
            return float('inf')

        # 因为我们要最小化 RMSE，所以返回负 MSE
        return score
    
    # 调整 Optuna 的日志级别，抑制每个 trial 的日志输出
    optuna.logging.set_verbosity(optuna.logging.WARNING)

    # 使用 TPE 采样器进行优化
    sampler = TPESampler(seed=42)
    study = optuna.create_study(direction='maximize', sampler=sampler)
    study.optimize(rf_objective, n_trials=n_trials)

    # 获取最佳参数
    best_params = study.best_params

    # 训练最终模型
    best_rf = RandomForestRegressor(
        n_estimators=best_params['n_estimators'],
        max_depth=best_params['max_depth'],
        min_samples_split=best_params['min_samples_split'],
        min_samples_leaf=best_params['min_samples_leaf'],
        max_features=best_params['max_features'],
        bootstrap=best_params['bootstrap'],
        criterion=best_params['criterion'],
        random_state=42,
        n_jobs=-1
    )

    best_rf.fit(X_train, y_train)

    # 在测试集上进行预测
    y_pred = best_rf.predict(X_test)

    # 输出最佳超参数和性能指标
    print("Optuna 最佳超参数:", best_params)
    print("随机森林 R²:", r2_score(y_test, y_pred))
    print("随机森林 RMSE:", np.sqrt(mean_squared_error(y_test, y_pred)))

    return best_rf, y_pred


def explain_shap_rf(model, X_train, X_test, feature_names, plot_type="bar", save_path=None):
    """
    使用 SHAP 解释随机森林回归模型。

    参数:
    - model: 训练好的随机森林回归模型
    - X_train: 训练集特征
    - X_test: 测试集特征
    - feature_names: 特征名称列表
    - plot_type: SHAP 可视化类型，默认为 "bar"

    返回:
    - shap_values: 计算得到的 SHAP 值
    """
    
    # 如果 X_train 不是 DataFrame，将其转换为 DataFrame 并添加列名
    if not isinstance(X_train, pd.DataFrame):
        X_train = pd.DataFrame(X_train, columns=feature_names)
    if not isinstance(X_test, pd.DataFrame):
        X_test = pd.DataFrame(X_test, columns=feature_names)


    # 初始化 SHAP 解释器
    explainer = shap.TreeExplainer(model)
    
    # 计算 SHAP 值
    # shap_values_train = explainer.shap_values(X_train)
    # shap_values_test = explainer.shap_values(X_test)
    shap_values_train = explainer(X_train)
    shap_values_test = explainer(X_test)
    
    # 可视化特征重要性（全局解释）
    if plot_type == "bar":
        shap.summary_plot(shap_values_train, X_train, feature_names=feature_names, plot_type="bar", show=False)
    else:
        shap.summary_plot(shap_values_train, X_train, feature_names=feature_names)
    
    # 可视化单个预测的 SHAP 值（局部解释）
    # 例如，选择测试集中的第一个样本
    sample_index = 0
    # shap.plots.waterfall(shap_values_train[sample_index], max_display=20)
    
    # 保存图像
    if save_path:
        plt.savefig(save_path, bbox_inches='tight')
    
    # 清除当前图像，以防止后续图像叠加
    plt.clf()
    
    return shap_values_test


import xgboost as xgb

def train_xgboost(X_train, y_train, X_test, y_test, n_iter=50):
    """
    训练 XGBoost 回归模型，并评估其性能。

    参数：
    - X_train, y_train: 训练集特征和目标变量
    - X_test, y_test: 测试集特征和目标变量
    - n_iter: 随机搜索迭代次数
    - n_estimators, learning_rate, max_depth, random_state: 模型超参数
    ` baseline为n_estimators=100, learning_rate=0.1, max_depth=6, random_state=42
    xgb_reg = xgb.XGBRegressor(
        n_estimators=n_estimators,
        learning_rate=learning_rate,
        max_depth=max_depth,
        random_state=random_state,
        n_jobs=-1
    )

    返回：
    - xgb_reg: 训练好的 XGBoost 模型
    - y_pred: 测试集预测值
    """
    # 定义参数分布
    param_dist = {
        'n_estimators': [int(x) for x in np.linspace(50, 300, num=6)],
        'learning_rate': [0.01, 0.05, 0.1, 0.2],
        'max_depth': [3, 5, 7, 9],
        'subsample': [0.6, 0.8, 1.0],
        'colsample_bytree': [0.6, 0.8, 1.0],
        'gamma': [0, 0.1, 0.2, 0.3],
        'reg_alpha': [0, 0.01, 0.1],
        'reg_lambda': [1, 1.5, 2]
    }

    xgb_reg = xgb.XGBRegressor(
        objective='reg:squarederror',
        random_state=42,
        n_jobs=-1,
        gpu_id = 0,
    )

    # 使用 RandomizedSearchCV 进行超参数搜索
    random_search = RandomizedSearchCV(
        estimator=xgb_reg,
        param_distributions=param_dist,
        n_iter=n_iter,
        cv=5,
        scoring='neg_mean_squared_error',
        n_jobs=-1,
        random_state=42,
        verbose=1
    )

     # 训练模型
    random_search.fit(X_train, y_train)

    # 获取最佳模型
    best_xgb = random_search.best_estimator_

    # 在测试集上进行预测
    y_pred = best_xgb.predict(X_test)

    # 输出最佳超参数和性能指标
    print("xgboost 最佳超参数:", random_search.best_params_)
    # print("训练集最优得分 (MSE):", -random_search.best_score_)
    print("xgboost R²:", r2_score(y_test, y_pred))
    print("xgboost RMSE:", np.sqrt(mean_squared_error(y_test, y_pred)))

    return best_xgb, y_pred

import optuna
from optuna.samplers import TPESampler
import xgboost as xgb
from sklearn.metrics import mean_squared_error, r2_score

def train_xgboost_optuna(X_train, y_train, X_test, y_test, n_trials=50):
    """
    使用 Optuna 进行 XGBoost 回归模型的超参数调优，并评估其性能。

    参数：
    - X_train, y_train: 训练集特征和目标变量
    - X_test, y_test: 测试集特征和目标变量
    - n_trials: 试验次数，即优化过程中的迭代次数

    返回：
    - best_model: 调优后的最佳 XGBoost 模型
    - y_pred: 测试集预测值
    """
    def xg_objective(trial):
        # 定义要调优的超参数空间
        param = {
            'objective': 'reg:squarederror',
            'random_state': 42,
            'n_jobs': -1,
            # 'gpu_id': 0,
            # 'tree_method': 'gpu_hist',
            'n_estimators': trial.suggest_int('n_estimators', 50, 300),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
            'max_depth': trial.suggest_int('max_depth', 3, 10),
            'subsample': trial.suggest_float('subsample', 0.6, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
            'gamma': trial.suggest_float('gamma', 0, 0.5),
            'reg_alpha': trial.suggest_float('reg_alpha', 0, 0.1),
            'reg_lambda': trial.suggest_float('reg_lambda', 1, 2),
            'device':'cuda',
            'early_stopping_rounds':10,
        }

        # 创建模型并进行交叉验证
        model = xgb.XGBRegressor(**param)
        model.fit(X_train, y_train, eval_set=[(X_test, y_test)], verbose=False)
        preds = model.predict(X_test)
        rmse = mean_squared_error(y_test, preds, squared=False)
        return rmse

    # 调整 Optuna 的日志级别，抑制每个 trial 的日志输出
    optuna.logging.set_verbosity(optuna.logging.WARNING)

    # 使用 TPE 采样器进行优化
    sampler = TPESampler(seed=42)
    study = optuna.create_study(direction='minimize', sampler=sampler)
    study.optimize(xg_objective, n_trials=n_trials)

    # 获取最佳参数并训练最终模型
    best_params = study.best_params
    best_params['objective'] = 'reg:squarederror'
    best_params['random_state'] = 42
    best_params['n_jobs'] = -1
    best_params['gpu_id'] = 0
    best_params['tree_method'] = 'gpu_hist'

    best_model = xgb.XGBRegressor(**best_params)
    best_model.fit(X_train, y_train)

    # 在测试集上进行预测
    y_pred = best_model.predict(X_test)

    # 输出最佳超参数和性能指标
    print("Optuna 最佳超参数:", best_params)
    print("xgboost R²:", r2_score(y_test, y_pred))
    print("xgboost RMSE:", mean_squared_error(y_test, y_pred, squared=False))

    return best_model, y_pred

def explain_shap_xgb(model, X_train, X_test, feature_names, save_path, depvar):
    
    """
    使用 SHAP 解释 XGBoost 模型。

    参数：
    - model: 训练好的 XGBoost 模型
    - model_predict: 训练好的 XGBoost 模型预测结果
    - X_train: 训练集特征（用于构建 SHAP 解释器）
    - X_test: 测试集特征（用于计算 SHAP 值）
    - feature_names: 特征名称列表
    - plot_type: SHAP 图类型（"bar" 或 "summary"）

    返回：
    - shap_values: 计算得到的 SHAP 值
    """

    # 如果 X_train 不是 DataFrame，将其转换为 DataFrame 并添加列名
    if not isinstance(X_train, pd.DataFrame):
        X_train = pd.DataFrame(X_train, columns=feature_names)
    if not isinstance(X_test, pd.DataFrame):
        X_test = pd.DataFrame(X_test, columns=feature_names)

    # 初始化 SHAP 解释器
    explainer = shap.Explainer(model, X_train)
    shap_values = explainer(X_test)

    # 可视化特征重要性（全局解释）
    shap.summary_plot(shap_values, features=X_test, feature_names=feature_names, plot_type="bar", show=False)
    # 获取当前的坐标轴
    ax = plt.gca()
    # 设置横纵坐标标签及其字体大小
    ax.xaxis.label.set_size(16)  # 设置 X 轴标签的字体大小
    ax.yaxis.label.set_size(16)  # 设置 Y 轴标签的字体大小
    # 设置坐标轴刻度标签的字体大小
    ax.tick_params(axis='both', which='major', labelsize=16)
    # 保存图像
    plt.savefig(save_path+ f'XGB_{depvar}_contributor.pdf', bbox_inches='tight')
    plt.clf()

    shap.plots.beeswarm(shap_values, max_display = None, show=False)
    # 获取当前的坐标轴
    ax = plt.gca()
    # 设置横纵坐标标签及其字体大小
    ax.xaxis.label.set_size(16)  # 设置 X 轴标签的字体大小
    ax.yaxis.label.set_size(16)  # 设置 Y 轴标签的字体大小
    # 设置坐标轴刻度标签的字体大小
    ax.tick_params(axis='both', which='major', labelsize=16)

    colorbar = plt.gcf().axes[-1]
    colorbar.tick_params(labelsize=16)
    if colorbar.get_ylabel():
       colorbar.set_ylabel(colorbar.get_ylabel(), fontsize=16)
    if colorbar.get_title():
       colorbar.set_title(colorbar.get_title(), fontsize=16)

    plt.savefig(save_path + f'XGB_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.clf()

    # 绘制SHAP依赖图
    os.makedirs(save_path + f'XGB_dependence/{depvar}/', exist_ok=True)
    for i, feature in enumerate(feature_names):
        # shap.dependence_plot(feature, shap_values.values, X_test, interaction_index=None, show=False)
        shap.plots.scatter(shap_values[:, feature], show=False)
        # 获取当前的坐标轴
        ax = plt.gca()
        # 设置横纵坐标标签及其字体大小
        ax.xaxis.label.set_size(18)  # 设置 X 轴标签的字体大小
        ax.yaxis.label.set_size(18)  # 设置 Y 轴标签的字体大小
        # 设置坐标轴刻度标签的字体大小
        ax.tick_params(axis='both', which='major', labelsize=18)
        # 添加 SHAP=0 的横线
        plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
        plt.savefig(save_path + f'XGB_dependence/{depvar}/XGB_{depvar}_{feature}_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()

        # make a standard partial dependence plot with a single SHAP value overlaid
        shap.partial_dependence_plot(
            feature,
            model.predict,
            X_test,
            model_expected_value=True,
            feature_expected_value=True,
            show=False,
            ice=False,
        )
        # 获取当前的坐标轴
        ax = plt.gca()
        # 设置横纵坐标标签及其字体大小
        ax.xaxis.label.set_size(18)  # 设置 X 轴标签的字体大小
        ax.yaxis.label.set_size(18)  # 设置 Y 轴标签的字体大小
        # 设置坐标轴刻度标签的字体大小
        ax.tick_params(axis='both', which='major', labelsize=18)
        # plt.savefig(f'../caspt_plot/Rate_SHAP_XGB_scatter/{depvar}/XGB_{depvar}_{feature}_partial_dependence_plot.pdf', bbox_inches='tight')
        plt.savefig(save_path + f'XGB_dependence/{depvar}/XGB_{depvar}_{feature}_partial_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()

    # 可视化单个预测的 SHAP 值（局部解释）
    # 例如，选择测试集中的第一个样本
    #nsample_index = 0
    # shap.plots.waterfall(shap_values[sample_index], max_display=20)

    return shap_values

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import r2_score, mean_squared_error
import optuna

class PyTorchRegressor(nn.Module):
    def __init__(self, input_dim, hidden_dims, dropout_rate=None, use_batch_norm=False):
        super(PyTorchRegressor, self).__init__()
        layers = []
        last_dim = input_dim
        for dim in hidden_dims:
            layers.append(nn.Linear(last_dim, dim))
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(dim))
            layers.append(nn.ReLU())
            if dropout_rate is not None:
                layers.append(nn.Dropout(dropout_rate))
            last_dim = dim
        layers.append(nn.Linear(last_dim, 1))
        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)
    
    def predict(self, X):
           self.eval()  # Set the model to evaluation mode
           with torch.no_grad():
               if isinstance(X, np.ndarray):
                   X = torch.from_numpy(X).float()
               elif isinstance(X, pd.DataFrame):
                   X = torch.from_numpy(X.values).float()
               predictions = self.forward(X)
               return predictions.numpy().flatten()
    
def dnn_objective(trial, X_train_tensor, y_train_tensor, X_test_tensor, y_test_tensor, input_dim, device):
    # 定义需要优化的超参数
    hidden_layer_sizes = trial.suggest_categorical('hidden_layer_sizes', [
        [64, 32],
        [128, 64],
        [256, 128, 64],
        [512, 256, 128, 64]
    ])
    learning_rate = trial.suggest_loguniform('learning_rate', 1e-4, 1e-2)
    batch_size = trial.suggest_categorical('batch_size', [16, 32, 64, 128, 256])
    dropout_rate = trial.suggest_uniform('dropout_rate', 0.2, 0.5)  # 新增 Dropout 率
    use_batch_norm = trial.suggest_categorical('use_batch_norm', [True, False])  # 是否使用 BatchNorm
    weight_decay = trial.suggest_loguniform('weight_decay', 1e-5, 1e-2)  # 新增权重衰减
    epochs = 100
    patience = 10

    # 创建数据加载器
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    # train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, pin_memory=True)

    # 初始化模型、损失函数和优化器
    model = PyTorchRegressor(input_dim, hidden_layer_sizes, dropout_rate if trial.suggest_categorical('use_dropout', [True, False]) else None, use_batch_norm).to(device)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)

    # 训练模型
    best_loss = float('inf')
    epochs_no_improve = 0

    for epoch in range(epochs):
        model.train()
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()

        # 验证集损失
        model.eval()
        with torch.no_grad():
            val_outputs = model(X_test_tensor)
            val_loss = criterion(val_outputs, y_test_tensor).item()

        # 早停
        if val_loss < best_loss:
            best_loss = val_loss
            epochs_no_improve = 0
            best_model_state = model.state_dict()
        else:
            epochs_no_improve += 1
            if epochs_no_improve >= patience:
                break

        # 向 Optuna 报告中间结果，并检查是否需要剪枝
        trial.report(val_loss, epoch)

        if trial.should_prune():
            raise optuna.exceptions.TrialPruned()

    # 加载最佳模型
    model.load_state_dict(best_model_state)

    # 计算验证集上的指标
    model.eval()
    with torch.no_grad():
        y_pred_tensor = model(X_test_tensor)
    y_pred = y_pred_tensor.cpu().numpy().flatten()
    val_rmse = np.sqrt(mean_squared_error(y_test_tensor.cpu().numpy(), y_pred))

    # 返回验证集上的 RMSE（目标是最小化该值）
    return val_rmse
    
def train_neural_network_pytorch_optimized(X_train_scaled, y_train, X_test_scaled, y_test, 
                                           input_dim, device='cpu', n_trials=50):
    """
    使用 Optuna 自动调参训练 PyTorch 神经网络回归模型，并评估其性能。
    """
    # 转换为 PyTorch 张量
    X_train_tensor = torch.tensor(X_train_scaled, dtype=torch.float32).to(device)
    y_train_tensor = torch.tensor(y_train, dtype=torch.float32).view(-1, 1).to(device)
    X_test_tensor = torch.tensor(X_test_scaled, dtype=torch.float32).to(device)
    y_test_tensor = torch.tensor(y_test, dtype=torch.float32).view(-1, 1).to(device)

    # 调整 Optuna 的日志级别，抑制每个 trial 的日志输出
    optuna.logging.set_verbosity(optuna.logging.WARNING)

    # 创建优化器
    study = optuna.create_study(direction='minimize')

    # 定义目标函数，传递所有必要的参数
    func = lambda trial: dnn_objective(trial, X_train_tensor, y_train_tensor, X_test_tensor, y_test_tensor, input_dim, device)

    # 启动优化过程
    study.optimize(func, n_trials=n_trials, timeout=None)

    print("DNN 最佳超参数:", study.best_params)
    print("验证集最优 RMSE:", study.best_value)

    # 使用最佳超参数重新训练模型
    best_params = study.best_params
    hidden_layer_sizes = best_params['hidden_layer_sizes']
    learning_rate = best_params['learning_rate']
    batch_size = best_params['batch_size']
    dropout_rate = best_params.get('dropout_rate', 0.0)
    use_batch_norm = best_params.get('use_batch_norm', False)
    weight_decay = best_params.get('weight_decay', 0.0)
    epochs = 100
    patience = 10

    # 重新创建数据加载器
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

    # 初始化模型、损失函数和优化器
    model = PyTorchRegressor(input_dim, hidden_layer_sizes, dropout_rate, use_batch_norm).to(device)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)

    # 训练模型
    best_loss = float('inf')
    epochs_no_improve = 0

    for epoch in range(epochs):
        model.train()
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()

        # 验证集损失
        model.eval()
        with torch.no_grad():
            val_outputs = model(X_test_tensor)
            val_loss = criterion(val_outputs, y_test_tensor).item()

        # 早停
        if val_loss < best_loss:
            best_loss = val_loss
            epochs_no_improve = 0
            best_model_state = model.state_dict()
        else:
            epochs_no_improve += 1
            if epochs_no_improve >= patience:
                break

    # 加载最佳模型
    model.load_state_dict(best_model_state)

    # 在测试集上进行预测
    model.eval()
    with torch.no_grad():
        y_pred_tensor = model(X_test_tensor)
    y_pred = y_pred_tensor.cpu().numpy().flatten()

    print("DNN R²:", r2_score(y_test, y_pred))
    print("DNN RMSE:", np.sqrt(mean_squared_error(y_test, y_pred)))

    return model, y_pred

def explain_shap_DNN(model, X_train_scaled, X_test_scaled, feature_names, device='cpu', save_path=None, depvar=None):
    """
    使用 SHAP 解释 PyTorch 神经网络模型，使用 GradientExplainer 提高效率。
    """
    # 将模型设置为评估模式
    model.eval()

    # 转换数据为 PyTorch 张量
    background = torch.tensor(X_train_scaled[:100], dtype=torch.float32).to(device)
    test_data = torch.tensor(X_test_scaled, dtype=torch.float32).to(device)

    # 使用 GradientExplainer
    explainer = shap.GradientExplainer(model, background)
    
    # 计算 SHAP 值
    shap_values = explainer.shap_values(test_data)
    
    print(type(shap_values))
    print(type(test_data))
    print(shap_values.shape)
    print("SHAP Values:", shap_values)

    # 检查并转换 shap_values 为 shap.Explanation 对象
    if not isinstance(shap_values, shap.Explanation):
        shap_values = shap.Explanation(
            values=shap_values,
            data=X_test_scaled,
            feature_names=feature_names
        )

    # 全局特征重要性
    shap.summary_plot(shap_values, features=X_test_scaled, feature_names=feature_names, plot_type="bar", show=False)
    plt.savefig(save_path, bbox_inches='tight')
    # plt.clf()

    shap.plots.beeswarm(shap_values, max_display = None, show=False)
    plt.savefig(f'../caspt_plot/DNN_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.clf()

    # 绘制SHAP依赖图
    os.makedirs(f'../caspt_plot/Rate_SHAP_DNN_scatter/{depvar}/', exist_ok=True)
    for i, feature in enumerate(feature_names):
        # shap.dependence_plot(feature, shap_values.values, X_test, interaction_index=None, show=False)
        shap.plots.scatter(shap_values[:, feature], show=False)
        # 添加 SHAP=0 的横线
        plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
        plt.savefig(f'../caspt_plot/Rate_SHAP_DNN_scatter/{depvar}/{feature}_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()

    return shap_values


def explain_shap_pytorch(model, X_train_scaled, X_test_scaled, feature_names, num_background=100, num_samples=10, device='cpu', save_path=None, depvar=None):
    """
    使用 SHAP 解释 PyTorch 神经网络模型。

    参数：
    - model: 训练好的 PyTorch 神经网络模型
    - X_train_scaled: 训练集特征（已标准化，用于构建 SHAP 解释器）
    - X_test_scaled: 测试集特征（已标准化，用于计算 SHAP 值）
    - feature_names: 特征名称列表
    - num_background: 用于背景数据的样本数量
    - num_samples: 需要计算 SHAP 值的测试样本数量
    - device: 训练设备（'cpu' 或 'cuda'）

    返回：
    - shap_values: 计算得到的 SHAP 值
    """
    # 将模型设置为评估模式
    model.eval()

    # 将模型转换为可用于 SHAP 的函数
    def model_predict(x):
        with torch.no_grad():
            x_tensor = torch.tensor(x, dtype=torch.float32).to(device)
            outputs = model(x_tensor).cpu().numpy()
        return outputs.flatten()

    # 选择背景数据
    # background = X_train_scaled[:num_background]
    background = X_train_scaled

    #* 初始化 SHAP KernelExplainer, 后续看看能否更换Explainer的种类
    # explainer = shap.KernelExplainer(model_predict, background)
    explainer = shap.Explainer(model_predict, background)

    # 计算 SHAP 值
    # shap_values = explainer.shap_values(X_test_scaled[:num_samples])
    shap_values = explainer.shap_values(X_test_scaled)
    
    # 检查并转换 shap_values 为 shap.Explanation 对象
    if not isinstance(shap_values, shap.Explanation):
        shap_values = shap.Explanation(
            values=shap_values,
            data=X_test_scaled,
            feature_names=feature_names
        )

    # 全局特征重要性
    shap.summary_plot(shap_values, features=X_test_scaled, feature_names=feature_names, plot_type="bar", show=False)
    plt.savefig(save_path, bbox_inches='tight')
    plt.clf()

    shap.plots.beeswarm(shap_values, max_display = None, show=False)
    plt.savefig(f'../caspt_plot/DNN_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.clf()

    # 绘制SHAP依赖图
    os.makedirs(f'../caspt_plot/Rate_SHAP_DNN_scatter/{depvar}/', exist_ok=True)
    for i, feature in enumerate(feature_names):
        # shap.dependence_plot(feature, shap_values.values, X_test, interaction_index=None, show=False)
        shap.plots.scatter(shap_values[:, feature], show=False)
        # 添加 SHAP=0 的横线
        plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
        plt.savefig(f'../caspt_plot/Rate_SHAP_DNN_scatter/{depvar}/{feature}_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()

        # make a standard partial dependence plot with a single SHAP value overlaid
        # shap.partial_dependence_plot(
        #     feature,
        #     lambda X: model(torch.tensor(X, dtype=torch.float32).to(device)),
        #     X_test_scaled,
        #     model_expected_value=True,
        #     feature_expected_value=True,
        #     show=False,
        #     ice=False,
        # )
        # plt.savefig(f'../caspt_plot/Rate_SHAP_DNN_scatter/{depvar}/{feature}_partial_dependence_plot.pdf', bbox_inches='tight')
        # plt.clf()

    # 可视化单个预测的 SHAP 值（局部解释）
    # 例如，选择测试集中的第一个样本
    # sample_index = 0
    # shap_values_sample = shap_values[sample_index]
    # X_sample = X_test_scaled[sample_index]

    # # 构建 SHAP Explanation 对象
    # explanation = shap.Explanation(
    #     values=shap_values_sample,
    #     base_values=explainer.expected_value,
    #     data=X_sample,
    #     feature_names=feature_names
    # )

    # shap.plots.waterfall(explanation, max_display=20)

    # 保存图像
    # if save_path:
    #     plt.savefig(save_path, bbox_inches='tight')

    # 清除当前图像，以防止后续图像叠加
    # plt.clf()

    return shap_values


from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

def evaluate_model(y_true, y_pred):
    """
    计算回归模型的评估指标。
    
    参数：
    - y_true: 真实值
    - y_pred: 预测值
    
    返回：
    - metrics_dict: 包含各评估指标的字典
    """
    metrics_dict = {
        'R2': r2_score(y_true, y_pred),
        'RMSE': mean_squared_error(y_true, y_pred, squared=False),
        'MAE': mean_absolute_error(y_true, y_pred),
        'MAPE': np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    }
    return metrics_dict

import lightgbm as lgb

def train_lightgbm_optuna(X_train, y_train, X_test, y_test, n_trials=50):
    
    # 调整 Optuna 的日志级别，抑制每个 trial 的日志输出
    optuna.logging.set_verbosity(optuna.logging.ERROR)

    # Define the objective function for Optuna
    def lgbm_objective(trial):
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'verbosity': -1,
            'boosting_type': 'gbdt',
            'lambda_l1': trial.suggest_loguniform('lambda_l1', 1e-8, 10.0),
            'lambda_l2': trial.suggest_loguniform('lambda_l2', 1e-8, 10.0),
            'num_leaves': trial.suggest_int('num_leaves', 2, 256),
            'feature_fraction': trial.suggest_uniform('feature_fraction', 0.4, 1.0),
            'bagging_fraction': trial.suggest_uniform('bagging_fraction', 0.4, 1.0),
            'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
            'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
            'device_type': 'gpu',
        }

        # Create LightGBM datasets
        lgb_train = lgb.Dataset(X_train, y_train)
        lgb_test = lgb.Dataset(X_test, y_test, reference=lgb_train)

        # Train the model with early stopping via callbacks
        gbm = lgb.train(params, lgb_train, valid_sets=[lgb_test],
                callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False), 
                           lgb.log_evaluation(False)])

        # Predict and compute RMSE
        preds = gbm.predict(X_test)
        rmse = mean_squared_error(y_test, preds, squared=False)
        return rmse
    
    # Optimize hyperparameters with Optuna
    study = optuna.create_study(direction='minimize')
    study.optimize(lgbm_objective, n_trials=n_trials)

    # print('Best RMSE: {}'.format(study.best_trial.value))
    print('lightGBM 最佳超参数: {}'.format(study.best_trial.params))

    # Retrain the model with the best hyperparameters
    best_params = study.best_trial.params
    best_params['objective'] = 'regression'
    best_params['metric'] = 'rmse'
    best_params['verbosity'] = -1
    best_params['boosting_type'] = 'gbdt'

    lgb_train = lgb.Dataset(X_train, y_train)
    lgb_test = lgb.Dataset(X_test, y_test, reference=lgb_train)

    gbm = lgb.train(best_params, lgb_train, valid_sets=[lgb_test],
                    callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False), 
                               lgb.log_evaluation(False)])
    preds = gbm.predict(X_test)

    # Compute RMSE and R² score
    rmse = mean_squared_error(y_test, preds, squared=False)
    r2 = r2_score(y_test, preds)
    # Display RMSE and R² score on the validation set
    print('lightGBM R²: {}'.format(r2))
    print('lightGBM RMSE: {}'.format(rmse))

    return gbm, preds


def explain_shap_lightgbm(model, X_train, X_test, feature_names, plot_type="bar", save_path=None, depvar=None):
    """
    使用 SHAP 解释LightGBM回归模型。

    参数:
    - model: 训练好的LightGBM回归模型
    - X_train: 训练集特征
    - X_test: 测试集特征
    - feature_names: 特征名称列表
    - plot_type: SHAP 可视化类型，默认为 "bar"

    返回:
    - shap_values: 计算得到的 SHAP 值
    """
    # 如果 X_train 不是 DataFrame，将其转换为 DataFrame 并添加列名
    if not isinstance(X_train, pd.DataFrame):
        X_train = pd.DataFrame(X_train, columns=feature_names)
    if not isinstance(X_test, pd.DataFrame):
        X_test = pd.DataFrame(X_test, columns=feature_names)

    # 初始化 SHAP 解释器
    explainer = shap.TreeExplainer(model)
    
    # 计算 SHAP 值
    # shap_values_train = explainer.shap_values(X_train)
    # shap_values_test = explainer.shap_values(X_test)
    # shap_values_train = explainer(X_train)
    shap_values_test = explainer(X_test)
    
    # 可视化特征重要性（全局解释）
    shap.summary_plot(shap_values_test, X_test, feature_names=feature_names, plot_type="bar", show=False)
    # plt.savefig(save_path , bbox_inches='tight')
    plt.savefig(save_path+ f'LGBM_{depvar}_contributor.pdf', bbox_inches='tight')
    plt.clf()

    shap.plots.beeswarm(shap_values_test, max_display = None, show=False)
    # plt.savefig(f'../caspt_plot/LGBM_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.savefig(save_path + f'LGBM_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.clf()

    # 绘制SHAP依赖图
    # os.makedirs(f'../caspt_plot/Rate_SHAP_LGBM_scatter/{depvar}/', exist_ok=True)
    os.makedirs(save_path + f'LGBM_dependence/{depvar}/', exist_ok=True)
    for i, feature in enumerate(feature_names):
        # shap.dependence_plot(feature, shap_values.values, X_test, interaction_index=None, show=False)
        shap.plots.scatter(shap_values_test[:, feature], show=False)
        # 添加 SHAP=0 的横线
        plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
        # plt.savefig(f'../caspt_plot/Rate_SHAP_LGBM_scatter/{depvar}/{feature}_dependence_plot.pdf', bbox_inches='tight')
        plt.savefig(save_path + f'LGBM_dependence/{depvar}/LGBM_{depvar}_{feature}_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()

        # make a standard partial dependence plot with a single SHAP value overlaid
        shap.partial_dependence_plot(
            feature,
            model.predict,
            X_test,
            model_expected_value=True,
            feature_expected_value=True,
            show=False,
            ice=False,
        )
        # plt.savefig(f'../caspt_plot/Rate_SHAP_LGBM_scatter/{depvar}/{feature}_partial_dependence_plot.pdf', bbox_inches='tight')
        plt.savefig(save_path + f'LGBM_dependence/{depvar}/LGBM_{depvar}_{feature}_partial_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()
    
    # 可视化单个预测的 SHAP 值（局部解释）
    # 例如，选择测试集中的第一个样本
    # sample_index = 0
    # shap.plots.waterfall(shap_values_train[sample_index], max_display=20)

    # 保存图像
    # if save_path:
    #     plt.savefig(save_path, bbox_inches='tight')

    # 清除当前图像，以防止后续图像叠加
    # plt.clf()
    
    return shap_values_test


def train_catboost(X_train, y_train, X_test, y_test, n_trials):
    
    """
    使用 Optuna 进行超参数优化，训练 CatBoost 回归模型，并评估其性能。

    参数：
    - X_train, y_train: 训练集特征和目标变量
    - X_test, y_test: 测试集特征和目标变量
    - n_trials: Optuna 的优化迭代次数

    返回：
    - best_model: 训练好的 CatBoost 模型
    - y_pred: 测试集预测值
    """
    import optuna
    from catboost import CatBoostRegressor
    from sklearn.metrics import mean_squared_error, r2_score
    # 调整 Optuna 的日志级别，抑制每个 trial 的日志输出
    optuna.logging.set_verbosity(optuna.logging.ERROR)

    def cat_objective(trial):
        params = {
            'iterations': trial.suggest_int('iterations', 100, 1000),
            'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.1, log=True),
            'depth': trial.suggest_int('depth', 1, 10),
            'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1e-8, 100.0, log=True),
            'bagging_temperature': trial.suggest_float('bagging_temperature', 0.0, 1.0),
            'random_strength': trial.suggest_float('random_strength', 0.0, 1.0),
            # 'subsample': trial.suggest_float('subsample', 0.05, 1.0),
            'colsample_bylevel': trial.suggest_float('colsample_bylevel', 0.05, 1.0),
            "min_data_in_leaf": trial.suggest_int("min_data_in_leaf", 10, 100),
            'loss_function': 'RMSE',
            'verbose': False,
            'random_seed': 42,
            # 'task_type': 'GPU',    # 使用 GPU 加速
            # 'devices': '0',        # 指定 GPU 设备（如果有多个 GPU，可以指定特定的 GPU）
        }

        model = CatBoostRegressor(**params)
        model.fit(X_train, y_train, eval_set=[(X_test, y_test)], early_stopping_rounds=100, verbose=False, use_best_model=True)
        preds = model.predict(X_test)
        rmse = mean_squared_error(y_test, preds, squared=False)
        
        trial.report(rmse, step=1)
        if trial.should_prune():
            raise optuna.exceptions.TrialPruned()
 
        return rmse

    # 创建 Optuna 的 study 对象
    study = optuna.create_study(direction='minimize', pruner=optuna.pruners.MedianPruner())
    study.optimize(cat_objective, n_trials = n_trials,  n_jobs=8)

    # 输出最佳超参数
    print('catboost 最佳超参数:', study.best_params)

    # 使用最佳超参数重新训练模型
    best_params = study.best_params
    best_params['loss_function'] = 'RMSE'
    best_params['verbose'] = False
    best_params['random_seed'] = 42

    best_model = CatBoostRegressor(**best_params)
    best_model.fit(X_train, y_train, eval_set=[(X_test, y_test)], early_stopping_rounds=50, verbose=False)

    # 在测试集上进行预测
    y_pred = best_model.predict(X_test)

    # 评估模型性能
    print("catboost R²:", r2_score(y_test, y_pred))
    print("catboost RMSE:", np.sqrt(mean_squared_error(y_test, y_pred)))

    return best_model, y_pred

def train_catboost_classification(X_train, y_train, X_test, y_test, n_trials):
    """
    使用 Optuna 进行超参数优化，训练 CatBoost 分类模型，并评估其性能。

    参数：
    - X_train, y_train: 训练集特征和目标变量
    - X_test, y_test: 测试集特征和目标变量
    - n_trials: Optuna 的优化迭代次数

    返回：
    - best_model: 训练好的 CatBoost 模型
    - y_pred: 测试集预测概率
    """
    import optuna
    from catboost import CatBoostClassifier
    from sklearn.metrics import accuracy_score, roc_auc_score, f1_score
    
    # 调整 Optuna 的日志级别，抑制每个 trial 的日志输出
    optuna.logging.set_verbosity(optuna.logging.ERROR)

    def cat_objective(trial):
        params = {
            'iterations': trial.suggest_int('iterations', 100, 1000),
            'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.1, log=True),
            'depth': trial.suggest_int('depth', 1, 10),
            'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1e-8, 100.0, log=True),
            'bagging_temperature': trial.suggest_float('bagging_temperature', 0.0, 1.0),
            'random_strength': trial.suggest_float('random_strength', 0.0, 1.0),
            'colsample_bylevel': trial.suggest_float('colsample_bylevel', 0.05, 1.0),
            "min_data_in_leaf": trial.suggest_int("min_data_in_leaf", 10, 100),
            'loss_function': 'Logloss',  # 修改为分类损失函数
            'verbose': False,
            'random_seed': 42,
            # 'task_type': 'GPU',    # 使用 GPU 加速
            # 'devices': '0',        # 指定 GPU 设备（如果有多个 GPU，可以指定特定的 GPU）
        }

        model = CatBoostClassifier(**params)
        model.fit(X_train, y_train, eval_set=[(X_test, y_test)], early_stopping_rounds=100, verbose=False, use_best_model=True)
        preds = model.predict_proba(X_test)[:, 1]  # 获取正类的概率
        auc = roc_auc_score(y_test, preds)  # 使用AUC作为优化目标
        
        trial.report(auc, step=1)
        if trial.should_prune():
            raise optuna.exceptions.TrialPruned()
 
        return -auc  # 因为Optuna默认最小化，所以返回负的AUC

    # 创建 Optuna 的 study 对象
    study = optuna.create_study(direction='minimize', pruner=optuna.pruners.MedianPruner())
    study.optimize(cat_objective, n_trials = n_trials,  n_jobs=8)

    # 输出最佳超参数
    print('catboost 最佳超参数:', study.best_params)

    # 使用最佳超参数重新训练模型
    best_params = study.best_params
    best_params['loss_function'] = 'Logloss'
    best_params['verbose'] = False
    best_params['random_seed'] = 42

    best_model = CatBoostClassifier(**best_params)
    best_model.fit(X_train, y_train, eval_set=[(X_test, y_test)], early_stopping_rounds=50, verbose=False)

    # 在测试集上进行预测
    y_pred = best_model.predict_proba(X_test)[:, 1]  # 获取正类的概率

    # 评估模型性能
    print("catboost AUC:", roc_auc_score(y_test, y_pred))
    print("catboost Accuracy:", accuracy_score(y_test, y_pred > 0.5))
    print("catboost F1 Score:", f1_score(y_test, y_pred > 0.5))

    return best_model, y_pred

def plot_feature_importance_donut(shap_values, feature_names, save_path=None, num_var=5):
    """
    绘制环形特征重要性图

    参数：
    - shap_values: SHAP 计算的特征贡献值（可以是绝对值求和后的贡献比例）
    - feature_names: 特征名称列表
    - save_path: 保存路径（可选）
    - num_var: 变量个数
    - colors: 前10特征及其他部分的颜色列表
    """

    colors=['#22AFE4', '#52A06C', '#94B1D9', '#225B66', '#E76A2A',
                '#F9C192', '#FEC111', '#D2817E', '#94191C', '#6F3A96', '#B1B2B6']
    # colors=colors_list[:num_var]
    
    # 提取 SHAP 值的数值部分
    shap_values_array = shap_values.values
    
    # 计算每个特征的平均绝对 SHAP 值
    feature_importance = np.abs(shap_values_array).mean(axis=0)
    feature_importance_percentage = feature_importance / feature_importance.sum() * 100

    
    #* 对特征按贡献率排序，取前10特征
    sorted_idx = np.argsort(feature_importance_percentage)[::-1]  # 从大到小排序
    top_10_idx = sorted_idx[:num_var]
    others_idx = sorted_idx[num_var:]

    # 重新整理特征名和重要性
    top_10_names = [feature_names[i] for i in top_10_idx]
    top_10_importance = feature_importance_percentage[top_10_idx]
    others_importance = feature_importance_percentage[others_idx].sum()  # 计算“Others”总贡献率

    # 合并前10和“Others”
    final_names = top_10_names + ['Others']
    final_importance = np.append(top_10_importance, others_importance)

    # 绘制环形图
    plt.figure(figsize=(6, 6))
    wedges, texts, autotexts = plt.pie(
        final_importance,
        # labels=final_names,
        labels=None,  # 不直接在环形图上显示标签
        autopct='%1.1f%%',  # 显示百分比
        startangle=90,
        pctdistance=1.15,  # 百分比文本距离
        colors=colors[:len(final_names)],  # 使用指定颜色
        wedgeprops=dict(width = 0.6, edgecolor = 'white', linewidth = 3),
        textprops={'fontsize': 18}  # 设置字体大小
    )

    # 添加图例
    plt.legend(
        handles=[plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=colors[i], markersize=10)
                 for i in range(len(final_names))],
        labels=final_names,
        loc='center right',  # 图例位置
        bbox_to_anchor=(1.75, 0.5),  # 图例位置稍微偏移
        edgecolor='black',     # 设置边框颜色为黑色
        fancybox=False, 
        fontsize=18
    )

    plt.savefig(save_path, bbox_inches='tight')
    plt.clf()

def explain_shap_catboost(model, X_train, X_test, feature_names, plot_type="bar", save_path=None, depvar=None, color_values=None, data_scaler=None):
    """
    使用 SHAP 解释 CatBoost 模型。

    参数：
    - model: 训练好的 CatBoost 模型
    - X_train: 训练集特征（用于构建 SHAP 解释器）
    - X_test: 测试集特征（用于计算 SHAP 值）
    - feature_names: 特征名称列表
    - plot_type: SHAP 图类型（"bar" 或 "summary"）

    返回：
    - shap_values: 计算得到的 SHAP 值
    """
    import shap
    # from gplearn.genetic import SymbolicRegressor
    # from gplearn.functions import make_function
    from sklearn.preprocessing import PolynomialFeatures
    from sklearn.linear_model import LinearRegression
    
    # 还原x为原始范围
    X_test_original = data_scaler.inverse_transform(X_test)
    
    # 如果 X_train 不是 DataFrame，将其转换为 DataFrame 并添加列名
    if not isinstance(X_train, pd.DataFrame):
        X_train = pd.DataFrame(X_train, columns=feature_names)
    if not isinstance(X_test, pd.DataFrame):
        X_test = pd.DataFrame(X_test, columns=feature_names)

    # 初始化 SHAP 解释器
    explainer = shap.TreeExplainer(model, X_train)
    shap_values = explainer(X_test)

    plt.rcParams.update({'font.size': 18})

    shap.summary_plot(shap_values, features=X_test, feature_names=feature_names, plot_type="bar", show=False)
    # plt.savefig(save_path, bbox_inches='tight')
    plt.savefig(save_path+ f'Cat_{depvar}_contributor.pdf', bbox_inches='tight')
    plt.clf()

    plot_feature_importance_donut(shap_values, feature_names, save_path+f'Cat_{depvar}_feature_importance_donut.pdf', model_name=depvar)

    # 调整图的大小
    plt.figure(figsize=(9, 6))
    shap.plots.beeswarm(shap_values, max_display = 10, show=False)
    # 再次调整字体大小
    plt.xticks(fontsize=18)
    plt.yticks(fontsize=18)
    plt.xlabel(plt.gca().get_xlabel(), fontsize=18)
    plt.ylabel(plt.gca().get_ylabel(), fontsize=18)
    
    # 获取颜色条对象并调整字体大小
    colorbar = plt.gcf().axes[-1]  # 获取颜色条轴对象（通常是最后一个轴）
    colorbar.tick_params(labelsize=18)  # 调整颜色条刻度字体大小
    colorbar.set_ylabel(colorbar.get_ylabel(), fontsize=18)  # 调整颜色条标签字体大小
    
    # plt.savefig(f'../caspt_plot/CAT_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.savefig(save_path + f'Cat_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.clf()

    # 绘制SHAP依赖图
    # os.makedirs(f'../caspt_plot/Rate_SHAP_CAT_scatter/{depvar}/', exist_ok=True)
    os.makedirs(save_path + f'Cat_dependence/{depvar}/', exist_ok=True)
    for i, feature in enumerate(feature_names):
        # shap.dependence_plot(feature, shap_values.values, X_test, interaction_index=None, show=False)
        shap.plots.scatter(shap_values[:, feature], 
                           alpha=0.4,
                           dot_size=10,
                        #    xmax=shap_values[:, feature].percentile(99),
                           show=False,
                           hist=False,
                           )
        """
        # 定义拟合曲线1
        # 定义符号回归模型
        est_gp = SymbolicRegressor(
            population_size=5000,
            generations=20,
            tournament_size=20,
            stopping_criteria=0.01,
            const_range=(0, 2),
            function_set=['add', 'sub', 'mul', 'div'],
            metric='mean absolute error',
            parsimony_coefficient=0.001,
            verbose=1,
            n_jobs=-1
        )

        # 拟合模型，重塑 X 为二维数组
        est_gp.fit(shap_values[:, feature].data.reshape(-1, 1), shap_values[:, feature].values)

        # 生成拟合曲线的数据点
        age_fit = np.linspace(shap_values[:, feature].data.min(), shap_values[:, feature].data.max(), 500).reshape(-1, 1)
        shap_fit = est_gp.predict(age_fit)
        plt.plot(age_fit, shap_fit, color='#ff461f', linewidth=1, linestyle='-',)
        
        # 定义拟合函数2
        def fit_polynomial(shapp_values, column, degree=4):
            X = shapp_values[:, column].data.reshape(-1, 1)
            y = shapp_values[:, column].values
            poly = PolynomialFeatures(degree=degree)
            X_poly = poly.fit_transform(X)
            model_predict = LinearRegression()
            model_predict.fit(X_poly, y)
            return model_predict, poly

        # 使用多项式回归进行拟合
        model_predict, poly = fit_polynomial(shap_values, feature, degree=4)

        # 生成拟合曲线的数据点
        X_fit = np.linspace(shap_values[:, feature].data.min(), shap_values[:, feature].data.max(), 500).reshape(-1, 1)
        X_fit_poly = poly.transform(X_fit)
        y_fit = model_predict.predict(X_fit_poly)
        """
        # 绘制拟合曲线
        # plt.plot(X_fit, y_fit, color='#16a951', linewidth=1, linestyle='-')
        
        # 获取当前的坐标轴
        ax = plt.gca()
        # 获取当前x轴的刻度位置（标准化后的值）
        scaled_ticks = ax.get_xticks()
        # 找到当前特征在特征列表中的索引
        feature_index = feature_names.index(feature)
        
        min_val = data_scaler.data_min_[feature_index]
        max_val = data_scaler.data_max_[feature_index]
        
        # 将标准化后的刻度位置反标准化为原始数据值
        original_ticks = scaled_ticks * (max_val - min_val) + min_val

        # 设置 x 轴范围，基于原始数据值并添加一定的缓冲区
        buffer = (scaled_ticks.max() - scaled_ticks.min()) * 0.05  # 5% 缓冲
        # plt.xlim(0, scaled_ticks.max() + buffer)
        
        # 设置新的刻度标签为原始数据值，并保留两位小数
        ax.set_xticks(scaled_ticks)
        ax.set_xticklabels([f"{tick:.0f}" for tick in original_ticks])
        plt.xlim(0, scaled_ticks.max() + buffer)

        # 将 x 轴限制在 0 到 1 之间
        # plt.xlim(original_ticks.min(), original_ticks.max())  
        # 添加网格线
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, color='gray')
        # 添加 SHAP=0 的横线
        plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
        
        # 再次调整字体大小
        plt.xticks(fontsize=18)
        plt.yticks(fontsize=18)
        plt.xlabel(plt.gca().get_xlabel(), fontsize=18)
        plt.ylabel(plt.gca().get_ylabel(), fontsize=18)
        
        # plt.savefig(f'../caspt_plot/Rate_SHAP_CAT_scatter/{depvar}/{feature}_dependence_plot.pdf', bbox_inches='tight')
        plt.savefig(save_path + f'Cat_dependence/{depvar}/Cat_{depvar}_{feature}_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()

        shap.plots.scatter(shap_values[:, feature], 
                           alpha=0.4,
                           dot_size=10,
                        #    xmax=shap_values[:, feature].percentile(99),
                           show=False,
                        #    color=shap_values,
                           color = color_values,
                           hist=False,
                           )
        # 获取当前的坐标轴
        ax = plt.gca()
        # 获取当前x轴的刻度位置（标准化后的值）
        scaled_ticks = ax.get_xticks()
        # 找到当前特征在特征列表中的索引
        feature_index = feature_names.index(feature)
        
        min_val = data_scaler.data_min_[feature_index]
        max_val = data_scaler.data_max_[feature_index]
        
        # 将标准化后的刻度位置反标准化为原始数据值
        original_ticks = scaled_ticks * (max_val - min_val) + min_val

        # 设置 x 轴范围，基于原始数据值并添加一定的缓冲区
        buffer = (scaled_ticks.max() - scaled_ticks.min()) * 0.05  # 5% 缓冲
        
        
        # 设置新的刻度标签为原始数据值，并保留两位小数
        ax.set_xticks(scaled_ticks)
        ax.set_xticklabels([f"{tick:.0f}" for tick in original_ticks])
        plt.xlim(0, scaled_ticks.max() + buffer)
        
        # 绘制拟合曲线
        # plt.plot(X_fit, y_fit, color='#16a951', linewidth=1, linestyle='-')
        # plt.xlim(0, 1)  # 将 x 轴限制在 0 到 1 之间
        # 添加网格线
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, color='lightgray')
        # 添加 SHAP=0 的横线
        plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
        
        # 再次调整字体大小
        plt.xticks(fontsize=18)
        plt.yticks(fontsize=18)
        plt.xlabel(plt.gca().get_xlabel(), fontsize=18)
        plt.ylabel(plt.gca().get_ylabel(), fontsize=18)
        
        # 获取颜色条对象并调整字体大小
        colorbar = plt.gcf().axes[-1]  # 获取颜色条轴对象（通常是最后一个轴）
        colorbar.tick_params(labelsize=18)  # 调整颜色条刻度字体大小
        colorbar.set_ylabel(colorbar.get_ylabel(), fontsize=18)  # 调整颜色条标签字体大小
        
        # plt.savefig(f'../caspt_plot/Rate_SHAP_CAT_scatter/{depvar}/{feature}_dependence_plot.pdf', bbox_inches='tight')
        plt.savefig(save_path + f'Cat_dependence/{depvar}/Cat_{depvar}_{feature}_dependence_plot_color.pdf', bbox_inches='tight')
        plt.clf()

        # make a standard partial dependence plot with a single SHAP value overlaid
        shap.partial_dependence_plot(
            feature,
            model.predict,
            X_test,
            model_expected_value=True,
            feature_expected_value=True,
            show=False,
            ice=False,
        )
        
        # 再次调整字体大小
        plt.xticks(fontsize=18)
        plt.yticks(fontsize=18)
        plt.xlabel(plt.gca().get_xlabel(), fontsize=18)
        plt.ylabel(plt.gca().get_ylabel(), fontsize=18)
        plt.xlim(0, 1)  # 将 x 轴限制在 0 到 1 之间
        
        # plt.savefig(f'../caspt_plot/Rate_SHAP_CAT_scatter/{depvar}/{feature}_partial_dependence_plot.pdf', bbox_inches='tight')
        plt.savefig(save_path + f'Cat_dependence/{depvar}/Cat_{depvar}_{feature}_partial_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()

    # 可视化单个预测的 SHAP 值（局部解释）
    # 例如，选择测试集中的第一个样本
    # sample_index = 0
    # shap.plots.waterfall(shap_values[sample_index], max_display=20)

    # 保存图像
    # if save_path:
    #     plt.savefig(save_path, bbox_inches='tight')

    # # 清除当前图像，以防止后续图像叠加
    # plt.clf()

    return shap_values

def explain_shap_catboost_new(model, X_train, X_test, feature_names, save_path=None, depvar=None, num_var=None):
    """
    使用 SHAP 解释 CatBoost 模型。

    参数：
    - model: 训练好的 CatBoost 模型
    - X_train: 训练集特征（用于构建 SHAP 解释器）
    - X_test: 测试集特征（用于计算 SHAP 值）
    - feature_names: 特征名称列表
    - plot_type: SHAP 图类型（"bar" 或 "summary"）

    返回：
    - shap_values: 计算得到的 SHAP 值
    """
    import shap

    plt.rcParams['font.family'] = 'Times New Roman'
    # 如果 X_train 不是 DataFrame，将其转换为 DataFrame 并添加列名
    if not isinstance(X_train, pd.DataFrame):
        X_train = pd.DataFrame(X_train, columns=feature_names)
    if not isinstance(X_test, pd.DataFrame):
        X_test = pd.DataFrame(X_test, columns=feature_names)

    # 初始化 SHAP 解释器
    explainer = shap.TreeExplainer(model, X_train)
    shap_values = explainer(X_test)

    plt.rcParams.update({'font.size': 18})

    # 计算每个特征的平均绝对SHAP值和95%置信区间
    import numpy as np
    from scipy import stats

    # 获取SHAP值的绝对值
    abs_shap_values = np.abs(shap_values.values)

    # 计算每个特征的平均绝对SHAP值
    mean_abs_shap = np.mean(abs_shap_values, axis=0)

    # 计算95%置信区间
    confidence_intervals = []
    for i in range(abs_shap_values.shape[1]):
        feature_shap = abs_shap_values[:, i]
        # 使用bootstrap方法计算置信区间
        n_bootstrap = 1000
        bootstrap_means = []
        n_samples = len(feature_shap)

        for _ in range(n_bootstrap):
            # 有放回抽样
            bootstrap_sample = np.random.choice(feature_shap, size=n_samples, replace=True)
            bootstrap_means.append(np.mean(bootstrap_sample))

        # 计算95%置信区间
        ci_lower = np.percentile(bootstrap_means, 2.5)
        ci_upper = np.percentile(bootstrap_means, 97.5)
        confidence_intervals.append((ci_lower, ci_upper))

    # 创建自定义的条形图，包含置信区间
    fig, ax = plt.subplots(figsize=(10, 8))

    # 按重要性排序
    sorted_indices = np.argsort(mean_abs_shap)[::-1]
    sorted_means = mean_abs_shap[sorted_indices]
    sorted_features = [feature_names[i] for i in sorted_indices]
    sorted_cis = [confidence_intervals[i] for i in sorted_indices]

    # 计算误差条的长度
    ci_lower_errors = [sorted_means[i] - sorted_cis[i][0] for i in range(len(sorted_means))]
    ci_upper_errors = [sorted_cis[i][1] - sorted_means[i] for i in range(len(sorted_means))]

    # 绘制条形图和误差条
    y_pos = np.arange(len(sorted_features))
    bars = ax.barh(y_pos, sorted_means, color='#1f77b4', alpha=0.7)

    # 添加置信区间误差条
    ax.errorbar(sorted_means, y_pos,
                xerr=[ci_lower_errors, ci_upper_errors],
                fmt='none', color='black', capsize=3, capthick=1)

    # 设置标签和标题
    ax.set_yticks(y_pos)
    ax.set_yticklabels(sorted_features)
    ax.set_xlabel('Mean |SHAP value| (95% CI)', fontsize=18)
    ax.set_title('Feature Importance with 95% Confidence Intervals', fontsize=18)

    # 调整字体大小
    ax.tick_params(axis='both', labelsize=16)

    # 添加网格
    ax.grid(True, axis='x', alpha=0.3)

    # 调整布局
    plt.tight_layout()

    # 保存图像
    plt.savefig(save_path + f'Cat_{depvar}_contributor_with_CI.pdf', bbox_inches='tight')
    plt.clf()

    # 同时保存原始的SHAP summary plot（不带置信区间）
    shap.summary_plot(shap_values, features=X_test, feature_names=feature_names, plot_type="bar", show=False)
    plt.savefig(save_path + f'Cat_{depvar}_contributor_original.pdf', bbox_inches='tight')
    plt.clf()

    plot_feature_importance_donut(shap_values, feature_names, save_path+f'Cat_{depvar}_feature_importance_donut.pdf', num_var)

    # 调整图的大小
    plt.figure(figsize=(12, 6))
    shap.plots.beeswarm(shap_values, max_display = 10, show=False)
    # 再次调整字体大小
    plt.xticks(fontsize=18)
    plt.yticks(fontsize=18)
    plt.xlabel(plt.gca().get_xlabel(), fontsize=18)
    plt.ylabel(plt.gca().get_ylabel(), fontsize=18)
    
    # 获取颜色条对象并调整字体大小
    colorbar = plt.gcf().axes[-1]  # 获取颜色条轴对象（通常是最后一个轴）
    colorbar.tick_params(labelsize=18)  # 调整颜色条刻度字体大小
    colorbar.set_ylabel(colorbar.get_ylabel(), fontsize=18)  # 调整颜色条标签字体大小
    
    # plt.savefig(f'../caspt_plot/CAT_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.savefig(save_path + f'Cat_{depvar}_beeswarm.pdf', bbox_inches='tight')
    plt.clf()

    # 绘制SHAP依赖图
    # os.makedirs(f'../caspt_plot/Rate_SHAP_CAT_scatter/{depvar}/', exist_ok=True)
    os.makedirs(save_path + f'Cat_dependence/{depvar}/', exist_ok=True)
    for i, feature in enumerate(feature_names):
        
        # 设置颜色
        # color_shap = 'tab:blue'
        # color_pd = 'tab:red'
        
        # 创建一个图形和主坐标轴
        fig, ax1 = plt.subplots(figsize=(8, 6))

        shap.plots.scatter(shap_values[:, feature], 
                           alpha=1,
                           dot_size=10,
                           show=False,
                           hist=False,
                           ax=ax1,
                           color= '#38a1db', 
                           )
        
        ax1.set_xlabel(f'{feature}', fontsize=20)
        ax1.set_ylabel('SHAP value', fontsize=20)
        
        ax1.tick_params(axis='x', labelsize=20)
        ax1.tick_params(axis='y', labelsize=20)
        
        # 添加网格线
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, color='lightgray')
        # 添加 SHAP=0 的横线
        plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
        
        # 5.2 创建第二个 y 轴用于绘制部分依赖图
        ax2 = ax1.twinx()

        # make a standard partial dependence plot with a single SHAP value overlaid
        shap.partial_dependence_plot(
            feature,
            model.predict,
            X_test,
            # model_expected_value=True,
            # feature_expected_value=True,
            show=False,
            ice=False,  
            hist=False,
            ax=ax2,
        )
        
        # 然后获取 ax2 上的所有线条
        pdp_lines = ax2.get_lines()

        # 如果只想改最后画上去的那一条线，可按索引 [-1]
        if pdp_lines:
            pdp_lines[-1].set_color('#f47983')     # 修改颜色
            pdp_lines[-1].set_linewidth(2)     # 修改线宽
                
        # 5) 设置右侧 y 轴标签
        ax2.set_ylabel('Partial dependence', fontsize=20)
        
        ax2.spines["right"].set_visible(True)  
        
        # 6) 将第二个 y 轴刻度与标签放在右边，并设置字体大小
        ax2.yaxis.set_label_position("right")  
        ax2.yaxis.set_ticks_position("right")
        ax2.tick_params(axis='y', labelsize=20)
        
        # 调整布局并显示图形
        plt.tight_layout()
        plt.savefig(save_path + f'Cat_dependence/{depvar}/Cat_{depvar}_{feature}_mixed_partial_dependence_plot.pdf', bbox_inches='tight')
        plt.clf()
        
    return shap_values        
        
                           

def plot_shap_pdp(model, X_train, X_test, feature_names, plot_type="bar", save_path=None, depvar=None, color_values=None, data_scaler=None):
    """
    使用 SHAP 解释 CatBoost 模型。

    参数：
    - model: 训练好的 CatBoost 模型
    - X_train: 训练集特征（用于构建 SHAP 解释器）
    - X_test: 测试集特征（用于计算 SHAP 值）
    - feature_names: 特征名称列表
    - plot_type: SHAP 图类型（"bar" 或 "summary"）
    """
    from sklearn.inspection import partial_dependence, PartialDependenceDisplay
    # 如果 X_train 不是 DataFrame，将其转换为 DataFrame 并添加列名
    if not isinstance(X_train, pd.DataFrame):
        X_train = pd.DataFrame(X_train, columns=feature_names)
    if not isinstance(X_test, pd.DataFrame):
        X_test = pd.DataFrame(X_test, columns=feature_names)

    # 初始化 SHAP 解释器
    explainer = shap.TreeExplainer(model, X_train)
    shap_values = explainer(X_test)

    for i, feature in enumerate(feature_names):
        
        # 设置颜色
        # color_shap = 'tab:blue'
        # color_pd = 'tab:red'
        
        # 创建一个图形和主坐标轴
        fig, ax1 = plt.subplots(figsize=(9, 6))

        shap.plots.scatter(shap_values[:, feature], 
                           alpha=0.4,
                           dot_size=10,
                           show=False,
                           hist=False,
                           ax=ax1,
                           )
        
        ax1.set_xlabel(f'{feature}', fontsize=18)
        ax1.set_ylabel('SHAP Value', fontsize=18)
        
        ax1.tick_params(axis='x', labelsize=18)
        ax1.tick_params(axis='y', labelsize=18)
        
        # 添加网格线
        plt.grid(True, which='both', linestyle='--', linewidth=0.5, color='gray')
        # 添加 SHAP=0 的横线
        plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
        
        # 5.2 创建第二个 y 轴用于绘制部分依赖图
        ax2 = ax1.twinx()

        # make a standard partial dependence plot with a single SHAP value overlaid
        shap.partial_dependence_plot(
            feature,
            model.predict,
            X_test,
            # model_expected_value=True,
            # feature_expected_value=True,
            show=False,
            ice=False,  
            hist=False,
            ax=ax2,
        )
        
        # 然后获取 ax2 上的所有线条
        pdp_lines = ax2.get_lines()

        # 如果只想改最后画上去的那一条线，可按索引 [-1]
        if pdp_lines:
            pdp_lines[-1].set_color('#f47983')     # 修改颜色
            pdp_lines[-1].set_linewidth(2)     # 修改线宽
                
        # 5) 设置右侧 y 轴标签
        ax2.set_ylabel('Partial Dependence', fontsize=18)
        
        ax2.spines["right"].set_visible(True)  
        
        # 6) 将第二个 y 轴刻度与标签放在右边，并设置字体大小
        ax2.yaxis.set_label_position("right")  
        ax2.yaxis.set_ticks_position("right")
        ax2.tick_params(axis='y', labelsize=18)
        
        
        # 获取当前的坐标轴
        current_axes = plt.gca()
        # 获取当前x轴的刻度位置（标准化后的值）
        scaled_ticks = current_axes.get_xticks()
        # 找到当前特征在特征列表中的索引
        feature_index = feature_names.index(feature)
        
        min_val = data_scaler.data_min_[feature_index]
        max_val = data_scaler.data_max_[feature_index]
        
        # 将标准化后的刻度位置反标准化为原始数据值
        original_ticks = scaled_ticks * (max_val - min_val) + min_val

        # 设置 x 轴范围，基于原始数据值并添加一定的缓冲区
        buffer = (scaled_ticks.max() - scaled_ticks.min()) * 0.05  # 5% 缓冲
        # plt.xlim(0, scaled_ticks.max() + buffer)
        
        # 设置新的刻度标签为原始数据值，并保留两位小数
        ax2.set_xticks(scaled_ticks)
        ax2.set_xticklabels([f"{tick:.0f}" for tick in original_ticks])
        plt.xlim(0, scaled_ticks.max() + buffer)
        # plt.xlim(0, 1)  # 将 x 轴限制在 0 到 1 之间
        
        # 调整布局并显示图形
        plt.tight_layout()
        plt.show()

    return shap_values