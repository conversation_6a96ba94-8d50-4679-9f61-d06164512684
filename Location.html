<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="f40ed3704847460aaee7ac8a5dd4fcc4" class="chart-container" style="width:800px; height:400px; "></div>
    <script>
        var chart_f40ed3704847460aaee7ac8a5dd4fcc4 = echarts.init(
            document.getElementById('f40ed3704847460aaee7ac8a5dd4fcc4'), 'white', {renderer: 'canvas'});
        var option_f40ed3704847460aaee7ac8a5dd4fcc4 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "sankey",
            "data": [
                {
                    "name": "Fatal Accidents",
                    "itemStyle": {
                        "color": "#FFBBBB"
                    }
                },
                {
                    "name": "Injury Accidents",
                    "itemStyle": {
                        "color": "#BFBCDA"
                    }
                },
                {
                    "name": "Property Damage Only",
                    "itemStyle": {
                        "color": "#99DAEE"
                    }
                },
                {
                    "name": "Non-motorized lane"
                },
                {
                    "name": "Straight road"
                },
                {
                    "name": "Curved road"
                },
                {
                    "name": "Intersection"
                },
                {
                    "name": "Sidewalk"
                }
            ],
            "links": [
                {
                    "source": "Fatal Accidents",
                    "target": "Non-motorized lane",
                    "value": 2
                },
                {
                    "source": "Fatal Accidents",
                    "target": "Straight road",
                    "value": 5
                },
                {
                    "source": "Injury Accidents",
                    "target": "Curved road",
                    "value": 4
                },
                {
                    "source": "Injury Accidents",
                    "target": "Intersection",
                    "value": 4
                },
                {
                    "source": "Injury Accidents",
                    "target": "Non-motorized lane",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "Straight road",
                    "value": 2
                },
                {
                    "source": "Property Damage Only",
                    "target": "Curved road",
                    "value": 10
                },
                {
                    "source": "Property Damage Only",
                    "target": "Intersection",
                    "value": 27
                },
                {
                    "source": "Property Damage Only",
                    "target": "Non-motorized lane",
                    "value": 3
                },
                {
                    "source": "Property Damage Only",
                    "target": "Sidewalk",
                    "value": 5
                },
                {
                    "source": "Property Damage Only",
                    "target": "Straight road",
                    "value": 11
                }
            ],
            "left": "5%",
            "top": "5%",
            "right": "20%",
            "bottom": "5%",
            "nodeWidth": 20,
            "nodeGap": 10,
            "nodeAlign": "justify",
            "orient": "horizontal",
            "draggable": true,
            "label": {
                "show": true,
                "position": "right",
                "color": "black",
                "margin": 8,
                "fontSize": 14,
                "fontFamily": "Times New Roman"
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.3,
                "curveness": 0.5,
                "type": "solid",
                "color": "source"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {}
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    }
};
        chart_f40ed3704847460aaee7ac8a5dd4fcc4.setOption(option_f40ed3704847460aaee7ac8a5dd4fcc4);
    </script>
</body>
</html>
