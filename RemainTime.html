<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="4ff80b0279b849c0abd91515a51f2987" class="chart-container" style="width:800px; height:400px; "></div>
    <script>
        var chart_4ff80b0279b849c0abd91515a51f2987 = echarts.init(
            document.getElementById('4ff80b0279b849c0abd91515a51f2987'), 'white', {renderer: 'canvas'});
        var option_4ff80b0279b849c0abd91515a51f2987 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "sankey",
            "data": [
                {
                    "name": "Fatal Accidents",
                    "itemStyle": {
                        "color": "#FFBBBB"
                    }
                },
                {
                    "name": "Injury Accidents",
                    "itemStyle": {
                        "color": "#BFBCDA"
                    }
                },
                {
                    "name": "Property Damage Only",
                    "itemStyle": {
                        "color": "#99DAEE"
                    }
                },
                {
                    "name": "5 minutes or above"
                },
                {
                    "name": "below 5 minutes"
                }
            ],
            "links": [
                {
                    "source": "Fatal Accidents",
                    "target": "5 minutes or above",
                    "value": 1
                },
                {
                    "source": "Fatal Accidents",
                    "target": "below 5 minutes",
                    "value": 6
                },
                {
                    "source": "Injury Accidents",
                    "target": "5 minutes or above",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "below 5 minutes",
                    "value": 10
                },
                {
                    "source": "Property Damage Only",
                    "target": "5 minutes or above",
                    "value": 11
                },
                {
                    "source": "Property Damage Only",
                    "target": "below 5 minutes",
                    "value": 45
                }
            ],
            "left": "5%",
            "top": "5%",
            "right": "20%",
            "bottom": "5%",
            "nodeWidth": 20,
            "nodeGap": 10,
            "nodeAlign": "justify",
            "orient": "horizontal",
            "draggable": true,
            "label": {
                "show": true,
                "position": "right",
                "color": "black",
                "margin": 8,
                "fontSize": 14,
                "fontFamily": "Times New Roman"
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.3,
                "curveness": 0.5,
                "type": "solid",
                "color": "source"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {}
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    }
};
        chart_4ff80b0279b849c0abd91515a51f2987.setOption(option_4ff80b0279b849c0abd91515a51f2987);
    </script>
</body>
</html>
