<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="c7396b093c304558a3b4a47261ff8e45" class="chart-container" style="width:800px; height:400px; "></div>
    <script>
        var chart_c7396b093c304558a3b4a47261ff8e45 = echarts.init(
            document.getElementById('c7396b093c304558a3b4a47261ff8e45'), 'white', {renderer: 'canvas'});
        var option_c7396b093c304558a3b4a47261ff8e45 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "sankey",
            "data": [
                {
                    "name": "Fatal Accidents",
                    "itemStyle": {
                        "color": "#FFBBBB"
                    }
                },
                {
                    "name": "Injury Accidents",
                    "itemStyle": {
                        "color": "#BFBCDA"
                    }
                },
                {
                    "name": "Property Damage Only",
                    "itemStyle": {
                        "color": "#99DAEE"
                    }
                },
                {
                    "name": "Afternoon 13:30-17:00"
                },
                {
                    "name": "Evening peak 17:00-19:30"
                },
                {
                    "name": "Night 19:30-24:00"
                },
                {
                    "name": "lunch time 10:30-13:30"
                },
                {
                    "name": "Morning peak 7:00-10:30"
                },
                {
                    "name": "Midnight 0:00-7:00"
                }
            ],
            "links": [
                {
                    "source": "Fatal Accidents",
                    "target": "Afternoon 13:30-17:00",
                    "value": 2
                },
                {
                    "source": "Fatal Accidents",
                    "target": "Evening peak 17:00-19:30",
                    "value": 3
                },
                {
                    "source": "Fatal Accidents",
                    "target": "Night 19:30-24:00",
                    "value": 1
                },
                {
                    "source": "Fatal Accidents",
                    "target": "lunch time 10:30-13:30",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "Afternoon 13:30-17:00",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "Evening peak 17:00-19:30",
                    "value": 5
                },
                {
                    "source": "Injury Accidents",
                    "target": "Morning peak 7:00-10:30",
                    "value": 5
                },
                {
                    "source": "Property Damage Only",
                    "target": "Afternoon 13:30-17:00",
                    "value": 7
                },
                {
                    "source": "Property Damage Only",
                    "target": "Evening peak 17:00-19:30",
                    "value": 23
                },
                {
                    "source": "Property Damage Only",
                    "target": "Midnight 0:00-7:00",
                    "value": 1
                },
                {
                    "source": "Property Damage Only",
                    "target": "Morning peak 7:00-10:30",
                    "value": 1
                },
                {
                    "source": "Property Damage Only",
                    "target": "Night 19:30-24:00",
                    "value": 5
                },
                {
                    "source": "Property Damage Only",
                    "target": "lunch time 10:30-13:30",
                    "value": 19
                }
            ],
            "left": "5%",
            "top": "5%",
            "right": "20%",
            "bottom": "5%",
            "nodeWidth": 20,
            "nodeGap": 10,
            "nodeAlign": "justify",
            "orient": "horizontal",
            "draggable": true,
            "label": {
                "show": true,
                "position": "right",
                "color": "black",
                "margin": 8,
                "fontSize": 14,
                "fontFamily": "Times New Roman"
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.3,
                "curveness": 0.5,
                "type": "solid",
                "color": "source"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {}
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    }
};
        chart_c7396b093c304558a3b4a47261ff8e45.setOption(option_c7396b093c304558a3b4a47261ff8e45);
    </script>
</body>
</html>
