<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="aa324fcc1464481dbadc7ede69508418" class="chart-container" style="width:800px; height:400px; "></div>
    <script>
        var chart_aa324fcc1464481dbadc7ede69508418 = echarts.init(
            document.getElementById('aa324fcc1464481dbadc7ede69508418'), 'white', {renderer: 'canvas'});
        var option_aa324fcc1464481dbadc7ede69508418 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "sankey",
            "data": [
                {
                    "name": "Fatal Accidents",
                    "itemStyle": {
                        "color": "#FFBBBB"
                    }
                },
                {
                    "name": "Injury Accidents",
                    "itemStyle": {
                        "color": "#BFBCDA"
                    }
                },
                {
                    "name": "Property Damage Only",
                    "itemStyle": {
                        "color": "#99DAEE"
                    }
                },
                {
                    "name": "3 orders or less"
                },
                {
                    "name": "4-6 orders"
                },
                {
                    "name": "7 orders or more"
                }
            ],
            "links": [
                {
                    "source": "Fatal Accidents",
                    "target": "3 orders or less",
                    "value": 3
                },
                {
                    "source": "Fatal Accidents",
                    "target": "4-6 orders",
                    "value": 3
                },
                {
                    "source": "Fatal Accidents",
                    "target": "7 orders or more",
                    "value": 1
                },
                {
                    "source": "Injury Accidents",
                    "target": "3 orders or less",
                    "value": 3
                },
                {
                    "source": "Injury Accidents",
                    "target": "4-6 orders",
                    "value": 6
                },
                {
                    "source": "Injury Accidents",
                    "target": "7 orders or more",
                    "value": 2
                },
                {
                    "source": "Property Damage Only",
                    "target": "3 orders or less",
                    "value": 20
                },
                {
                    "source": "Property Damage Only",
                    "target": "4-6 orders",
                    "value": 23
                },
                {
                    "source": "Property Damage Only",
                    "target": "7 orders or more",
                    "value": 13
                }
            ],
            "left": "5%",
            "top": "5%",
            "right": "20%",
            "bottom": "5%",
            "nodeWidth": 20,
            "nodeGap": 10,
            "nodeAlign": "justify",
            "orient": "horizontal",
            "draggable": true,
            "label": {
                "show": true,
                "position": "right",
                "color": "black",
                "margin": 8,
                "fontSize": 14,
                "fontFamily": "Times New Roman"
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.3,
                "curveness": 0.5,
                "type": "solid",
                "color": "source"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {}
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    }
};
        chart_aa324fcc1464481dbadc7ede69508418.setOption(option_aa324fcc1464481dbadc7ede69508418);
    </script>
</body>
</html>
