{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pandas as pd\n", "import biogeme.database as db\n", "import biogeme.biogeme_logging as blog\n", "from biogeme.models import piecewiseFormula\n", "import biogeme.biogeme as bio\n", "from biogeme.expressions import Beta, log, Elem, bioNormalCdf, Variable, bioDraws, MonteCarlo\n", "import shutil\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["df = pd.read_excel('new_412.xlsx')\n", "unreli_index = pd.read_excel('问题问卷编号.xlsx', header= None)\n", "# 筛选无效问卷 删除 回答时间小于2min且 Attitude1 和 Attitude5回答一致 的问卷\n", "df = df[df['QeTime'] >= 120]\n", "df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]\n", "\n", "database = db.Database('data', df)\n", "globals().update(database.variables)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#* variable definition\n", "# 场景属性自变量\n", "deliVolume = Variable('Volume')\n", "deliTime = Variable('Time')\n", "deliWeather = Variable('Weather')\n", "deliLocation = Variable('Location')\n", "RemainTime = Variable('RemainTime')\n", "CauseUnsafe = Variable('CauseUnsafe')\n", "\n", "# 因变量\n", "UnsafeAccident = Variable('UnsafeAccident')\n", "\n", "# 社会经济属性自变量\n", "gender = Variable('gender')\n", "age = Variable('age')\n", "education = Variable('education')\n", "marriage = Variable('marriage')\n", "family = Variable('family')\n", "citytime = Variable('citytime')\n", "monthincome = Variable('monthincome')\n", "disposableincome = Variable('disposableincome')\n", "\n", "# 配送属性自变量\n", "deliverytime = Variable('deliverytime')\n", "deliverymode = Variable('deliverymode')\n", "deliveryposition = Variable('deliveryposition')\n", "workingdays = Variable('workingdays')\n", "workinghours = Variable('workinghours')\n", "deliveryorders = Variable('deliveryorders')\n", "deliveryrange = Variable('deliveryrange')\n", "deliveryspeed = Variable('deliveryspeed')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 社会经济属性二分类变量\n", "gender_female = database.DefineVariable('gender_female', gender == 2)\n", "\n", "age_less_24 = database.DefineVariable('age_less_24', age <= 2)\n", "age_between_24_35 = database.DefineVariable('age_between_24_35', age == 3)\n", "age_more_35 = database.DefineVariable('age_more_35', age >= 4)\n", "\n", "edu_less_junior = database.DefineVariable('edu_less_junior', education <= 2)\n", "edu_more_uni = database.DefineVariable('edu_more_uni', education >= 4)\n", "\n", "marriage_not = database.DefineVariable('marriage_not', marriage <= 1)\n", "children = database.DefineVariable('children', ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0))\n", "\n", "family_small = database.DefineVariable('family_small', family <= 2)\n", "family_middle = database.DefineVariable('family_middle', (family <= 4) + (family >= 3) > 0)\n", "family_big = database.DefineVariable('family_big', (family >= 5) &  (family <= 7))\n", "\n", "citytime_less_3 = database.DefineVariable('citytime_less_3', citytime <= 2)\n", "citytime_more_6 = database.DefineVariable('citytime_more_6', citytime >= 4)\n", "citytime_local = database.DefineVariable('citytime_local', citytime == 5)\n", "\n", "monthincome_less_2000 = database.DefineVariable('monthincome_less_2000', monthincome <= 1)\n", "monthincome_less_4000 = database.DefineVariable('monthincome_less_4000', monthincome <= 2)\n", "monthincome_less_6000 = database.DefineVariable('monthincome_less_6000', monthincome <= 3)\n", "monthincome_more_8000 = database.DefineVariable('monthincome_more_8000', monthincome >= 5)\n", "monthincome_4000_8000 = database.DefineVariable('monthincome_4000_8000', (monthincome >= 3) & (monthincome <= 4))\n", "\n", "disposableincome_less_5000 = database.DefineVariable('disposableincome_less_5000', disposableincome <= 1)\n", "disposableincome_more_10000 = database.DefineVariable('disposableincome_more_10000', disposableincome >= 3)\n", "\n", "# 场景属性二分类变量\n", "deliVolume_low = database.DefineVariable('deliVolume_low', deliVolume <= 1)\n", "deliVolume_middle = database.DefineVariable('deliVolume_middle', deliVolume == 2)\n", "deliVolume_high = database.DefineVariable('deliVolume_high', deliVolume >= 3)\n", "\n", "deliTime_morning = database.DefineVariable('deliTime_morning', deliTime == 3)\n", "deliTime_noon = database.DefineVariable('deliTime_noon', deliTime == 1)\n", "deliTime_evening = database.DefineVariable('deliTime_evening', deliTime == 2)\n", "deliTime_afternoon = database.DefineVariable('deliTime_afternoon', deliTime == 4)\n", "deliTime_night = database.DefineVariable('deliTime_night', deliTime >= 5)\n", "\n", "deliWeather_heavy = database.DefineVariable('deliWeather_heavy', deliWeather == 1)\n", "deliWeather_good = database.DefineVariable('deliWeather_good', deliWeather == 2)\n", "deliWeather_spit = database.DefineVariable('deliWeather_spit', deliWeather == 3)\n", "\n", "deliLocation_inter = database.DefineVariable('deliWeather_inter', deliLocation == 1)\n", "deliLocation_straight = database.DefineVariable('deliLocation_straight', deliLocation == 2)\n", "deliLocation_curve = database.DefineVariable('deliLocation_curve', deliLocation == 3)\n", "deliLocation_pede = database.DefineVariable('deliLocation_pede', deliLocation == 5)\n", "\n", "RemainTime_short = database.DefineVariable('RemainTime_short', RemainTime <= 1)\n", "\n", "CauseUnsafe_overspeed = database.DefineVariable('CauseUnsafe_overspeed', CauseUnsafe == 1)\n", "CauseUnsafe_breakrule = database.DefineVariable('CauseUnsafe_breakrule', CauseUnsafe == 2)\n", "CauseUnsafe_drowsy = database.DefineVariable('CauseUnsafe_drowsy', CauseUnsafe == 3)\n", "CauseUnsafe_emergency = database.DefineVariable('CauseUnsafe_emergency', CauseUnsafe == 4)\n", "CauseUnsafe_phone = database.DefineVariable('CauseUnsafe_phone', CauseUnsafe == 5)\n", "CauseUnsafe_without = database.DefineVariable('CauseUnsafe_without', CauseUnsafe == 6)\n", "\n", "# 配送属性二分类变量\n", "deliverytime_brief = database.DefineVariable('deliverytime_brief', deliverytime == 1)\n", "deliverytime_short = database.DefineVariable('deliverytime_short', deliverytime <= 2)\n", "deliverytime_extended = database.DefineVariable('deliverytime_extended', deliverytime >= 3)\n", "deliverytime_prolonged = database.DefineVariable('deliverytime_prolonged', deliverytime  == 4)\n", "\n", "deliverymode_full = database.DefineVariable('deliverymode_full', deliverymode >= 2)\n", "\n", "deliveryposition_brief = database.DefineVariable('deliveryposition_brief', deliveryposition <= 1)\n", "deliveryposition_short = database.DefineVariable('deliveryposition_short', deliveryposition <= 2)\n", "deliveryposition_extended = database.DefineVariable('deliveryposition_extended', deliveryposition >= 3)\n", "deliveryposition_prolonged = database.DefineVariable('deliveryposition_prolonged', deliveryposition  >= 4)\n", "\n", "workingdays_brief = database.DefineVariable('workingdays_brief', workingdays <= 1)\n", "workingdays_short = database.DefineVariable('workingdays_short', workingdays <= 2)\n", "workingdays_standard = database.DefineVariable('workingdays_standard', workingdays <= 3)\n", "workingdays_extended = database.DefineVariable('workingdays_extended', workingdays  >= 5)\n", "workingdays_prolonged = database.DefineVariable('workingdays_prolonged', workingdays  >= 6)\n", "\n", "workinghours_brief = database.DefineVariable('workinghours_brief', workinghours <= 1)\n", "workinghours_short = database.DefineVariable('workinghours_short', workinghours <= 2)\n", "workinghours_standard = database.DefineVariable('workinghours_standard', workinghours >= 3)\n", "workinghours_extended = database.DefineVariable('workinghours_extended', workinghours >= 4)\n", "workinghours_prolonged = database.DefineVariable('workinghours_prolonged', workinghours >= 5)\n", "\n", "deliveryorders_brief = database.DefineVariable('deliveryorders_brief', deliveryorders <= 1)\n", "deliveryorders_short = database.DefineVariable('deliveryorders_short', deliveryorders <= 2)\n", "deliveryorders_standard = database.DefineVariable('deliveryorders_standard', deliveryorders <= 3)\n", "deliveryorders_extended = database.DefineVariable('deliveryorders_extended', deliveryorders >= 4)\n", "deliveryorders_prolonged = database.DefineVariable('deliveryorders_prolonged', deliveryorders >= 5)\n", "\n", "deliveryrange_brief = database.DefineVariable('deliveryrange_brief', deliveryrange <= 1)\n", "deliveryrange_short = database.DefineVariable('deliveryrange_short', deliveryrange <= 2)\n", "deliveryrange_standard = database.DefineVariable('deliveryrange_standard', deliveryrange <= 3)\n", "deliveryrange_extended = database.DefineVariable('deliveryrange_extended', deliveryrange >= 4)\n", "deliveryrange_prolonged = database.DefineVariable('deliveryrange_prolonged', deliveryrange >= 5)\n", "\n", "deliveryspeed_brief = database.DefineVariable('deliveryspeed_brief', deliveryspeed <= 1)\n", "deliveryspeed_short = database.DefineVariable('deliveryspeed_short', deliveryspeed <= 2)\n", "deliveryspeed_standard = database.DefineVariable('deliveryspeed_standard', deliveryspeed >= 3)\n", "#!\n", "deliveryspeed_extended = database.DefineVariable('deliveryspeed_extended', deliveryspeed == 4)\n", "deliveryspeed_prolonged = database.DefineVariable('deliveryspeed_prolonged', deliveryspeed == 5)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["#* structual coefficient\n", "# Attitude\n", "coef_intercept_Att = Beta('coef_intercept_Att', 0.0, None, None, 0)\n", "coef_gender_Att = Beta('coef_gender_Att', 0.0, None, None, 0)\n", "coef_age_Att = Beta('coef_age_Att', 0.0, None, None, 0)\n", "coef_edu_Att = Beta('coef_edu_Att', 0.0, None, None, 0)\n", "coef_marriage_Att = Beta('coef_marriage_Att', 0.0, None, None, 0)\n", "coef_children_Att = Beta('coef_children_Att', 0.0, None, None, 0)\n", "coef_family_Att = Beta('coef_family_Att', 0.0, None, None, 0)\n", "coef_citytime_Att = Beta('coef_citytime_Att', 0.0, None, None, 0)\n", "coef_monthincome_Att = Beta('coef_monthincome_Att', 0.0, None, None, 0)\n", "coef_disposableincome_Att = Beta('coef_disposableincome_Att', 0.0, None, None, 0)\n", "\n", "# Habit\n", "coef_intercept_Habit = Beta('coef_intercept_Habit', 0.0, None, None, 0)\n", "coef_gender_Habit = Beta('coef_gender_Habit', 0.0, None, None, 0)\n", "coef_age_Habit = Beta('coef_age_Habit', 0.0, None, None, 0)\n", "coef_edu_Habit = Beta('coef_edu_Habit', 0.0, None, None, 0)\n", "coef_marriage_Habit = Beta('coef_marriage_Habit', 0.0, None, None, 0)\n", "coef_children_Habit = Beta('coef_children_Habit', 0.0, None, None, 0)\n", "coef_family_Habit = Beta('coef_family_Habit', 0.0, None, None, 0)\n", "coef_citytime_Habit = Beta('coef_citytime_Habit', 0.0, None, None, 0)\n", "coef_monthincome_Habit = Beta('coef_monthincome_Habit', 0.0, None, None, 0)\n", "coef_disposableincome_Habit = Beta('coef_disposableincome_Habit', 0.0, None, None, 0)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["#* measurement coefficient\n", "INTER_Att2 = Beta('INTER_Att2', 0, None, None, 1)\n", "INTER_Att3 = Beta('INTER_Att3', 0, None, None, 0)\n", "INTER_Att4 = Beta('INTER_Att4', 0, None, None, 0)\n", "INTER_Att5 = Beta('INTER_Att5', 0, None, None, 0)\n", "INTER_Att6 = Beta('INTER_Att6', 0, None, None, 0)\n", "\n", "B_Att2 = Beta('B_Att2', 1, None, None, 1)\n", "B_Att3 = Beta('B_Att3', 1, None, None, 0)\n", "B_Att4 = Beta('B_Att4', 1, None, None, 0)\n", "B_Att5 = Beta('B_Att5', 1, None, None, 0)\n", "B_Att6 = Beta('B_Att6', 1, None, None, 0)\n", "\n", "SIGMA_Att2 = Beta('SIGMA_Att2', 1, 1.0e-5, None, 1)\n", "SIGMA_Att3 = Beta('SIGMA_Att3', 1, 1.0e-5, None, 0)\n", "SIGMA_Att4 = Beta('SIGMA_Att4', 1, 1.0e-5, None, 0)\n", "SIGMA_Att5 = Beta('SIGMA_Att5', 1, 1.0e-5, None, 0)\n", "SIGMA_Att6 = Beta('SIGMA_Att6', 1, 1.0e-5, None, 0)\n", "\n", "INTER_Habit_1 = Beta('INTER_Habit_1', 0, None, None, 1)\n", "INTER_Habit_2 = Beta('INTER_Habit_2', 0, None, None, 0)\n", "INTER_Habit_3 = Beta('INTER_Habit_3', 0, None, None, 0)\n", "INTER_Habit_4 = Beta('INTER_Habit_4', 0, None, None, 0)\n", "INTER_Habit_5 = Beta('INTER_Habit_5', 0, None, None, 0)\n", "\n", "B_Habit1 = Beta('B_Habit1', 1, None, None, 1)\n", "B_Habit2 = Beta('B_Habit2', 1, None, None, 0)\n", "B_Habit3 = Beta('B_Habit3', 1, None, None, 0)\n", "B_Habit4 = Beta('B_Habit4', 1, None, None, 0)\n", "B_Habit5 = Beta('B_Habit5', 1, None, None, 0)\n", "\n", "SIGMA_Habit1 = Beta('SIGMA_Habit1', 1, 1.0e-5, None, 1)\n", "SIGMA_Habit2 = Beta('SIGMA_Habit2', 1, 1.0e-5, None, 0)\n", "SIGMA_Habit3 = Beta('SIGMA_Habit3', 1, 1.0e-5, None, 0)\n", "SIGMA_Habit4 = Beta('SIGMA_Habit4', 1, 1.0e-5, None, 0)\n", "SIGMA_Habit5 = Beta('SIGMA_Habit5', 1, 1.0e-5, None, 0)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["#* structual equations\n", "\n", "Att = coef_intercept_Att + \\\n", "    coef_age_Att  * age_less_24 + \\\n", "    coef_gender_Att  * gender_female + \\\n", "    coef_edu_Att * edu_less_junior + \\\n", "    coef_marriage_Att * marriage_not + \\\n", "    coef_children_Att * children + \\\n", "    coef_family_Att * family_big + \\\n", "    coef_monthincome_Att * monthincome_less_4000 + \\\n", "    coef_disposableincome_Att * disposableincome_more_10000\n", "\n", "MODEL_Att2 = INTER_Att2 + B_Att2 * Att\n", "MODEL_Att3 = INTER_Att3 + B_Att3 * Att\n", "MODEL_Att4 = INTER_Att4 + B_Att4 * Att\n", "MODEL_Att5 = INTER_Att5 + B_Att5 * Att\n", "MODEL_Att6 = INTER_Att6 + B_Att6 * Att\n", "\n", "\n", "Habit = coef_intercept_Habit + \\\n", "    coef_gender_Habit  * gender_female + \\\n", "    coef_age_Habit  * age_less_24 + \\\n", "    coef_edu_Habit * edu_less_junior + \\\n", "    coef_marriage_Habit * marriage_not + \\\n", "    coef_children_Habit * children + \\\n", "    coef_family_Habit * family_big + \\\n", "    coef_monthincome_Habit * monthincome_less_4000 + \\\n", "    coef_disposableincome_Habit * disposableincome_more_10000 \n", "\n", "MODEL_Habit1 = INTER_Habit_1 + B_Habit1 * Habit\n", "MODEL_Habit2 = INTER_Habit_2 + B_Habit2 * Habit\n", "MODEL_Habit3 = INTER_Habit_3 + B_Habit3 * Habit\n", "MODEL_Habit4 = INTER_Habit_4 + B_Habit4 * Habit\n", "MODEL_Habit5 = INTER_Habit_5 + B_Habit5 * Habit\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# As the measurements are using a Likert scale with M = 5 levels, we deﬁne 4 parameters\n", "delta_1 = Beta('delta_1', 0.1, 1.0e-5, None, 0)\n", "delta_2 = Beta('delta_2', 0.2, 1.0e-5, None, 0)\n", "tau_1 = -delta_1 - delta_2\n", "tau_2 = -delta_1\n", "tau_3 = delta_1\n", "tau_4 = delta_1 + delta_2\n", "\n", "#* measurement equations\n", "Att2_tau_1 = (tau_1 - MODEL_Att2) / SIGMA_Att2\n", "Att2_tau_2 = (tau_2 - MODEL_Att2) / SIGMA_Att2\n", "Att2_tau_3 = (tau_3 - MODEL_Att2) / SIGMA_Att2\n", "Att2_tau_4 = (tau_4 - MODEL_Att2) / SIGMA_Att2\n", "IndiAtt2 = {\n", "    1: bioNormalCdf(Att2_tau_1),\n", "    2: bioNormalCdf(Att2_tau_2) - bioNormalCdf(Att2_tau_1),\n", "    3: bioNormalCdf(Att2_tau_3) - bioNormalCdf(Att2_tau_2),\n", "    4: bioNormalCdf(Att2_tau_4) - bioNormalCdf(Att2_tau_3),\n", "    5: 1 - bioNormalCdf(Att2_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att2 = Elem(IndiAtt2, Attitude2)\n", "\n", "Att3_tau_1 = (tau_1 - MODEL_Att3) / SIGMA_Att3\n", "Att3_tau_2 = (tau_2 - MODEL_Att3) / SIGMA_Att3\n", "Att3_tau_3 = (tau_3 - MODEL_Att3) / SIGMA_Att3\n", "Att3_tau_4 = (tau_4 - MODEL_Att3) / SIGMA_Att3\n", "IndiAtt3 = {\n", "    1: bioNormalCdf(Att3_tau_1),\n", "    2: bioNormalCdf(Att3_tau_2) - bioNormalCdf(Att3_tau_1),\n", "    3: bioNormalCdf(Att3_tau_3) - bioNormalCdf(Att3_tau_2),\n", "    4: bioNormalCdf(Att3_tau_4) - bioNormalCdf(Att3_tau_3),\n", "    5: 1 - bioNormalCdf(Att3_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att3 = Elem(IndiAtt3, Attitude3)\n", "\n", "Att4_tau_1 = (tau_1 - MODEL_Att4) / SIGMA_Att4\n", "Att4_tau_2 = (tau_2 - MODEL_Att4) / SIGMA_Att4\n", "Att4_tau_3 = (tau_3 - MODEL_Att4) / SIGMA_Att4\n", "Att4_tau_4 = (tau_4 - MODEL_Att4) / SIGMA_Att4\n", "IndiAtt4 = {\n", "    1: bioNormalCdf(Att4_tau_1),\n", "    2: bioNormalCdf(Att4_tau_2) - bioNormalCdf(Att4_tau_1),\n", "    3: bioNormalCdf(Att4_tau_3) - bioNormalCdf(Att4_tau_2),\n", "    4: bioNormalCdf(Att4_tau_4) - bioNormalCdf(Att4_tau_3),\n", "    5: 1 - bioNormalCdf(Att4_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att4 = Elem(IndiAtt4, Attitude4)\n", "\n", "Att5_tau_1 = (tau_1 - MODEL_Att5) / SIGMA_Att5\n", "Att5_tau_2 = (tau_2 - MODEL_Att5) / SIGMA_Att5\n", "Att5_tau_3 = (tau_3 - MODEL_Att5) / SIGMA_Att5\n", "Att5_tau_4 = (tau_4 - MODEL_Att5) / SIGMA_Att5\n", "IndiAtt5 = {\n", "    1: bioNormalCdf(Att5_tau_1),\n", "    2: bioNormalCdf(Att5_tau_2) - bioNormalCdf(Att5_tau_1),\n", "    3: bioNormalCdf(Att5_tau_3) - bioNormalCdf(Att5_tau_2),\n", "    4: bioNormalCdf(Att5_tau_4) - bioNormalCdf(Att5_tau_3),\n", "    5: 1 - bioNormalCdf(Att5_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att5 = Elem(IndiAtt5, Attitude5)\n", "\n", "Att6_tau_1 = (tau_1 - MODEL_Att6) / SIGMA_Att6\n", "Att6_tau_2 = (tau_2 - MODEL_Att6) / SIGMA_Att6\n", "Att6_tau_3 = (tau_3 - MODEL_Att6) / SIGMA_Att6\n", "Att6_tau_4 = (tau_4 - MODEL_Att6) / SIGMA_Att6\n", "IndiAtt6 = {\n", "    1: bioNormalCdf(Att6_tau_1),\n", "    2: bioNormalCdf(Att6_tau_2) - bioNormalCdf(Att6_tau_1),\n", "    3: bioNormalCdf(Att6_tau_3) - bioNormalCdf(Att6_tau_2),\n", "    4: bioNormalCdf(Att6_tau_4) - bioNormalCdf(Att6_tau_3),\n", "    5: 1 - bioNormalCdf(Att6_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att6 = Elem(IndiAtt6, Attitude6)\n", "\n", "Habit1_tau_1 = (tau_1 - MODEL_Habit1) / SIGMA_Habit1\n", "Habit1_tau_2 = (tau_2 - MODEL_Habit1) / SIGMA_Habit1\n", "Habit1_tau_3 = (tau_3 - MODEL_Habit1) / SIGMA_Habit1\n", "Habit1_tau_4 = (tau_4 - MODEL_Habit1) / SIGMA_Habit1\n", "IndiHabit1 = {\n", "    1: bioNormalCdf(Habit1_tau_1),\n", "    2: bioNormalCdf(Habit1_tau_2) - bioNormalCdf(Habit1_tau_1),\n", "    3: bioNormalCdf(Habit1_tau_3) - bioNormalCdf(Habit1_tau_2),\n", "    4: bioNormalCdf(Habit1_tau_4) - bioNormalCdf(Habit1_tau_3),\n", "    5: 1 - bioNormalCdf(Habit1_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit1 = Elem(IndiHabit1, Habit1)\n", "\n", "Habit2_tau_1 = (tau_1 - MODEL_Habit2) / SIGMA_Habit2\n", "Habit2_tau_2 = (tau_2 - MODEL_Habit2) / SIGMA_Habit2\n", "Habit2_tau_3 = (tau_3 - MODEL_Habit2) / SIGMA_Habit2\n", "Habit2_tau_4 = (tau_4 - MODEL_Habit2) / SIGMA_Habit2\n", "IndiHabit2 = {\n", "    1: bioNormalCdf(Habit2_tau_1),\n", "    2: bioNormalCdf(Habit2_tau_2) - bioNormalCdf(Habit2_tau_1),\n", "    3: bioNormalCdf(Habit2_tau_3) - bioNormalCdf(Habit2_tau_2),\n", "    4: bioNormalCdf(Habit2_tau_4) - bioNormalCdf(Habit2_tau_3),\n", "    5: 1 - bioNormalCdf(Habit2_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit2 = Elem(IndiHabit2, Habit2)\n", "\n", "Habit3_tau_1 = (tau_1 - MODEL_Habit3) / SIGMA_Habit3\n", "Habit3_tau_2 = (tau_2 - MODEL_Habit3) / SIGMA_Habit3\n", "Habit3_tau_3 = (tau_3 - MODEL_Habit3) / SIGMA_Habit3\n", "Habit3_tau_4 = (tau_4 - MODEL_Habit3) / SIGMA_Habit3\n", "IndiHabit3 = {\n", "    1: bioNormalCdf(Habit3_tau_1),\n", "    2: bioNormalCdf(Habit3_tau_2) - bioNormalCdf(Habit3_tau_1),\n", "    3: bioNormalCdf(Habit3_tau_3) - bioNormalCdf(Habit3_tau_2),\n", "    4: bioNormalCdf(Habit3_tau_4) - bioNormalCdf(Habit3_tau_3),\n", "    5: 1 - bioNormalCdf(Habit3_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit3 = Elem(IndiHabit3, Habit3)\n", "\n", "Habit4_tau_1 = (tau_1 - MODEL_Habit4) / SIGMA_Habit4\n", "Habit4_tau_2 = (tau_2 - MODEL_Habit4) / SIGMA_Habit4\n", "Habit4_tau_3 = (tau_3 - MODEL_Habit4) / SIGMA_Habit4\n", "Habit4_tau_4 = (tau_4 - MODEL_Habit4) / SIGMA_Habit4\n", "IndiHabit4 = {\n", "    1: bioNormalCdf(Habit4_tau_1),\n", "    2: bioNormalCdf(Habit4_tau_2) - bioNormalCdf(Habit4_tau_1),\n", "    3: bioNormalCdf(Habit4_tau_3) - bioNormalCdf(Habit4_tau_2),\n", "    4: bioNormalCdf(Habit4_tau_4) - bioNormalCdf(Habit4_tau_3),\n", "    5: 1 - bioNormalCdf(Habit4_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit4 = Elem(IndiHabit4, Habit4)\n", "\n", "Habit5_tau_1 = (tau_1 - MODEL_Habit5) / SIGMA_Habit5\n", "Habit5_tau_2 = (tau_2 - MODEL_Habit5) / SIGMA_Habit5\n", "Habit5_tau_3 = (tau_3 - MODEL_Habit5) / SIGMA_Habit5\n", "Habit5_tau_4 = (tau_4 - MODEL_Habit5) / SIGMA_Habit5\n", "IndiHabit5 = {\n", "    1: bioNormalCdf(Habit5_tau_1),\n", "    2: bioNormalCdf(Habit5_tau_2) - bioNormalCdf(Habit5_tau_1),\n", "    3: bioNormalCdf(Habit5_tau_3) - bioNormalCdf(Habit5_tau_2),\n", "    4: bioNormalCdf(Habit5_tau_4) - bioNormalCdf(Habit5_tau_3),\n", "    5: 1 - bioNormalCdf(Habit5_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit5 = Elem(IndiHabit5, Habit5)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["loglike = (\n", "    log(P_Att2)\n", "    + log(P_Att3)\n", "    + log(P_Att4)\n", "    + log(P_Att5)\n", "    + log(P_Att6)\n", "    + log(P_Habit1)\n", "    + log(P_Habit2)\n", "    + log(P_Habit3)\n", "    + log(P_Habit4)\n", "    + log(P_Habit5)\n", ")\n", "\n", "the_biogeme = bio.BIOGEME(database, loglike)\n", "the_biogeme.modelName = 'accident_iclv_measure'\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimated betas: 44\n", "final log likelihood: -3943.356\n", "Output file: accident_iclv_measure.html\n", "LaTeX file: accident_iclv_measure.tex\n"]}], "source": ["results = the_biogeme.estimate()\n", "\n", "print(f'Estimated betas: {len(results.data.betaValues)}')\n", "print(f'final log likelihood: {results.data.logLike:.3f}')\n", "print(f'Output file: {results.data.htmlFileName}')\n", "results.writeLaTeX()\n", "print(f'LaTeX file: {results.data.latexFileName}')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Value</th>\n", "      <th><PERSON><PERSON>d err</th>\n", "      <th>Rob. t-test</th>\n", "      <th>Rob. p-value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>B_Att3</th>\n", "      <td>1.024287</td>\n", "      <td>0.174540</td>\n", "      <td>5.868507</td>\n", "      <td>4.397373e-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B_Att4</th>\n", "      <td>0.943782</td>\n", "      <td>0.161463</td>\n", "      <td>5.845189</td>\n", "      <td>5.059921e-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B_Att5</th>\n", "      <td>0.905213</td>\n", "      <td>0.153209</td>\n", "      <td>5.908349</td>\n", "      <td>3.455530e-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B_Att6</th>\n", "      <td>0.662463</td>\n", "      <td>0.133809</td>\n", "      <td>4.950808</td>\n", "      <td>7.390607e-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B_Habit2</th>\n", "      <td>0.649024</td>\n", "      <td>0.108938</td>\n", "      <td>5.957762</td>\n", "      <td>2.557162e-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B_Habit3</th>\n", "      <td>0.848572</td>\n", "      <td>0.162989</td>\n", "      <td>5.206314</td>\n", "      <td>1.926282e-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B_Habit4</th>\n", "      <td>0.866184</td>\n", "      <td>0.185163</td>\n", "      <td>4.677960</td>\n", "      <td>2.897432e-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B_Habit5</th>\n", "      <td>0.689440</td>\n", "      <td>0.209013</td>\n", "      <td>3.298546</td>\n", "      <td>9.718704e-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTER_Att3</th>\n", "      <td>-0.141064</td>\n", "      <td>0.101169</td>\n", "      <td>-1.394342</td>\n", "      <td>1.632143e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTER_Att4</th>\n", "      <td>-0.166013</td>\n", "      <td>0.100129</td>\n", "      <td>-1.657991</td>\n", "      <td>9.731924e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTER_Att5</th>\n", "      <td>-0.192014</td>\n", "      <td>0.095346</td>\n", "      <td>-2.013867</td>\n", "      <td>4.402349e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTER_Att6</th>\n", "      <td>-0.277584</td>\n", "      <td>0.080889</td>\n", "      <td>-3.431667</td>\n", "      <td>5.998846e-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTER_Habit_2</th>\n", "      <td>-0.367180</td>\n", "      <td>0.070526</td>\n", "      <td>-5.206321</td>\n", "      <td>1.926213e-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTER_Habit_3</th>\n", "      <td>0.149337</td>\n", "      <td>0.093705</td>\n", "      <td>1.593688</td>\n", "      <td>1.110058e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTER_Habit_4</th>\n", "      <td>0.202849</td>\n", "      <td>0.102537</td>\n", "      <td>1.978297</td>\n", "      <td>4.789519e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTER_Habit_5</th>\n", "      <td>0.352320</td>\n", "      <td>0.108145</td>\n", "      <td>3.257850</td>\n", "      <td>1.122599e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SIGMA_Att3</th>\n", "      <td>0.945287</td>\n", "      <td>0.051526</td>\n", "      <td>18.345678</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SIGMA_Att4</th>\n", "      <td>0.922628</td>\n", "      <td>0.047204</td>\n", "      <td>19.545383</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SIGMA_Att5</th>\n", "      <td>0.891877</td>\n", "      <td>0.053320</td>\n", "      <td>16.727012</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SIGMA_Att6</th>\n", "      <td>0.774909</td>\n", "      <td>0.041386</td>\n", "      <td>18.724041</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SIGMA_Habit2</th>\n", "      <td>0.840739</td>\n", "      <td>0.048969</td>\n", "      <td>17.168751</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SIGMA_Habit3</th>\n", "      <td>0.832871</td>\n", "      <td>0.044950</td>\n", "      <td>18.528986</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SIGMA_Habit4</th>\n", "      <td>0.824407</td>\n", "      <td>0.045834</td>\n", "      <td>17.986658</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SIGMA_Habit5</th>\n", "      <td>0.962127</td>\n", "      <td>0.057344</td>\n", "      <td>16.778032</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_age_Att</th>\n", "      <td>0.102631</td>\n", "      <td>0.153101</td>\n", "      <td>0.670350</td>\n", "      <td>5.026349e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_age_Habit</th>\n", "      <td>0.066952</td>\n", "      <td>0.156114</td>\n", "      <td>0.428867</td>\n", "      <td>6.680202e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_children_Att</th>\n", "      <td>0.496485</td>\n", "      <td>0.172498</td>\n", "      <td>2.878215</td>\n", "      <td>3.999319e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_children_Habit</th>\n", "      <td>0.418502</td>\n", "      <td>0.173371</td>\n", "      <td>2.413906</td>\n", "      <td>1.578255e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_disposableincome_Att</th>\n", "      <td>-0.174050</td>\n", "      <td>0.152537</td>\n", "      <td>-1.141036</td>\n", "      <td>2.538550e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_disposableincome_Habit</th>\n", "      <td>0.122744</td>\n", "      <td>0.151065</td>\n", "      <td>0.812527</td>\n", "      <td>4.164894e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_edu_Att</th>\n", "      <td>0.065893</td>\n", "      <td>0.159827</td>\n", "      <td>0.412276</td>\n", "      <td>6.801369e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_edu_Habit</th>\n", "      <td>0.071313</td>\n", "      <td>0.165667</td>\n", "      <td>0.430461</td>\n", "      <td>6.668602e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_family_Att</th>\n", "      <td>-0.402054</td>\n", "      <td>0.148924</td>\n", "      <td>-2.699728</td>\n", "      <td>6.939614e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_family_Habit</th>\n", "      <td>-0.036149</td>\n", "      <td>0.137499</td>\n", "      <td>-0.262907</td>\n", "      <td>7.926222e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_gender_Att</th>\n", "      <td>-0.496741</td>\n", "      <td>0.137795</td>\n", "      <td>-3.604936</td>\n", "      <td>3.122295e-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_gender_Habit</th>\n", "      <td>-0.598435</td>\n", "      <td>0.164279</td>\n", "      <td>-3.642790</td>\n", "      <td>2.696993e-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_intercept_Att</th>\n", "      <td>-0.683183</td>\n", "      <td>0.161793</td>\n", "      <td>-4.222583</td>\n", "      <td>2.415186e-05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_intercept_Habit</th>\n", "      <td>-0.655831</td>\n", "      <td>0.178193</td>\n", "      <td>-3.680454</td>\n", "      <td>2.328191e-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_marriage_Att</th>\n", "      <td>0.343941</td>\n", "      <td>0.207310</td>\n", "      <td>1.659067</td>\n", "      <td>9.710227e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_marriage_Habit</th>\n", "      <td>0.535935</td>\n", "      <td>0.183332</td>\n", "      <td>2.923298</td>\n", "      <td>3.463446e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_monthincome_Att</th>\n", "      <td>-0.006847</td>\n", "      <td>0.137462</td>\n", "      <td>-0.049807</td>\n", "      <td>9.602763e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_monthincome_Habit</th>\n", "      <td>-0.086175</td>\n", "      <td>0.137284</td>\n", "      <td>-0.627709</td>\n", "      <td>5.301945e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>delta_1</th>\n", "      <td>0.302924</td>\n", "      <td>0.021861</td>\n", "      <td>13.856697</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>delta_2</th>\n", "      <td>0.677961</td>\n", "      <td>0.038069</td>\n", "      <td>17.808535</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                Value  Rob. Std err  Rob. t-test  Rob. p-value\n", "B_Att3                       1.024287      0.174540     5.868507  4.397373e-09\n", "B_Att4                       0.943782      0.161463     5.845189  5.059921e-09\n", "B_Att5                       0.905213      0.153209     5.908349  3.455530e-09\n", "B_Att6                       0.662463      0.133809     4.950808  7.390607e-07\n", "B_Habit2                     0.649024      0.108938     5.957762  2.557162e-09\n", "B_Habit3                     0.848572      0.162989     5.206314  1.926282e-07\n", "B_Habit4                     0.866184      0.185163     4.677960  2.897432e-06\n", "B_Habit5                     0.689440      0.209013     3.298546  9.718704e-04\n", "INTER_Att3                  -0.141064      0.101169    -1.394342  1.632143e-01\n", "INTER_Att4                  -0.166013      0.100129    -1.657991  9.731924e-02\n", "INTER_Att5                  -0.192014      0.095346    -2.013867  4.402349e-02\n", "INTER_Att6                  -0.277584      0.080889    -3.431667  5.998846e-04\n", "INTER_Habit_2               -0.367180      0.070526    -5.206321  1.926213e-07\n", "INTER_Habit_3                0.149337      0.093705     1.593688  1.110058e-01\n", "INTER_Habit_4                0.202849      0.102537     1.978297  4.789519e-02\n", "INTER_Habit_5                0.352320      0.108145     3.257850  1.122599e-03\n", "SIGMA_Att3                   0.945287      0.051526    18.345678  0.000000e+00\n", "SIGMA_Att4                   0.922628      0.047204    19.545383  0.000000e+00\n", "SIGMA_Att5                   0.891877      0.053320    16.727012  0.000000e+00\n", "SIGMA_Att6                   0.774909      0.041386    18.724041  0.000000e+00\n", "SIGMA_Habit2                 0.840739      0.048969    17.168751  0.000000e+00\n", "SIGMA_Habit3                 0.832871      0.044950    18.528986  0.000000e+00\n", "SIGMA_Habit4                 0.824407      0.045834    17.986658  0.000000e+00\n", "SIGMA_Habit5                 0.962127      0.057344    16.778032  0.000000e+00\n", "coef_age_Att                 0.102631      0.153101     0.670350  5.026349e-01\n", "coef_age_Habit               0.066952      0.156114     0.428867  6.680202e-01\n", "coef_children_Att            0.496485      0.172498     2.878215  3.999319e-03\n", "coef_children_Habit          0.418502      0.173371     2.413906  1.578255e-02\n", "coef_disposableincome_Att   -0.174050      0.152537    -1.141036  2.538550e-01\n", "coef_disposableincome_Habit  0.122744      0.151065     0.812527  4.164894e-01\n", "coef_edu_Att                 0.065893      0.159827     0.412276  6.801369e-01\n", "coef_edu_Habit               0.071313      0.165667     0.430461  6.668602e-01\n", "coef_family_Att             -0.402054      0.148924    -2.699728  6.939614e-03\n", "coef_family_Habit           -0.036149      0.137499    -0.262907  7.926222e-01\n", "coef_gender_Att             -0.496741      0.137795    -3.604936  3.122295e-04\n", "coef_gender_Habit           -0.598435      0.164279    -3.642790  2.696993e-04\n", "coef_intercept_Att          -0.683183      0.161793    -4.222583  2.415186e-05\n", "coef_intercept_Habit        -0.655831      0.178193    -3.680454  2.328191e-04\n", "coef_marriage_Att            0.343941      0.207310     1.659067  9.710227e-02\n", "coef_marriage_Habit          0.535935      0.183332     2.923298  3.463446e-03\n", "coef_monthincome_Att        -0.006847      0.137462    -0.049807  9.602763e-01\n", "coef_monthincome_Habit      -0.086175      0.137284    -0.627709  5.301945e-01\n", "delta_1                      0.302924      0.021861    13.856697  0.000000e+00\n", "delta_2                      0.677961      0.038069    17.808535  0.000000e+00"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["results.getEstimatedParameters()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tevrp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}