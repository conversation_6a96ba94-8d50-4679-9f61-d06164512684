import pandas as pd
import biogeme.database as db
import biogeme.biogeme as bio
import biogeme.models as models
import biogeme.messaging as msg
import os
from biogeme.expressions import (
    Beta,
    Variable,
    DefineVariable,
    log,
    RandomVariable,
    Integrate,
    Elem,
    bioNormalCdf,
    exp,
    bioDraws,
    MonteCarlo,
)

import shutil
import matplotlib.pyplot as plt
import numpy as np

df = pd.read_excel('new_412.xlsx')
unreli_index = pd.read_excel('问题问卷编号.xlsx', header= None)
# 筛选无效问卷 删除 回答时间小于2min且 Attitude1 和 Attitude5回答一致 的问卷
# df = df[df['QeTime'] >= 120]

df = df[df['UnsafeAccident'] == 1]
df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]
# 同学评估筛选
# df = df[~df.index.isin(unreli_index[0])]

#*修改
df['SeverityAccident'] = np.where(df['Severity'] <= 2, 1, 2)

database = db.Database('data', df)
globals().update(database.variables)

#* variable definition
# 场景属性自变量
deliVolume = Variable('Volume')
deliTime = Variable('Time')
deliWeather = Variable('Weather')
deliLocation = Variable('Location')
RemainTime = Variable('RemainTime')
CauseUnsafe = Variable('CauseUnsafe')
Severity = Variable('Severity')

# 因变量
UnsafeAccident = Variable('UnsafeAccident')
SeverityAccident = Variable('SeverityAccident')

# 社会经济属性自变量
gender = Variable('gender')
age = Variable('age')
education = Variable('education')
marriage = Variable('marriage')
family = Variable('family')
citytime = Variable('citytime')
monthincome = Variable('monthincome')
disposableincome = Variable('disposableincome')

# 配送属性自变量
deliverytime = Variable('deliverytime')
deliverymode = Variable('deliverymode')
deliveryposition = Variable('deliveryposition')
workingdays = Variable('workingdays')
workinghours = Variable('workinghours')
deliveryorders = Variable('deliveryorders')
deliveryrange = Variable('deliveryrange')
deliveryspeed = Variable('deliveryspeed')

# 社会经济属性二分类变量
gender_female = DefineVariable('gender_female', gender == 2, database)

age_less_24 = DefineVariable('age_less_24', age <= 2, database)
age_more_35 = DefineVariable('age_more_35', age >= 4, database)

edu_less_junior = DefineVariable('edu_less_junior', education <= 2, database)
edu_more_uni = DefineVariable('edu_more_uni', education >= 4, database)

marriage_not = DefineVariable('marriage_not', marriage <= 1, database)

children = DefineVariable('children', ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0), database)

family_small = DefineVariable('family_small', family <= 2, database)
family_middle = DefineVariable('family_middle', (family <= 4) + (family >= 3) > 0, database)
family_big = DefineVariable('family_big', family >= 5, database)

citytime_less_3 = DefineVariable('citytime_less_3', citytime <= 2, database)
citytime_more_6 = DefineVariable('citytime_more_6', citytime >= 4, database)
citytime_local = DefineVariable('citytime_local', citytime == 5, database)

monthincome_less_2000 = DefineVariable('monthincome_less_2000', monthincome <= 1, database)
monthincome_less_4000 = DefineVariable('monthincome_less_4000', monthincome <= 2, database)
monthincome_less_6000 = DefineVariable('monthincome_less_6000', monthincome <= 3, database)
monthincome_more_8000 = DefineVariable('monthincome_more_8000', monthincome >= 5, database)

disposableincome_less_5000 = DefineVariable('disposableincome_less_5000', disposableincome <= 1, database)
disposableincome_less_10000 = DefineVariable('disposableincome_less_10000', disposableincome <= 2, database)

# 场景属性二分类变量
deliVolume_low = DefineVariable('deliVolume_low', deliVolume <= 1, database)
# deliVolume_middle = DefineVariable('deliVolume_middle', deliVolume <= 2, database)
deliVolume_high = DefineVariable('deliVolume_high', deliVolume >= 3, database)

deliTime_morning = DefineVariable('deliTime_morning', deliTime == 3, database)
deliTime_noon = DefineVariable('deliTime_noon', deliTime == 1, database)
deliTime_evening = DefineVariable('deliTime_evening', deliTime == 2, database)
deliTime_afternoon = DefineVariable('deliTime_afternoon', deliTime == 4, database)
deliTime_night = DefineVariable('deliTime_night', deliTime == 5, database)

deliWeather_heavy = DefineVariable('deliWeather_heavy', deliWeather == 1, database)
deliWeather_good = DefineVariable('deliWeather_good', deliWeather == 2, database)
deliWeather_spit = DefineVariable('deliWeather_spit', deliWeather == 3, database)

deliLocation_inter = DefineVariable('deliWeather_inter', deliLocation == 1, database)
deliLocation_straight = DefineVariable('deliLocation_straight', deliLocation == 2, database)
deliLocation_curve = DefineVariable('deliLocation_curve', deliLocation == 3, database)
deliLocation_pede = DefineVariable('deliLocation_pede', deliLocation == 5, database)

RemainTime_short = DefineVariable('RemainTime_short', RemainTime <= 1, database)

CauseUnsafe_overspeed = DefineVariable('CauseUnsafe_overspeed', CauseUnsafe == 1, database)
CauseUnsafe_breakrule = DefineVariable('CauseUnsafe_breakrule', CauseUnsafe == 2, database)
CauseUnsafe_drowsy = DefineVariable('CauseUnsafe_drowsy', CauseUnsafe == 3, database)
CauseUnsafe_emergency = DefineVariable('CauseUnsafe_emergency', CauseUnsafe == 4, database)
CauseUnsafe_phone = DefineVariable('CauseUnsafe_phone', CauseUnsafe == 5, database)
CauseUnsafe_without = DefineVariable('CauseUnsafe_without', CauseUnsafe == 6, database)

# 配送属性二分类变量
deliverytime_brief = DefineVariable('deliverytime_brief', deliverytime == 1, database)
deliverytime_short = DefineVariable('deliverytime_short', deliverytime <= 2, database)
deliverytime_extended = DefineVariable('deliverytime_extended', deliverytime >= 3, database)
deliverytime_prolonged = DefineVariable('deliverytime_prolonged', deliverytime  == 4, database)

deliverymode_full = DefineVariable('deliverymode_full', deliverymode >= 2, database)

deliveryposition_brief = DefineVariable('deliveryposition_brief', deliveryposition <= 1, database)
deliveryposition_short = DefineVariable('deliveryposition_short', deliveryposition <= 2, database)
deliveryposition_extended = DefineVariable('deliveryposition_extended', deliveryposition >= 3, database)
deliveryposition_prolonged = DefineVariable('deliveryposition_prolonged', deliveryposition  >= 4, database)

workingdays_brief = DefineVariable('workingdays_brief', workingdays <= 1, database)
workingdays_short = DefineVariable('workingdays_short', workingdays <= 2, database)
workingdays_standard = DefineVariable('workingdays_standard', workingdays <= 3, database)
workingdays_extended = DefineVariable('workingdays_extended', workingdays  >= 5, database)
workingdays_prolonged = DefineVariable('workingdays_prolonged', workingdays  >= 6, database)

workinghours_brief = DefineVariable('workinghours_brief', workinghours <= 1, database)
workinghours_short = DefineVariable('workinghours_short', workinghours <= 2, database)
workinghours_standard = DefineVariable('workinghours_standard', workinghours >= 3, database)
workinghours_extended = DefineVariable('workinghours_extended', workinghours >= 4, database)
workinghours_prolonged = DefineVariable('workinghours_prolonged', workinghours >= 5, database)

deliveryorders_brief = DefineVariable('deliveryorders_brief', deliveryorders <= 1, database)
deliveryorders_short = DefineVariable('deliveryorders_short', deliveryorders <= 2, database)
deliveryorders_standard = DefineVariable('deliveryorders_standard', deliveryorders <= 3, database)
deliveryorders_extended = DefineVariable('deliveryorders_extended', deliveryorders >= 4, database)
deliveryorders_prolonged = DefineVariable('deliveryorders_prolonged', deliveryorders >= 5, database)

deliveryrange_brief = DefineVariable('deliveryrange_brief', deliveryrange <= 1, database)
deliveryrange_short = DefineVariable('deliveryrange_short', deliveryrange <= 2, database)
deliveryrange_standard = DefineVariable('deliveryrange_standard', deliveryrange <= 3, database)
deliveryrange_extended = DefineVariable('deliveryrange_extended', deliveryrange >= 4, database)
deliveryrange_prolonged = DefineVariable('deliveryrange_prolonged', deliveryrange >= 5, database)

deliveryspeed_brief = DefineVariable('deliveryspeed_brief', deliveryspeed <= 1, database)
deliveryspeed_short = DefineVariable('deliveryspeed_short', deliveryspeed <= 2, database)
deliveryspeed_standard = DefineVariable('deliveryspeed_standard', deliveryspeed >= 3, database)
deliveryspeed_extended = DefineVariable('deliveryspeed_extended', deliveryspeed >= 4, database)
deliveryspeed_prolonged = DefineVariable('deliveryspeed_prolonged', deliveryspeed == 5, database)

#* choice model 
BETA_Volume = Beta('BETA_Volume', 0, None, None, 0)
BETA_Time= Beta('BETA_Time', 0, None, None, 0)
BETA_Weather= Beta('BETA_Weather', 0, None, None, 0)
BETA_Location = Beta('BETA_Location', 0, None, None, 0)
BETA_RemainTime = Beta('BETA_RemainTime', 0, None, None, 0)
BETA_CauseUnsafe = Beta('BETA_CauseUnsafe', 0, None, None, 0)

BETA_deliVolume_low = Beta('BETA_deliVolume_low', 0, None, None, 0)
BETA_deliVolume_high = Beta('BETA_deliVolume_high', 0, None, None, 0)

BETA_deliTime_morning = Beta('BETA_deliTime_morning', 0, None, None, 0)
BETA_deliTime_noon = Beta('BETA_deliTime_noon', 0, None, None, 0)
BETA_deliTime_evening = Beta('BETA_deliTime_evening', 0, None, None, 0)
BETA_deliTime_afternoon = Beta('BETA_deliTime_afternoon', 0, None, None, 0)
BETA_deliTime_night = Beta('BETA_deliTime_night', 0, None, None, 0)

BETA_deliWeather_heavy = Beta('BETA_deliWeather_heavy', 0, None, None, 0)
BETA_deliWeather_good = Beta('BETA_deliWeather_good', 0, None, None, 0)
BETA_deliWeather_spit = Beta('BETA_deliWeather_spit', 0, None, None, 0)

BETA_deliLocation_inter = Beta('BETA_deliLocation_inter', 0, None, None, 0)
BETA_deliLocation_straight = Beta('BETA_deliLocation_straight', 0, None, None, 0)
BETA_deliLocation_curve = Beta('BETA_deliLocation_curve', 0, None, None, 0)
BETA_deliLocation_pede = Beta('BETA_deliLocation_pede', 0, None, None, 0)

BETA_RemainTime_short = Beta('BETA_RemainTime_short', 0, None, None, 0)

BETA_CauseUnsafe_overspeed = Beta('BETA_CauseUnsafe_overspeed', 0, None, None, 0)
BETA_CauseUnsafe_breakrule = Beta('BETA_CauseUnsafe_breakrule', 0, None, None, 0)
BETA_CauseUnsafe_drowsy = Beta('BETA_CauseUnsafe_drowsy', 0, None, None, 0)
BETA_CauseUnsafe_emergency = Beta('BETA_CauseUnsafe_emergency', 0, None, None, 0)
BETA_CauseUnsafe_phone = Beta('BETA_CauseUnsafe_phone', 0, None, None, 0)
BETA_CauseUnsafe_without = Beta('BETA_CauseUnsafe_without', 0, None, None, 0)

BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)
BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)
# BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)
BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)
BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)

BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)

BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)
BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)
# BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)
BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)
BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)

BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)
BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)
BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)
BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)
BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)

BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)
BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)
BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)
BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)
BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)

BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)
BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)
BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)
BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)
BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)

BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)
BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)
BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)
BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)
BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)

BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)
BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)
BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)
BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)
BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)

BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)

BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)
BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)

BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)
BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)

BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)

BETA_children = Beta('BETA_children', 0.0, None, None, 0)

BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)
BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)
BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)

BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)
BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)
BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)

BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)
BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)
BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)
BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)

BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)
BETA_disposableincome_less_10000 = Beta('BETA_disposableincome_less_10000', 0.0, None, None, 0)

BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)

#* choice model 
# V1 = BETA_intercept + \
#      BETA_deliverytime_brief * deliverytime_brief + \
#      BETA_deliverytime_short * deliverytime_short + \
#      BETA_deliverytime_extended * deliverytime_extended + \
#      BETA_deliverytime_prolonged * deliverytime_prolonged + \
#      BETA_deliverymode_full * deliverymode_full + \
#      BETA_deliveryposition_brief * deliveryposition_brief + \
#      BETA_deliveryposition_short * deliveryposition_short + \
#      BETA_deliveryposition_extended * deliveryposition_extended + \
#      BETA_deliveryposition_prolonged * deliveryposition_prolonged + \
#      BETA_workingdays_brief * workingdays_brief + \
#      BETA_workingdays_short * workingdays_short + \
#      BETA_workingdays_standard * workingdays_standard + \
#      BETA_workingdays_extended * workingdays_extended + \
#      BETA_workingdays_prolonged * workingdays_prolonged + \
#      BETA_workinghours_brief * workinghours_brief + \
#      BETA_workinghours_short * workinghours_short + \
#      BETA_workinghours_standard * workinghours_standard + \
#      BETA_workinghours_extended * workinghours_extended + \
#      BETA_workinghours_prolonged * workinghours_prolonged + \
#      BETA_deliveryorders_brief * deliveryorders_brief + \
#      BETA_deliveryorders_short * deliveryorders_short + \
#      BETA_deliveryorders_standard * deliveryorders_standard + \
#      BETA_deliveryorders_extended * deliveryorders_extended + \
#      BETA_deliveryorders_prolonged * deliveryorders_prolonged + \
#      BETA_deliveryrange_brief * deliveryrange_brief + \
#      BETA_deliveryrange_short * deliveryrange_short + \
#      BETA_deliveryrange_standard * deliveryrange_standard + \
#      BETA_deliveryrange_extended * deliveryrange_extended + \
#      BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
#      BETA_deliveryspeed_brief * deliveryspeed_brief + \
#      BETA_deliveryspeed_short * deliveryspeed_short + \
#      BETA_deliveryspeed_standard * deliveryspeed_standard + \
#      BETA_deliveryspeed_extended * deliveryspeed_extended + \
#      BETA_deliveryspeed_prolonged * deliveryspeed_prolonged + \
#      BETA_gender_female  * gender_female + \
#      BETA_age_less_24 * age_less_24 + \
#      BETA_age_more_35 * age_more_35 + \
#      BETA_edu_less_junior * edu_less_junior + \
#      BETA_edu_more_uni * edu_more_uni + \
#      BETA_marriage * marriage_not + \
#      BETA_children * children + \
#      BETA_family_small * family_small + \
#      BETA_family_middle * family_middle + \
#      BETA_family_big * family_big + \
#      BETA_citytime_less_3 * citytime_less_3 + \
#      BETA_citytime_more_6 * citytime_more_6 + \
#      BETA_citytime_local * citytime_local + \
#      BETA_monthincome_less_2000 * monthincome_less_2000 + \
#      BETA_monthincome_less_4000 * monthincome_less_4000 + \
#      BETA_monthincome_less_6000 * monthincome_less_6000 + \
#      BETA_monthincome_more_8000 * monthincome_more_8000 + \
#      BETA_disposableincome_less_5000 * disposableincome_less_5000 + \
#      BETA_disposableincome_less_10000 * disposableincome_less_10000
# V2 = 0

#* 第一次once model
# V1 = BETA_intercept + \
#      BETA_age_less_24 * age_less_24 + \
#      BETA_children * children + \
#      BETA_citytime_less_3 * citytime_less_3 + \
#      BETA_deliverymode_full * deliverymode_full + \
#      BETA_deliveryorders_extended * deliveryorders_extended + \
#      BETA_deliveryposition_short * deliveryposition_short + \
#      BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
#      BETA_deliveryspeed_extended * deliveryspeed_extended + \
#      BETA_deliverytime_short * deliverytime_short + \
#      BETA_disposableincome_less_10000 * disposableincome_less_10000 + \
#      BETA_edu_less_junior * edu_less_junior + \
#      BETA_family_big * family_big + \
#      BETA_gender_female  * gender_female + \
#      BETA_marriage * marriage_not + \
#      BETA_monthincome_less_6000 * monthincome_less_6000 + \
#      BETA_workingdays_standard * workingdays_standard + \
#      BETA_workinghours_brief * workinghours_brief

# V2 = 0

#* 第二次once model
# V1 = BETA_intercept + \
# deliVolume_high * BETA_deliVolume_high + \
# deliVolume_low * BETA_deliVolume_low + \
# deliTime_morning * BETA_deliTime_morning + \
# deliTime_noon * BETA_deliTime_noon + \
# deliTime_evening * BETA_deliTime_evening + \
# deliTime_afternoon * BETA_deliTime_afternoon + \
# deliTime_night * BETA_deliTime_night + \
# deliWeather_good * BETA_deliWeather_good + \
# deliWeather_heavy * BETA_deliWeather_heavy + \
# deliWeather_spit * BETA_deliWeather_spit + \
# deliLocation_inter * BETA_deliLocation_inter + \
# deliLocation_straight * BETA_deliLocation_straight + \
# deliLocation_curve * BETA_deliLocation_curve + \
# deliLocation_pede * BETA_deliLocation_pede + \
# RemainTime_short * BETA_RemainTime_short + \
# CauseUnsafe_overspeed * BETA_CauseUnsafe_overspeed + \
# CauseUnsafe_breakrule * BETA_CauseUnsafe_breakrule + \
# CauseUnsafe_drowsy * BETA_CauseUnsafe_drowsy + \
# CauseUnsafe_emergency * BETA_CauseUnsafe_emergency + \
# CauseUnsafe_phone * BETA_CauseUnsafe_phone + \
# CauseUnsafe_without * BETA_CauseUnsafe_without

V1 = BETA_intercept + \
deliVolume_high * BETA_deliVolume_high + \
deliTime_morning * BETA_deliTime_morning + \
deliTime_noon * BETA_deliTime_noon + \
deliTime_evening * BETA_deliTime_evening + \
deliTime_afternoon * BETA_deliTime_afternoon + \
deliTime_night * BETA_deliTime_night + \
deliWeather_good * BETA_deliWeather_good + \
deliWeather_heavy * BETA_deliWeather_heavy + \
deliWeather_spit * BETA_deliWeather_spit + \
deliLocation_inter * BETA_deliLocation_inter + \
deliLocation_straight * BETA_deliLocation_straight + \
deliLocation_curve * BETA_deliLocation_curve + \
deliLocation_pede * BETA_deliLocation_pede + \
RemainTime_short * BETA_RemainTime_short 

V2 = 0

#* 第三次once model

# Associate utility functions with the numbering of alternatives
V = {1: V1,
     2: V2,
     }

condprob = models.loglogit(V, None, SeverityAccident)

# Define level of verbosity
logger = msg.bioMessage()
# logger.setSilent()
# logger.setWarning()
logger.setGeneral()
# logger.setDetailed()

# Create the Biogeme object
biogeme  = bio.BIOGEME(database,condprob)
# biogeme.modelName = 'accident_mnl'
# biogeme.modelName = 'accident_mnl_once_1' # 根据时间和条件同时删除问卷
# biogeme.modelName = 'accident_mnl_once_2' # 不删除问卷
# biogeme.modelName = 'accident_mnl_once_3' # 根据条删除问卷
biogeme.modelName = 'accident_degree_mnl_1' # 根据条删除问卷
biogeme.generate_pickle = False
biogeme.generatePickle = False
# biogeme.generate_html = False

# Estimate the parameters
results = biogeme.estimate()

print(f'Estimated betas: {len(results.data.betaValues)}')
print(f'Final log likelihood: {results.data.logLike:.3f}')
print(f'Output file: {results.data.htmlFileName}')

shutil.move(str(results.data.htmlFileName), str('new_questionaire/mnl/'+results.data.htmlFileName))

# print(results.short_summary())

# pandas_results = results.getEstimatedParameters()
# print(pandas_results)