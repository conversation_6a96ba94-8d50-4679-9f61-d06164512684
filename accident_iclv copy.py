
from datetime import datetime
import pandas as pd
import biogeme.database as db
import biogeme.biogeme as bio
import biogeme.models as models
import biogeme.messaging as msg
import os
from biogeme.expressions import (
    Beta,
    Variable,
    DefineVariable,
    log,
    RandomVariable,
    Integrate,
    Elem,
    bioNormalCdf,
    exp,
    bioDraws,
    MonteCarlo,
)

import shutil
import matplotlib.pyplot as plt
import copy

dforigin = pd.read_excel('new_412.xlsx')
unreli_index = pd.read_excel('问题问卷编号.xlsx', header= None)

# 筛选无效问卷 删除 回答时间小于2min且 Attitude1 和 Attitude5回答一致 的问卷
# for num_idx in [1,2,3]:
for num_idx in [1]:
    if num_idx == 1:
        df = dforigin[dforigin['QeTime'] >= 120]
        df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]
    elif num_idx == 2: 
        df = copy.deepcopy(dforigin)
    else:
        df = dforigin[dforigin['QeTime'] >= 120]
        df = df[~((df['Attitude1'] == df['Attitude6']))]

    # df = df[df['QeTime'] >= 120]
    # df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]
    # 同学评估筛选
    # df = df[~df.index.isin(unreli_index[0])]

    database = db.Database('data', df)
    globals().update(database.variables)

    #* variable definition
    # 场景属性自变量
    deliVolume = Variable('Volume')
    deliTime = Variable('Time')
    deliWeather = Variable('Weather')
    deliLocation = Variable('Location')
    RemainTime = Variable('RemainTime')
    CauseUnsafe = Variable('CauseUnsafe')

    # 因变量
    UnsafeAccident = Variable('UnsafeAccident')

    # 社会经济属性自变量
    gender = Variable('gender')
    age = Variable('age')
    education = Variable('education')
    marriage = Variable('marriage')
    family = Variable('family')
    citytime = Variable('citytime')
    monthincome = Variable('monthincome')
    disposableincome = Variable('disposableincome')

    # 配送属性自变量
    deliverytime = Variable('deliverytime')
    deliverymode = Variable('deliverymode')
    deliveryposition = Variable('deliveryposition')
    workingdays = Variable('workingdays')
    workinghours = Variable('workinghours')
    deliveryorders = Variable('deliveryorders')
    deliveryrange = Variable('deliveryrange')
    deliveryspeed = Variable('deliveryspeed')

    # 社会经济属性二分类变量
    gender_female = DefineVariable('gender_female', gender == 2, database)

    age_less_24 = DefineVariable('age_less_24', age <= 2, database)
    age_between_24_35 = DefineVariable('age_between_24_35', age == 3, database)
    age_more_35 = DefineVariable('age_more_35', age >= 4, database)

    edu_less_junior = DefineVariable('edu_less_junior', education <= 2, database)
    edu_more_uni = DefineVariable('edu_more_uni', education >= 4, database)

    marriage_not = DefineVariable('marriage_not', marriage <= 1, database)
    children = DefineVariable('children', ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0), database)

    family_small = DefineVariable('family_small', family <= 2, database)
    family_middle = DefineVariable('family_middle', (family <= 4) + (family >= 3) > 0, database)
    family_big = DefineVariable('family_big', (family >= 5) &  (family <= 7), database)

    citytime_less_3 = DefineVariable('citytime_less_3', citytime <= 2, database)
    citytime_more_6 = DefineVariable('citytime_more_6', citytime >= 4, database)
    citytime_local = DefineVariable('citytime_local', citytime == 5, database)

    monthincome_less_2000 = DefineVariable('monthincome_less_2000', monthincome <= 1, database)
    monthincome_less_4000 = DefineVariable('monthincome_less_4000', monthincome <= 2, database)
    monthincome_less_6000 = DefineVariable('monthincome_less_6000', monthincome <= 3, database)
    monthincome_more_8000 = DefineVariable('monthincome_more_8000', monthincome >= 5, database)
    monthincome_4000_8000 = DefineVariable('monthincome_4000_8000', (monthincome >= 3) & (monthincome <= 4), database)

    disposableincome_less_5000 = DefineVariable('disposableincome_less_5000', disposableincome <= 1, database)
    disposableincome_more_10000 = DefineVariable('disposableincome_more_10000', disposableincome >= 3, database)

    # 场景属性二分类变量
    deliVolume_low = DefineVariable('deliVolume_low', deliVolume <= 1, database)
    deliVolume_middle = DefineVariable('deliVolume_middle', deliVolume == 2, database)
    deliVolume_high = DefineVariable('deliVolume_high', deliVolume >= 3, database)

    deliTime_morning = DefineVariable('deliTime_morning', deliTime == 3, database)
    deliTime_noon = DefineVariable('deliTime_noon', deliTime == 1, database)
    deliTime_evening = DefineVariable('deliTime_evening', deliTime == 2, database)
    deliTime_afternoon = DefineVariable('deliTime_afternoon', deliTime == 4, database)
    deliTime_night = DefineVariable('deliTime_night', deliTime >= 5, database)

    deliWeather_heavy = DefineVariable('deliWeather_heavy', deliWeather == 1, database)
    deliWeather_good = DefineVariable('deliWeather_good', deliWeather == 2, database)
    deliWeather_spit = DefineVariable('deliWeather_spit', deliWeather == 3, database)

    deliLocation_inter = DefineVariable('deliWeather_inter', deliLocation == 1, database)
    deliLocation_straight = DefineVariable('deliLocation_straight', deliLocation == 2, database)
    deliLocation_curve = DefineVariable('deliLocation_curve', deliLocation == 3, database)
    deliLocation_pede = DefineVariable('deliLocation_pede', deliLocation == 5, database)

    RemainTime_short = DefineVariable('RemainTime_short', RemainTime <= 1, database)

    CauseUnsafe_overspeed = DefineVariable('CauseUnsafe_overspeed', CauseUnsafe == 1, database)
    CauseUnsafe_breakrule = DefineVariable('CauseUnsafe_breakrule', CauseUnsafe == 2, database)
    CauseUnsafe_drowsy = DefineVariable('CauseUnsafe_drowsy', CauseUnsafe == 3, database)
    CauseUnsafe_emergency = DefineVariable('CauseUnsafe_emergency', CauseUnsafe == 4, database)
    CauseUnsafe_phone = DefineVariable('CauseUnsafe_phone', CauseUnsafe == 5, database)
    CauseUnsafe_without = DefineVariable('CauseUnsafe_without', CauseUnsafe == 6, database)

    # 配送属性二分类变量
    deliverytime_brief = DefineVariable('deliverytime_brief', deliverytime == 1, database)
    deliverytime_short = DefineVariable('deliverytime_short', deliverytime <= 2, database)
    deliverytime_extended = DefineVariable('deliverytime_extended', deliverytime >= 3, database)
    deliverytime_prolonged = DefineVariable('deliverytime_prolonged', deliverytime  == 4, database)

    deliverymode_full = DefineVariable('deliverymode_full', deliverymode >= 2, database)

    deliveryposition_brief = DefineVariable('deliveryposition_brief', deliveryposition <= 1, database)
    deliveryposition_short = DefineVariable('deliveryposition_short', deliveryposition <= 2, database)
    deliveryposition_extended = DefineVariable('deliveryposition_extended', deliveryposition >= 3, database)
    deliveryposition_prolonged = DefineVariable('deliveryposition_prolonged', deliveryposition  >= 4, database)

    workingdays_brief = DefineVariable('workingdays_brief', workingdays <= 1, database)
    workingdays_short = DefineVariable('workingdays_short', workingdays <= 2, database)
    workingdays_standard = DefineVariable('workingdays_standard', workingdays <= 3, database)
    workingdays_extended = DefineVariable('workingdays_extended', workingdays  >= 5, database)
    workingdays_prolonged = DefineVariable('workingdays_prolonged', workingdays  >= 6, database)

    workinghours_brief = DefineVariable('workinghours_brief', workinghours <= 1, database)
    workinghours_short = DefineVariable('workinghours_short', workinghours <= 2, database)
    workinghours_standard = DefineVariable('workinghours_standard', workinghours >= 3, database)
    workinghours_extended = DefineVariable('workinghours_extended', workinghours >= 4, database)
    workinghours_prolonged = DefineVariable('workinghours_prolonged', workinghours >= 5, database)

    deliveryorders_brief = DefineVariable('deliveryorders_brief', deliveryorders <= 1, database)
    deliveryorders_short = DefineVariable('deliveryorders_short', deliveryorders <= 2, database)
    deliveryorders_standard = DefineVariable('deliveryorders_standard', deliveryorders <= 3, database)
    deliveryorders_extended = DefineVariable('deliveryorders_extended', deliveryorders >= 4, database)
    deliveryorders_prolonged = DefineVariable('deliveryorders_prolonged', deliveryorders >= 5, database)

    deliveryrange_brief = DefineVariable('deliveryrange_brief', deliveryrange <= 1, database)
    deliveryrange_short = DefineVariable('deliveryrange_short', deliveryrange <= 2, database)
    deliveryrange_standard = DefineVariable('deliveryrange_standard', deliveryrange <= 3, database)
    deliveryrange_extended = DefineVariable('deliveryrange_extended', deliveryrange >= 4, database)
    deliveryrange_prolonged = DefineVariable('deliveryrange_prolonged', deliveryrange >= 5, database)

    deliveryspeed_brief = DefineVariable('deliveryspeed_brief', deliveryspeed <= 1, database)
    deliveryspeed_short = DefineVariable('deliveryspeed_short', deliveryspeed <= 2, database)
    deliveryspeed_standard = DefineVariable('deliveryspeed_standard', deliveryspeed >= 3, database)
    #!
    deliveryspeed_extended = DefineVariable('deliveryspeed_extended', deliveryspeed == 4, database)
    deliveryspeed_prolonged = DefineVariable('deliveryspeed_prolonged', deliveryspeed == 5, database)

    #* structual coefficient
    # Attitude
    coef_intercept_Att = Beta('coef_intercept_Att', 0.0, None, None, 0)
    coef_gender_Att = Beta('coef_gender_Att', 0.0, None, None, 0)
    coef_age_Att = Beta('coef_age_Att', 0.0, None, None, 0)
    coef_edu_Att = Beta('coef_edu_Att', 0.0, None, None, 0)
    coef_marriage_Att = Beta('coef_marriage_Att', 0.0, None, None, 0)
    coef_children_Att = Beta('coef_children_Att', 0.0, None, None, 0)
    coef_family_Att = Beta('coef_family_Att', 0.0, None, None, 0)
    coef_citytime_Att = Beta('coef_citytime_Att', 0.0, None, None, 0)
    coef_monthincome_Att = Beta('coef_monthincome_Att', 0.0, None, None, 0)
    coef_disposableincome_Att = Beta('coef_disposableincome_Att', 0.0, None, None, 0)
    # Unsafe behavior
    coef_intercept_Unsafe = Beta('coef_intercept_Unsafe', 0.0, None, None, 0)
    coef_gender_Unsafe = Beta('coef_gender_Unsafe', 0.0, None, None, 0)
    coef_age_Unsafe = Beta('coef_age_Unsafe', 0.0, None, None, 0)
    coef_edu_Unsafe = Beta('coef_edu_Unsafe', 0.0, None, None, 0)
    coef_marriage_Unsafe = Beta('coef_marriage_Unsafe', 0.0, None, None, 0)
    coef_children_Unsafe = Beta('coef_children_Unsafe', 0.0, None, None, 0)
    coef_family_Unsafe = Beta('coef_family_Unsafe', 0.0, None, None, 0)
    coef_citytime_Unsafe = Beta('coef_citytime_Unsafe', 0.0, None, None, 0)
    coef_monthincome_Unsafe = Beta('coef_monthincome_Unsafe', 0.0, None, None, 0)
    coef_disposableincome_Unsafe = Beta('coef_disposableincome_Unsafe', 0.0, None, None, 0)
    # Environment
    coef_intercept_Envi = Beta('coef_intercept_Envi', 0.0, None, None, 0)
    coef_gender_Envi = Beta('coef_gender_Envi', 0.0, None, None, 0)
    coef_age_Envi = Beta('coef_age_Envi', 0.0, None, None, 0)
    coef_edu_Envi = Beta('coef_edu_Envi', 0.0, None, None, 0)
    coef_marriage_Envi = Beta('coef_marriage_Envi', 0.0, None, None, 0)
    coef_children_Envi = Beta('coef_children_Envi', 0.0, None, None, 0)
    coef_family_Envi = Beta('coef_family_Envi', 0.0, None, None, 0)
    coef_citytime_Envi = Beta('coef_citytime_Envi', 0.0, None, None, 0)
    coef_monthincome_Envi = Beta('coef_monthincome_Envi', 0.0, None, None, 0)
    coef_disposableincome_Envi = Beta('coef_disposableincome_Envi', 0.0, None, None, 0)
    # Pressure
    coef_intercept_Press = Beta('coef_intercept_Press', 0.0, None, None, 0)
    coef_gender_Press = Beta('coef_gender_Press', 0.0, None, None, 0)
    coef_age_Press = Beta('coef_age_Press', 0.0, None, None, 0)
    coef_edu_Press = Beta('coef_edu_Press', 0.0, None, None, 0)
    coef_marriage_Press = Beta('coef_marriage_Press', 0.0, None, None, 0)
    coef_children_Press = Beta('coef_children_Press', 0.0, None, None, 0)
    coef_family_Press = Beta('coef_family_Press', 0.0, None, None, 0)
    coef_citytime_Press = Beta('coef_citytime_Press', 0.0, None, None, 0)
    coef_monthincome_Press = Beta('coef_monthincome_Press', 0.0, None, None, 0)
    coef_disposableincome_Press = Beta('coef_disposableincome_Press', 0.0, None, None, 0)
    # Control
    coef_intercept_Con = Beta('coef_intercept_Con', 0.0, None, None, 0)
    coef_gender_Con = Beta('coef_gender_Con', 0.0, None, None, 0)
    coef_age_Con = Beta('coef_age_Con', 0.0, None, None, 0)
    coef_edu_Con = Beta('coef_edu_Con', 0.0, None, None, 0)
    coef_marriage_Con = Beta('coef_marriage_Con', 0.0, None, None, 0)
    coef_children_Con = Beta('coef_children_Con', 0.0, None, None, 0)
    coef_family_Con = Beta('coef_family_Con', 0.0, None, None, 0)
    coef_citytime_Con = Beta('coef_citytime_Con', 0.0, None, None, 0)
    coef_monthincome_Con = Beta('coef_monthincome_Con', 0.0, None, None, 0)
    coef_disposableincome_Con = Beta('coef_disposableincome_Con', 0.0, None, None, 0)
    # Delivery habit
    coef_intercept_Deli = Beta('coef_intercept_Deli', 0.0, None, None, 0)
    coef_gender_Deli = Beta('coef_gender_Deli', 0.0, None, None, 0)
    coef_age_Deli = Beta('coef_age_Deli', 0.0, None, None, 0)
    coef_edu_Deli = Beta('coef_edu_Deli', 0.0, None, None, 0)
    coef_marriage_Deli = Beta('coef_marriage_Deli', 0.0, None, None, 0)
    coef_children_Deli = Beta('coef_children_Deli', 0.0, None, None, 0)
    coef_family_Deli = Beta('coef_family_Deli', 0.0, None, None, 0)
    coef_citytime_Deli = Beta('coef_citytime_Deli', 0.0, None, None, 0)
    coef_monthincome_Deli = Beta('coef_monthincome_Deli', 0.0, None, None, 0)
    coef_disposableincome_Deli = Beta('coef_disposableincome_Deli', 0.0, None, None, 0)
    # Habit
    coef_intercept_Habit = Beta('coef_intercept_Habit', 0.0, None, None, 0)
    coef_gender_Habit = Beta('coef_gender_Habit', 0.0, None, None, 0)
    coef_age_Habit = Beta('coef_age_Habit', 0.0, None, None, 0)
    coef_edu_Habit = Beta('coef_edu_Habit', 0.0, None, None, 0)
    coef_marriage_Habit = Beta('coef_marriage_Habit', 0.0, None, None, 0)
    coef_children_Habit = Beta('coef_children_Habit', 0.0, None, None, 0)
    coef_family_Habit = Beta('coef_family_Habit', 0.0, None, None, 0)
    coef_citytime_Habit = Beta('coef_citytime_Habit', 0.0, None, None, 0)
    coef_monthincome_Habit = Beta('coef_monthincome_Habit', 0.0, None, None, 0)
    coef_disposableincome_Habit = Beta('coef_disposableincome_Habit', 0.0, None, None, 0)
    # Subjective
    coef_intercept_Sub = Beta('coef_intercept_Sub', 0.0, None, None, 0)
    coef_gender_Sub = Beta('coef_gender_Sub', 0.0, None, None, 0)
    coef_age_Sub = Beta('coef_age_Sub', 0.0, None, None, 0)
    coef_edu_Sub = Beta('coef_edu_Sub', 0.0, None, None, 0)
    coef_marriage_Sub = Beta('coef_marriage_Sub', 0.0, None, None, 0)
    coef_children_Sub = Beta('coef_children_Sub', 0.0, None, None, 0)
    coef_family_Sub = Beta('coef_family_Sub', 0.0, None, None, 0)
    coef_citytime_Sub = Beta('coef_citytime_Sub', 0.0, None, None, 0)
    coef_monthincome_Sub = Beta('coef_monthincome_Sub', 0.0, None, None, 0)
    coef_disposableincome_Sub = Beta('coef_disposableincome_Sub', 0.0, None, None, 0)

    #* measurement coefficient
    INTER_Att2 = Beta('INTER_Att2', 0, None, None, 1)
    INTER_Att3 = Beta('INTER_Att3', 0, None, None, 0)
    INTER_Att4 = Beta('INTER_Att4', 0, None, None, 0)
    INTER_Att5 = Beta('INTER_Att5', 0, None, None, 0)
    INTER_Att6 = Beta('INTER_Att6', 0, None, None, 0)

    B_Att2 = Beta('B_Att2', 1, None, None, 1)
    B_Att3 = Beta('B_Att3', 1, None, None, 0)
    B_Att4 = Beta('B_Att4', 1, None, None, 0)
    B_Att5 = Beta('B_Att5', 1, None, None, 0)
    B_Att6 = Beta('B_Att6', 1, None, None, 0)

    SIGMA_Att2 = Beta('SIGMA_Att2', 1, 1.0e-5, None, 1)
    SIGMA_Att3 = Beta('SIGMA_Att3', 1, 1.0e-5, None, 0)
    SIGMA_Att4 = Beta('SIGMA_Att4', 1, 1.0e-5, None, 0)
    SIGMA_Att5 = Beta('SIGMA_Att5', 1, 1.0e-5, None, 0)
    SIGMA_Att6 = Beta('SIGMA_Att6', 1, 1.0e-5, None, 0)

    INTER_Unsafe1 = Beta('INTER_Unsafe1', 0, None, None, 1)
    INTER_Unsafe2 = Beta('INTER_Unsafe2', 0, None, None, 0)
    INTER_Unsafe3 = Beta('INTER_Unsafe3', 0, None, None, 0)
    INTER_Unsafe4 = Beta('INTER_Unsafe4', 0, None, None, 0)
    INTER_Unsafe5 = Beta('INTER_Unsafe5', 0, None, None, 0)

    B_Unsafe1 = Beta('B_Unsafe1', 1, None, None, 1)
    B_Unsafe2 = Beta('B_Unsafe2', 1, None, None, 0)
    B_Unsafe3 = Beta('B_Unsafe3', 1, None, None, 0)
    B_Unsafe4 = Beta('B_Unsafe4', 1, None, None, 0)
    B_Unsafe5 = Beta('B_Unsafe5', 1, None, None, 0)

    SIGMA_Unsafe1 = Beta('SIGMA_Unsafe1', 1, 1.0e-5, None, 1)
    SIGMA_Unsafe2 = Beta('SIGMA_Unsafe2', 1, 1.0e-5, None, 0)
    SIGMA_Unsafe3 = Beta('SIGMA_Unsafe3', 1, 1.0e-5, None, 0)
    SIGMA_Unsafe4 = Beta('SIGMA_Unsafe4', 1, 1.0e-5, None, 0)
    SIGMA_Unsafe5 = Beta('SIGMA_Unsafe5', 1, 1.0e-5, None, 0)

    INTER_Envi1 = Beta('INTER_Envi1', 0, None, None, 1)
    INTER_Envi2 = Beta('INTER_Envi2', 0, None, None, 0)
    INTER_Envi3 = Beta('INTER_Envi3', 0, None, None, 0)
    INTER_Envi4 = Beta('INTER_Envi4', 0, None, None, 0)
    INTER_Envi5 = Beta('INTER_Envi5', 0, None, None, 0)

    B_Envi1 = Beta('B_Envi1', 1, None, None, 1)
    B_Envi2 = Beta('B_Envi2', 1, None, None, 0)
    B_Envi3 = Beta('B_Envi3', 1, None, None, 0)
    B_Envi4 = Beta('B_Envi4', 1, None, None, 0)
    B_Envi5 = Beta('B_Envi5', 1, None, None, 0)

    SIGMA_Envi1 = Beta('SIGMA_Envi1', 1, 1.0e-5, None, 1)
    SIGMA_Envi2 = Beta('SIGMA_Envi2', 1, 1.0e-5, None, 0)
    SIGMA_Envi3 = Beta('SIGMA_Envi3', 1, 1.0e-5, None, 0)
    SIGMA_Envi4 = Beta('SIGMA_Envi4', 1, 1.0e-5, None, 0)
    SIGMA_Envi5 = Beta('SIGMA_Envi5', 1, 1.0e-5, None, 0)

    INTER_Press1 = Beta('INTER_Press1', 0, None, None, 1)
    INTER_Press2 = Beta('INTER_Press2', 0, None, None, 0)
    INTER_Press3 = Beta('INTER_Press3', 0, None, None, 0)
    INTER_Press4 = Beta('INTER_Press4', 0, None, None, 0)

    B_Press1 = Beta('B_Press1', 1, None, None, 1)
    B_Press2 = Beta('B_Press2', 1, None, None, 0)
    B_Press3 = Beta('B_Press3', 1, None, None, 0)
    B_Press4 = Beta('B_Press4', 1, None, None, 0)

    SIGMA_Press1 = Beta('SIGMA_Press1', 1, 1.0e-5, None, 1)
    SIGMA_Press2 = Beta('SIGMA_Press2', 1, 1.0e-5, None, 0)
    SIGMA_Press3 = Beta('SIGMA_Press3', 1, 1.0e-5, None, 0)
    SIGMA_Press4 = Beta('SIGMA_Press4', 1, 1.0e-5, None, 0)

    INTER_Con1 = Beta('INTER_Con1', 0, None, None, 1)
    INTER_Con2 = Beta('INTER_Con2', 0, None, None, 0)
    INTER_Con3 = Beta('INTER_Con3', 0, None, None, 0)
    INTER_Con4 = Beta('INTER_Con4', 0, None, None, 0)

    B_Con1 = Beta('B_Con1', 1, None, None, 1)
    B_Con2 = Beta('B_Con2', 1, None, None, 0)
    B_Con3 = Beta('B_Con3', 1, None, None, 0)
    B_Con4 = Beta('B_Con4', 1, None, None, 0)

    SIGMA_Con1 = Beta('SIGMA_Con1', 1, 1.0e-5, None, 1)
    SIGMA_Con2 = Beta('SIGMA_Con2', 1, 1.0e-5, None, 0)
    SIGMA_Con3 = Beta('SIGMA_Con3', 1, 1.0e-5, None, 0)
    SIGMA_Con4 = Beta('SIGMA_Con4', 1, 1.0e-5, None, 0)

    INTER_Deli_1 = Beta('INTER_Deli_1', 0, None, None, 1)
    INTER_Deli_4 = Beta('INTER_Deli_4', 0, None, None, 0)
    INTER_Deli_5 = Beta('INTER_Deli_5', 0, None, None, 0)
    INTER_Deli_6 = Beta('INTER_Deli_6', 0, None, None, 0)

    B_Deli_1 = Beta('B_Deli_1', 1, None, None, 1)
    B_Deli_4 = Beta('B_Deli_4', 1, None, None, 0)
    B_Deli_5 = Beta('B_Deli_5', 1, None, None, 0)
    B_Deli_6 = Beta('B_Deli_6', 1, None, None, 0)

    SIGMA_Deli1 = Beta('SIGMA_Deli1', 1, 1.0e-5, None, 1)
    SIGMA_Deli4 = Beta('SIGMA_Deli4', 1, 1.0e-5, None, 0)
    SIGMA_Deli5 = Beta('SIGMA_Deli5', 1, 1.0e-5, None, 0)
    SIGMA_Deli6 = Beta('SIGMA_Deli6', 1, 1.0e-5, None, 0)

    INTER_Habit_1 = Beta('INTER_Habit_1', 0, None, None, 1)
    INTER_Habit_2 = Beta('INTER_Habit_2', 0, None, None, 0)
    INTER_Habit_3 = Beta('INTER_Habit_3', 0, None, None, 0)
    INTER_Habit_4 = Beta('INTER_Habit_4', 0, None, None, 0)
    INTER_Habit_5 = Beta('INTER_Habit_5', 0, None, None, 0)

    B_Habit1 = Beta('B_Habit1', 1, None, None, 1)
    B_Habit2 = Beta('B_Habit2', 1, None, None, 0)
    B_Habit3 = Beta('B_Habit3', 1, None, None, 0)
    B_Habit4 = Beta('B_Habit4', 1, None, None, 0)
    B_Habit5 = Beta('B_Habit5', 1, None, None, 0)

    SIGMA_Habit1 = Beta('SIGMA_Habit1', 1, 1.0e-5, None, 1)
    SIGMA_Habit2 = Beta('SIGMA_Habit2', 1, 1.0e-5, None, 0)
    SIGMA_Habit3 = Beta('SIGMA_Habit3', 1, 1.0e-5, None, 0)
    SIGMA_Habit4 = Beta('SIGMA_Habit4', 1, 1.0e-5, None, 0)
    SIGMA_Habit5 = Beta('SIGMA_Habit5', 1, 1.0e-5, None, 0)

    INTER_Sub_1 = Beta('INTER_Sub_1', 0, None, None, 1)
    INTER_Sub_2 = Beta('INTER_Sub_2', 0, None, None, 0)
    INTER_Sub_3 = Beta('INTER_Sub_3', 0, None, None, 0)
    INTER_Sub_4 = Beta('INTER_Sub_4', 0, None, None, 0)

    B_Sub1 = Beta('B_Sub1', 1, None, None, 1)
    B_Sub2 = Beta('B_Sub2', 1, None, None, 0)
    B_Sub3 = Beta('B_Sub3', 1, None, None, 0)
    B_Sub4 = Beta('B_Sub4', 1, None, None, 0)

    SIGMA_Sub1 = Beta('SIGMA_Sub1', 1, 1.0e-5, None, 1)
    SIGMA_Sub2 = Beta('SIGMA_Sub2', 1, 1.0e-5, None, 0)
    SIGMA_Sub3 = Beta('SIGMA_Sub3', 1, 1.0e-5, None, 0)
    SIGMA_Sub4 = Beta('SIGMA_Sub4', 1, 1.0e-5, None, 0)

    #* latent variables
    BETA_Envi = Beta('BETA_Envi', 0, None, None, 0)
    BETA_Press = Beta('BETA_Press', 0, None, None, 0)
    BETA_Habit = Beta('BETA_Habit', 0, None, None, 0)
    BETA_Att = Beta('BETA_Att', 0, None, None, 0)
    BETA_Sub = Beta('BETA_Sub', 0, None, None, 0)
    BETA_Con = Beta('BETA_Con', 0, None, None, 0)
    BETA_Unsafe = Beta('BETA_Unsafe', 0, None, None, 0)
    BETA_Deli = Beta('BETA_Deli', 0, None, None, 0)

    #* choice model coefficient
    BETA_Volume = Beta('BETA_Volume', 0, None, None, 0)
    BETA_Time= Beta('BETA_Time', 0, None, None, 0)
    BETA_Weather= Beta('BETA_Weather', 0, None, None, 0)
    BETA_Location = Beta('BETA_Location', 0, None, None, 0)
    BETA_RemainTime = Beta('BETA_RemainTime', 0, None, None, 0)
    BETA_CauseUnsafe = Beta('BETA_CauseUnsafe', 0, None, None, 0)

    BETA_deliVolume_low = Beta('BETA_deliVolume_low', 0, None, None, 0)
    BETA_deliVolume_high = Beta('BETA_deliVolume_high', 0, None, None, 0)

    BETA_deliTime_morning = Beta('BETA_deliTime_morning', 0, None, None, 0)
    BETA_deliTime_noon = Beta('BETA_deliTime_noon', 0, None, None, 0)
    BETA_deliTime_evening = Beta('BETA_deliTime_evening', 0, None, None, 0)
    BETA_deliTime_afternoon = Beta('BETA_deliTime_afternoon', 0, None, None, 0)
    BETA_deliTime_night = Beta('BETA_deliTime_night', 0, None, None, 0)

    BETA_deliWeather_heavy = Beta('BETA_deliWeather_heavy', 0, None, None, 0)
    BETA_deliWeather_good = Beta('BETA_deliWeather_good', 0, None, None, 0)
    BETA_deliWeather_spit = Beta('BETA_deliWeather_spit', 0, None, None, 0)

    BETA_deliLocation_inter = Beta('BETA_deliLocation_inter', 0, None, None, 0)
    BETA_deliLocation_straight = Beta('BETA_deliLocation_straight', 0, None, None, 0)
    BETA_deliLocation_curve = Beta('BETA_deliLocation_curve', 0, None, None, 0)
    BETA_deliLocation_pede = Beta('BETA_deliLocation_pede', 0, None, None, 0)

    BETA_RemainTime_short = Beta('BETA_RemainTime_short', 0, None, None, 0)

    BETA_CauseUnsafe_overspeed = Beta('BETA_CauseUnsafe_overspeed', 0, None, None, 0)
    BETA_CauseUnsafe_breakrule = Beta('BETA_CauseUnsafe_breakrule', 0, None, None, 0)
    BETA_CauseUnsafe_drowsy = Beta('BETA_CauseUnsafe_drowsy', 0, None, None, 0)
    BETA_CauseUnsafe_emergency = Beta('BETA_CauseUnsafe_emergency', 0, None, None, 0)
    BETA_CauseUnsafe_phone = Beta('BETA_CauseUnsafe_phone', 0, None, None, 0)
    BETA_CauseUnsafe_without = Beta('BETA_CauseUnsafe_without', 0, None, None, 0)

    BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)
    BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)
    # BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)
    BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)
    BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)

    BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)

    BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)
    BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)
    # BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)
    BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)
    BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)

    BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)
    BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)
    BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)
    BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)
    BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)

    BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)
    BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)
    BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)
    BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)
    BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)

    BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)
    BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)
    BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)
    BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)
    BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)

    BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)
    BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)
    BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)
    BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)
    BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)

    BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)
    BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)
    BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)
    BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)
    BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)

    BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)

    BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)
    BETA_age_between_24_35 = Beta('BETA_age_between_24_35', 0.0, None, None, 0)
    BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)

    BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)
    BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)

    BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)

    BETA_children = Beta('BETA_children', 0.0, None, None, 0)

    BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)
    BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)
    BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)

    BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)
    BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)
    BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)

    BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)
    BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)
    BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)
    BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)

    BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)
    BETA_disposableincome_more_10000 = Beta('BETA_disposableincome_more_10000', 0.0, None, None, 0)

    BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)

    #* structual equations
    sigma_1 = Beta('sigma_1', 1, None, None, 0)
    omega_1 = bioDraws('omega_1', 'NORMAL_MLHS')

    # coef_citytime_Att * citytime_less_3 + \
    # coef_age_Att  * age_less_24 + \
    Att = coef_intercept_Att + \
        coef_age_Att  * age_less_24 + \
        coef_gender_Att  * gender_female + \
        coef_edu_Att * edu_less_junior + \
        coef_marriage_Att * marriage_not + \
        coef_children_Att * children + \
        coef_family_Att * family_big + \
        coef_monthincome_Att * monthincome_less_4000 + \
        coef_disposableincome_Att * disposableincome_more_10000 + \
        omega_1 * sigma_1

    MODEL_Att2 = INTER_Att2 + B_Att2 * Att
    MODEL_Att3 = INTER_Att3 + B_Att3 * Att
    MODEL_Att4 = INTER_Att4 + B_Att4 * Att
    MODEL_Att5 = INTER_Att5 + B_Att5 * Att
    MODEL_Att6 = INTER_Att6 + B_Att6 * Att

    sigma_2 = Beta('sigma_2', 1, None, None, 0)
    omega_2 = bioDraws('omega_2', 'NORMAL_MLHS')

        # coef_citytime_Unsafe * citytime_less_3 + \
    Unsafe = coef_intercept_Unsafe + \
        coef_gender_Unsafe  * gender_female + \
        coef_age_Unsafe  * age_less_24 + \
        coef_edu_Unsafe * edu_less_junior + \
        coef_marriage_Unsafe * marriage_not + \
        coef_children_Unsafe * children + \
        coef_family_Unsafe * family_big + \
        coef_monthincome_Unsafe * monthincome_less_4000 + \
        coef_disposableincome_Unsafe * disposableincome_more_10000 + \
        omega_2 * sigma_2

    MODEL_Unsafe1 = INTER_Unsafe1 + B_Unsafe1 * Unsafe
    MODEL_Unsafe2 = INTER_Unsafe2 + B_Unsafe2 * Unsafe
    MODEL_Unsafe3 = INTER_Unsafe3 + B_Unsafe3 * Unsafe
    MODEL_Unsafe4 = INTER_Unsafe4 + B_Unsafe4 * Unsafe
    MODEL_Unsafe5 = INTER_Unsafe5 + B_Unsafe5 * Unsafe

    sigma_3 = Beta('sigma_3', 1, None, None, 0)
    omega_3 = bioDraws('omega_3', 'NORMAL_MLHS')

        # coef_citytime_Envi * citytime_less_3 + \
    Envi = coef_intercept_Envi + \
        coef_gender_Envi  * gender_female + \
        coef_age_Envi  * age_less_24 + \
        coef_edu_Envi * edu_less_junior + \
        coef_marriage_Envi * marriage_not + \
        coef_children_Envi * children + \
        coef_family_Envi * family_big + \
        coef_monthincome_Envi * monthincome_less_4000 + \
        coef_disposableincome_Envi * disposableincome_more_10000 + \
        omega_3 * sigma_3

    MODEL_Envi1 = INTER_Envi1 + B_Envi1 * Envi
    MODEL_Envi2 = INTER_Envi2 + B_Envi2 * Envi
    MODEL_Envi3 = INTER_Envi3 + B_Envi3 * Envi
    MODEL_Envi4 = INTER_Envi4 + B_Envi4 * Envi
    MODEL_Envi5 = INTER_Envi5 + B_Envi5 * Envi

    sigma_4 = Beta('sigma_4', 1, None, None, 0)
    omega_4 = bioDraws('omega_4', 'NORMAL_MLHS')
    
        # coef_citytime_Press * citytime_less_3 + \
    Press = coef_intercept_Press + \
        coef_gender_Press  * gender_female + \
        coef_age_Press  * age_less_24 + \
        coef_edu_Press * edu_less_junior + \
        coef_marriage_Press * marriage_not + \
        coef_children_Press * children + \
        coef_family_Press * family_big + \
        coef_monthincome_Press * monthincome_less_4000 + \
        coef_disposableincome_Press * disposableincome_more_10000 + \
        omega_4 * sigma_4

    MODEL_Press1 = INTER_Press1 + B_Press1 * Press
    MODEL_Press2 = INTER_Press2 + B_Press2 * Press
    MODEL_Press3 = INTER_Press3 + B_Press3 * Press
    MODEL_Press4 = INTER_Press4 + B_Press4 * Press
    
    sigma_5 = Beta('sigma_5', 1, None, None, 0)
    omega_5 = bioDraws('omega_5', 'NORMAL_MLHS')

        # coef_citytime_Con * citytime_less_3 + \
    Con = coef_intercept_Con + \
        coef_gender_Con  * gender_female + \
        coef_age_Con  * age_less_24 + \
        coef_edu_Con * edu_less_junior + \
        coef_marriage_Con * marriage_not + \
        coef_children_Con * children + \
        coef_family_Con * family_big + \
        coef_monthincome_Con * monthincome_less_4000 + \
        coef_disposableincome_Con * disposableincome_more_10000 + \
        omega_5 * sigma_5

    MODEL_Con1 = INTER_Con1 + B_Con1 * Con
    MODEL_Con2 = INTER_Con2 + B_Con2 * Con
    MODEL_Con3 = INTER_Con3 + B_Con3 * Con
    MODEL_Con4 = INTER_Con4 + B_Con4 * Con

    sigma_6 = Beta('sigma_6', 1, None, None, 0)
    omega_6 = bioDraws('omega_6', 'NORMAL_MLHS')

        # coef_citytime_Deli * citytime_less_3 + \
    Deli = coef_intercept_Deli + \
        coef_gender_Deli  * gender_female + \
        coef_age_Deli  * age_less_24 + \
        coef_edu_Deli * edu_less_junior + \
        coef_marriage_Deli * marriage_not + \
        coef_children_Deli * children + \
        coef_family_Deli * family_big + \
        coef_monthincome_Deli * monthincome_less_4000 + \
        coef_disposableincome_Deli * disposableincome_more_10000 + \
        omega_6 * sigma_6

    MODEL_Deli1 = INTER_Deli_1 + B_Deli_1 * Deli
    MODEL_Deli4 = INTER_Deli_4 + B_Deli_4 * Deli
    MODEL_Deli5 = INTER_Deli_5 + B_Deli_5 * Deli
    MODEL_Deli6 = INTER_Deli_6 + B_Deli_6 * Deli

    sigma_7 = Beta('sigma_7', 1, None, None, 0)
    omega_7 = bioDraws('omega_7', 'NORMAL_MLHS')

        # coef_citytime_Habit * citytime_less_3 + \
    Habit = coef_intercept_Habit + \
        coef_gender_Habit  * gender_female + \
        coef_age_Habit  * age_less_24 + \
        coef_edu_Habit * edu_less_junior + \
        coef_marriage_Habit * marriage_not + \
        coef_children_Habit * children + \
        coef_family_Habit * family_big + \
        coef_monthincome_Habit * monthincome_less_4000 + \
        coef_disposableincome_Habit * disposableincome_more_10000 + \
        omega_7 * sigma_7

    MODEL_Habit1 = INTER_Habit_1 + B_Habit1 * Habit
    MODEL_Habit2 = INTER_Habit_2 + B_Habit2 * Habit
    MODEL_Habit3 = INTER_Habit_3 + B_Habit3 * Habit
    MODEL_Habit4 = INTER_Habit_4 + B_Habit4 * Habit
    MODEL_Habit5 = INTER_Habit_5 + B_Habit5 * Habit

    sigma_8 = Beta('sigma_8', 1, None, None, 0)
    omega_8 = bioDraws('omega_8', 'NORMAL_MLHS')
    
        # coef_citytime_Sub * citytime_less_3 + \
    Sub = coef_intercept_Sub + \
        coef_gender_Sub  * gender_female + \
        coef_age_Sub  * age_less_24 + \
        coef_edu_Sub * edu_less_junior + \
        coef_marriage_Sub * marriage_not + \
        coef_children_Sub * children + \
        coef_family_Sub * family_big + \
        coef_monthincome_Sub * monthincome_less_4000 + \
        coef_disposableincome_Sub * disposableincome_more_10000 + \
        omega_8 * sigma_8

    MODEL_Sub1 = INTER_Sub_1 + B_Sub1 * Sub
    MODEL_Sub2 = INTER_Sub_2 + B_Sub2 * Sub
    MODEL_Sub3 = INTER_Sub_3 + B_Sub3 * Sub
    MODEL_Sub4 = INTER_Sub_4 + B_Sub4 * Sub

    # As the measurements are using a Likert scale with M = 5 levels, we deﬁne 4 parameters
    delta_1 = Beta('delta_1', 0.1, 1.0e-5, None, 0)
    delta_2 = Beta('delta_2', 0.2, 1.0e-5, None, 0)
    tau_1 = -delta_1 - delta_2
    tau_2 = -delta_1
    tau_3 = delta_1
    tau_4 = delta_1 + delta_2

    #* measurement equations
    Att2_tau_1 = (tau_1 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_2 = (tau_2 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_3 = (tau_3 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_4 = (tau_4 - MODEL_Att2) / SIGMA_Att2
    IndiAtt2 = {
        1: bioNormalCdf(Att2_tau_1),
        2: bioNormalCdf(Att2_tau_2) - bioNormalCdf(Att2_tau_1),
        3: bioNormalCdf(Att2_tau_3) - bioNormalCdf(Att2_tau_2),
        4: bioNormalCdf(Att2_tau_4) - bioNormalCdf(Att2_tau_3),
        5: 1 - bioNormalCdf(Att2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att2 = Elem(IndiAtt2, Attitude2)

    Att3_tau_1 = (tau_1 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_2 = (tau_2 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_3 = (tau_3 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_4 = (tau_4 - MODEL_Att3) / SIGMA_Att3
    IndiAtt3 = {
        1: bioNormalCdf(Att3_tau_1),
        2: bioNormalCdf(Att3_tau_2) - bioNormalCdf(Att3_tau_1),
        3: bioNormalCdf(Att3_tau_3) - bioNormalCdf(Att3_tau_2),
        4: bioNormalCdf(Att3_tau_4) - bioNormalCdf(Att3_tau_3),
        5: 1 - bioNormalCdf(Att3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att3 = Elem(IndiAtt3, Attitude3)

    Att4_tau_1 = (tau_1 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_2 = (tau_2 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_3 = (tau_3 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_4 = (tau_4 - MODEL_Att4) / SIGMA_Att4
    IndiAtt4 = {
        1: bioNormalCdf(Att4_tau_1),
        2: bioNormalCdf(Att4_tau_2) - bioNormalCdf(Att4_tau_1),
        3: bioNormalCdf(Att4_tau_3) - bioNormalCdf(Att4_tau_2),
        4: bioNormalCdf(Att4_tau_4) - bioNormalCdf(Att4_tau_3),
        5: 1 - bioNormalCdf(Att4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att4 = Elem(IndiAtt4, Attitude4)

    Att5_tau_1 = (tau_1 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_2 = (tau_2 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_3 = (tau_3 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_4 = (tau_4 - MODEL_Att5) / SIGMA_Att5
    IndiAtt5 = {
        1: bioNormalCdf(Att5_tau_1),
        2: bioNormalCdf(Att5_tau_2) - bioNormalCdf(Att5_tau_1),
        3: bioNormalCdf(Att5_tau_3) - bioNormalCdf(Att5_tau_2),
        4: bioNormalCdf(Att5_tau_4) - bioNormalCdf(Att5_tau_3),
        5: 1 - bioNormalCdf(Att5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att5 = Elem(IndiAtt5, Attitude5)

    Att6_tau_1 = (tau_1 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_2 = (tau_2 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_3 = (tau_3 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_4 = (tau_4 - MODEL_Att6) / SIGMA_Att6
    IndiAtt6 = {
        1: bioNormalCdf(Att6_tau_1),
        2: bioNormalCdf(Att6_tau_2) - bioNormalCdf(Att6_tau_1),
        3: bioNormalCdf(Att6_tau_3) - bioNormalCdf(Att6_tau_2),
        4: bioNormalCdf(Att6_tau_4) - bioNormalCdf(Att6_tau_3),
        5: 1 - bioNormalCdf(Att6_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att6 = Elem(IndiAtt6, Attitude6)

    Unsafe1_tau_1 = (tau_1 - MODEL_Unsafe1) / SIGMA_Unsafe1
    Unsafe1_tau_2 = (tau_2 - MODEL_Unsafe1) / SIGMA_Unsafe1
    Unsafe1_tau_3 = (tau_3 - MODEL_Unsafe1) / SIGMA_Unsafe1
    Unsafe1_tau_4 = (tau_4 - MODEL_Unsafe1) / SIGMA_Unsafe1
    IndiUnsafe1 = {
        1: bioNormalCdf(Unsafe1_tau_1),
        2: bioNormalCdf(Unsafe1_tau_2) - bioNormalCdf(Unsafe1_tau_1),
        3: bioNormalCdf(Unsafe1_tau_3) - bioNormalCdf(Unsafe1_tau_2),
        4: bioNormalCdf(Unsafe1_tau_4) - bioNormalCdf(Unsafe1_tau_3),
        5: 1 - bioNormalCdf(Unsafe1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Unsafe1 = Elem(IndiUnsafe1, Unsafe1)

    Unsafe2_tau_1 = (tau_1 - MODEL_Unsafe2) / SIGMA_Unsafe2
    Unsafe2_tau_2 = (tau_2 - MODEL_Unsafe2) / SIGMA_Unsafe2
    Unsafe2_tau_3 = (tau_3 - MODEL_Unsafe2) / SIGMA_Unsafe2
    Unsafe2_tau_4 = (tau_4 - MODEL_Unsafe2) / SIGMA_Unsafe2
    IndiUnsafe2 = {
        1: bioNormalCdf(Unsafe2_tau_1),
        2: bioNormalCdf(Unsafe2_tau_2) - bioNormalCdf(Unsafe2_tau_1),
        3: bioNormalCdf(Unsafe2_tau_3) - bioNormalCdf(Unsafe2_tau_2),
        4: bioNormalCdf(Unsafe2_tau_4) - bioNormalCdf(Unsafe2_tau_3),
        5: 1 - bioNormalCdf(Unsafe2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Unsafe2 = Elem(IndiUnsafe2, Unsafe2)

    Unsafe3_tau_1 = (tau_1 - MODEL_Unsafe3) / SIGMA_Unsafe3
    Unsafe3_tau_2 = (tau_2 - MODEL_Unsafe3) / SIGMA_Unsafe3
    Unsafe3_tau_3 = (tau_3 - MODEL_Unsafe3) / SIGMA_Unsafe3
    Unsafe3_tau_4 = (tau_4 - MODEL_Unsafe3) / SIGMA_Unsafe3
    IndiUnsafe3 = {
        1: bioNormalCdf(Unsafe3_tau_1),
        2: bioNormalCdf(Unsafe3_tau_2) - bioNormalCdf(Unsafe3_tau_1),
        3: bioNormalCdf(Unsafe3_tau_3) - bioNormalCdf(Unsafe3_tau_2),
        4: bioNormalCdf(Unsafe3_tau_4) - bioNormalCdf(Unsafe3_tau_3),
        5: 1 - bioNormalCdf(Unsafe3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Unsafe3 = Elem(IndiUnsafe3, Unsafe3)

    Unsafe4_tau_1 = (tau_1 - MODEL_Unsafe4) / SIGMA_Unsafe4
    Unsafe4_tau_2 = (tau_2 - MODEL_Unsafe4) / SIGMA_Unsafe4
    Unsafe4_tau_3 = (tau_3 - MODEL_Unsafe4) / SIGMA_Unsafe4
    Unsafe4_tau_4 = (tau_4 - MODEL_Unsafe4) / SIGMA_Unsafe4
    IndiUnsafe4 = {
        1: bioNormalCdf(Unsafe4_tau_1),
        2: bioNormalCdf(Unsafe4_tau_2) - bioNormalCdf(Unsafe4_tau_1),
        3: bioNormalCdf(Unsafe4_tau_3) - bioNormalCdf(Unsafe4_tau_2),
        4: bioNormalCdf(Unsafe4_tau_4) - bioNormalCdf(Unsafe4_tau_3),
        5: 1 - bioNormalCdf(Unsafe4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Unsafe4 = Elem(IndiUnsafe4, Unsafe4)

    Unsafe5_tau_1 = (tau_1 - MODEL_Unsafe5) / SIGMA_Unsafe5
    Unsafe5_tau_2 = (tau_2 - MODEL_Unsafe5) / SIGMA_Unsafe5
    Unsafe5_tau_3 = (tau_3 - MODEL_Unsafe5) / SIGMA_Unsafe5
    Unsafe5_tau_4 = (tau_4 - MODEL_Unsafe5) / SIGMA_Unsafe5
    IndiUnsafe5 = {
        1: bioNormalCdf(Unsafe5_tau_1),
        2: bioNormalCdf(Unsafe5_tau_2) - bioNormalCdf(Unsafe5_tau_1),
        3: bioNormalCdf(Unsafe5_tau_3) - bioNormalCdf(Unsafe5_tau_2),
        4: bioNormalCdf(Unsafe5_tau_4) - bioNormalCdf(Unsafe5_tau_3),
        5: 1 - bioNormalCdf(Unsafe5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Unsafe5 = Elem(IndiUnsafe5, Unsafe5)

    Envi1_tau_1 = (tau_1 - MODEL_Envi1) / SIGMA_Envi1
    Envi1_tau_2 = (tau_2 - MODEL_Envi1) / SIGMA_Envi1
    Envi1_tau_3 = (tau_3 - MODEL_Envi1) / SIGMA_Envi1
    Envi1_tau_4 = (tau_4 - MODEL_Envi1) / SIGMA_Envi1
    IndiEnvi1 = {
        1: bioNormalCdf(Envi1_tau_1),
        2: bioNormalCdf(Envi1_tau_2) - bioNormalCdf(Envi1_tau_1),
        3: bioNormalCdf(Envi1_tau_3) - bioNormalCdf(Envi1_tau_2),
        4: bioNormalCdf(Envi1_tau_4) - bioNormalCdf(Envi1_tau_3),
        5: 1 - bioNormalCdf(Envi1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Envi1 = Elem(IndiEnvi1, Envi1)

    Envi2_tau_1 = (tau_1 - MODEL_Envi2) / SIGMA_Envi2
    Envi2_tau_2 = (tau_2 - MODEL_Envi2) / SIGMA_Envi2
    Envi2_tau_3 = (tau_3 - MODEL_Envi2) / SIGMA_Envi2
    Envi2_tau_4 = (tau_4 - MODEL_Envi2) / SIGMA_Envi2
    IndiEnvi2 = {
        1: bioNormalCdf(Envi2_tau_1),
        2: bioNormalCdf(Envi2_tau_2) - bioNormalCdf(Envi2_tau_1),
        3: bioNormalCdf(Envi2_tau_3) - bioNormalCdf(Envi2_tau_2),
        4: bioNormalCdf(Envi2_tau_4) - bioNormalCdf(Envi2_tau_3),
        5: 1 - bioNormalCdf(Envi2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Envi2 = Elem(IndiEnvi2, Envi2)

    Envi3_tau_1 = (tau_1 - MODEL_Envi3) / SIGMA_Envi3
    Envi3_tau_2 = (tau_2 - MODEL_Envi3) / SIGMA_Envi3
    Envi3_tau_3 = (tau_3 - MODEL_Envi3) / SIGMA_Envi3
    Envi3_tau_4 = (tau_4 - MODEL_Envi3) / SIGMA_Envi3
    IndiEnvi3 = {
        1: bioNormalCdf(Envi3_tau_1),
        2: bioNormalCdf(Envi3_tau_2) - bioNormalCdf(Envi3_tau_1),
        3: bioNormalCdf(Envi3_tau_3) - bioNormalCdf(Envi3_tau_2),
        4: bioNormalCdf(Envi3_tau_4) - bioNormalCdf(Envi3_tau_3),
        5: 1 - bioNormalCdf(Envi3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Envi3 = Elem(IndiEnvi3, Envi3)

    Envi4_tau_1 = (tau_1 - MODEL_Envi4) / SIGMA_Envi4
    Envi4_tau_2 = (tau_2 - MODEL_Envi4) / SIGMA_Envi4
    Envi4_tau_3 = (tau_3 - MODEL_Envi4) / SIGMA_Envi4
    Envi4_tau_4 = (tau_4 - MODEL_Envi4) / SIGMA_Envi4
    IndiEnvi4 = {
        1: bioNormalCdf(Envi4_tau_1),
        2: bioNormalCdf(Envi4_tau_2) - bioNormalCdf(Envi4_tau_1),
        3: bioNormalCdf(Envi4_tau_3) - bioNormalCdf(Envi4_tau_2),
        4: bioNormalCdf(Envi4_tau_4) - bioNormalCdf(Envi4_tau_3),
        5: 1 - bioNormalCdf(Envi4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Envi4 = Elem(IndiEnvi4, Envi4)

    Envi5_tau_1 = (tau_1 - MODEL_Envi5) / SIGMA_Envi5
    Envi5_tau_2 = (tau_2 - MODEL_Envi5) / SIGMA_Envi5
    Envi5_tau_3 = (tau_3 - MODEL_Envi5) / SIGMA_Envi5
    Envi5_tau_4 = (tau_4 - MODEL_Envi5) / SIGMA_Envi5
    IndiEnvi5 = {
        1: bioNormalCdf(Envi5_tau_1),
        2: bioNormalCdf(Envi5_tau_2) - bioNormalCdf(Envi5_tau_1),
        3: bioNormalCdf(Envi5_tau_3) - bioNormalCdf(Envi5_tau_2),
        4: bioNormalCdf(Envi5_tau_4) - bioNormalCdf(Envi5_tau_3),
        5: 1 - bioNormalCdf(Envi5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Envi5 = Elem(IndiEnvi5, Envi5)

    Press1_tau_1 = (tau_1 - MODEL_Press1) / SIGMA_Press1
    Press1_tau_2 = (tau_2 - MODEL_Press1) / SIGMA_Press1
    Press1_tau_3 = (tau_3 - MODEL_Press1) / SIGMA_Press1
    Press1_tau_4 = (tau_4 - MODEL_Press1) / SIGMA_Press1
    IndiPress1 = {
        1: bioNormalCdf(Press1_tau_1),
        2: bioNormalCdf(Press1_tau_2) - bioNormalCdf(Press1_tau_1),
        3: bioNormalCdf(Press1_tau_3) - bioNormalCdf(Press1_tau_2),
        4: bioNormalCdf(Press1_tau_4) - bioNormalCdf(Press1_tau_3),
        5: 1 - bioNormalCdf(Press1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Press1 = Elem(IndiPress1, Pressure1)

    Press2_tau_1 = (tau_1 - MODEL_Press2) / SIGMA_Press2
    Press2_tau_2 = (tau_2 - MODEL_Press2) / SIGMA_Press2
    Press2_tau_3 = (tau_3 - MODEL_Press2) / SIGMA_Press2
    Press2_tau_4 = (tau_4 - MODEL_Press2) / SIGMA_Press2
    IndiPress2 = {
        1: bioNormalCdf(Press2_tau_1),
        2: bioNormalCdf(Press2_tau_2) - bioNormalCdf(Press2_tau_1),
        3: bioNormalCdf(Press2_tau_3) - bioNormalCdf(Press2_tau_2),
        4: bioNormalCdf(Press2_tau_4) - bioNormalCdf(Press2_tau_3),
        5: 1 - bioNormalCdf(Press2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Press2 = Elem(IndiPress2, Pressure2)

    Press3_tau_1 = (tau_1 - MODEL_Press3) / SIGMA_Press3
    Press3_tau_2 = (tau_2 - MODEL_Press3) / SIGMA_Press3
    Press3_tau_3 = (tau_3 - MODEL_Press3) / SIGMA_Press3
    Press3_tau_4 = (tau_4 - MODEL_Press3) / SIGMA_Press3
    IndiPress3 = {
        1: bioNormalCdf(Press3_tau_1),
        2: bioNormalCdf(Press3_tau_2) - bioNormalCdf(Press3_tau_1),
        3: bioNormalCdf(Press3_tau_3) - bioNormalCdf(Press3_tau_2),
        4: bioNormalCdf(Press3_tau_4) - bioNormalCdf(Press3_tau_3),
        5: 1 - bioNormalCdf(Press3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Press3 = Elem(IndiPress3, Pressure3)

    Press4_tau_1 = (tau_1 - MODEL_Press4) / SIGMA_Press4
    Press4_tau_2 = (tau_2 - MODEL_Press4) / SIGMA_Press4
    Press4_tau_3 = (tau_3 - MODEL_Press4) / SIGMA_Press4
    Press4_tau_4 = (tau_4 - MODEL_Press4) / SIGMA_Press4
    IndiPress4 = {
        1: bioNormalCdf(Press4_tau_1),
        2: bioNormalCdf(Press4_tau_2) - bioNormalCdf(Press4_tau_1),
        3: bioNormalCdf(Press4_tau_3) - bioNormalCdf(Press4_tau_2),
        4: bioNormalCdf(Press4_tau_4) - bioNormalCdf(Press4_tau_3),
        5: 1 - bioNormalCdf(Press4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Press4 = Elem(IndiPress4, Pressure4)

    Con1_tau_1 = (tau_1 - MODEL_Con1) / SIGMA_Con1
    Con1_tau_2 = (tau_2 - MODEL_Con1) / SIGMA_Con1
    Con1_tau_3 = (tau_3 - MODEL_Con1) / SIGMA_Con1
    Con1_tau_4 = (tau_4 - MODEL_Con1) / SIGMA_Con1
    IndiCon1 = {
        1: bioNormalCdf(Con1_tau_1),
        2: bioNormalCdf(Con1_tau_2) - bioNormalCdf(Con1_tau_1),
        3: bioNormalCdf(Con1_tau_3) - bioNormalCdf(Con1_tau_2),
        4: bioNormalCdf(Con1_tau_4) - bioNormalCdf(Con1_tau_3),
        5: 1 - bioNormalCdf(Con1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Con1 = Elem(IndiCon1, Control1)

    Con2_tau_1 = (tau_1 - MODEL_Con2) / SIGMA_Con2
    Con2_tau_2 = (tau_2 - MODEL_Con2) / SIGMA_Con2
    Con2_tau_3 = (tau_3 - MODEL_Con2) / SIGMA_Con2
    Con2_tau_4 = (tau_4 - MODEL_Con2) / SIGMA_Con2
    IndiCon2 = {
        1: bioNormalCdf(Con2_tau_1),
        2: bioNormalCdf(Con2_tau_2) - bioNormalCdf(Con2_tau_1),
        3: bioNormalCdf(Con2_tau_3) - bioNormalCdf(Con2_tau_2),
        4: bioNormalCdf(Con2_tau_4) - bioNormalCdf(Con2_tau_3),
        5: 1 - bioNormalCdf(Con2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Con2 = Elem(IndiCon2, Control2)

    Con3_tau_1 = (tau_1 - MODEL_Con3) / SIGMA_Con3
    Con3_tau_2 = (tau_2 - MODEL_Con3) / SIGMA_Con3
    Con3_tau_3 = (tau_3 - MODEL_Con3) / SIGMA_Con3
    Con3_tau_4 = (tau_4 - MODEL_Con3) / SIGMA_Con3
    IndiCon3 = {
        1: bioNormalCdf(Con3_tau_1),
        2: bioNormalCdf(Con3_tau_2) - bioNormalCdf(Con3_tau_1),
        3: bioNormalCdf(Con3_tau_3) - bioNormalCdf(Con3_tau_2),
        4: bioNormalCdf(Con3_tau_4) - bioNormalCdf(Con3_tau_3),
        5: 1 - bioNormalCdf(Con3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Con3 = Elem(IndiCon3, Control3)

    Con4_tau_1 = (tau_1 - MODEL_Con4) / SIGMA_Con4
    Con4_tau_2 = (tau_2 - MODEL_Con4) / SIGMA_Con4
    Con4_tau_3 = (tau_3 - MODEL_Con4) / SIGMA_Con4
    Con4_tau_4 = (tau_4 - MODEL_Con4) / SIGMA_Con4
    IndiCon4 = {
        1: bioNormalCdf(Con4_tau_1),
        2: bioNormalCdf(Con4_tau_2) - bioNormalCdf(Con4_tau_1),
        3: bioNormalCdf(Con4_tau_3) - bioNormalCdf(Con4_tau_2),
        4: bioNormalCdf(Con4_tau_4) - bioNormalCdf(Con4_tau_3),
        5: 1 - bioNormalCdf(Con4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Con4 = Elem(IndiCon4, Control4)

    Deli1_tau_1 = (tau_1 - MODEL_Deli1) / SIGMA_Deli1
    Deli1_tau_2 = (tau_2 - MODEL_Deli1) / SIGMA_Deli1
    Deli1_tau_3 = (tau_3 - MODEL_Deli1) / SIGMA_Deli1
    Deli1_tau_4 = (tau_4 - MODEL_Deli1) / SIGMA_Deli1
    IndiDeli1 = {
        1: bioNormalCdf(Deli1_tau_1),
        2: bioNormalCdf(Deli1_tau_2) - bioNormalCdf(Deli1_tau_1),
        3: bioNormalCdf(Deli1_tau_3) - bioNormalCdf(Deli1_tau_2),
        4: bioNormalCdf(Deli1_tau_4) - bioNormalCdf(Deli1_tau_3),
        5: 1 - bioNormalCdf(Deli1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Deli1 = Elem(IndiDeli1, Deli1)

    Deli6_tau_1 = (tau_1 - MODEL_Deli6) / SIGMA_Deli6
    Deli6_tau_2 = (tau_2 - MODEL_Deli6) / SIGMA_Deli6
    Deli6_tau_3 = (tau_3 - MODEL_Deli6) / SIGMA_Deli6
    Deli6_tau_4 = (tau_4 - MODEL_Deli6) / SIGMA_Deli6
    IndiDeli6 = {
        1: bioNormalCdf(Deli6_tau_1),
        2: bioNormalCdf(Deli6_tau_2) - bioNormalCdf(Deli6_tau_1),
        3: bioNormalCdf(Deli6_tau_3) - bioNormalCdf(Deli6_tau_2),
        4: bioNormalCdf(Deli6_tau_4) - bioNormalCdf(Deli6_tau_3),
        5: 1 - bioNormalCdf(Deli6_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Deli6 = Elem(IndiDeli6, Deli6)

    Deli5_tau_1 = (tau_1 - MODEL_Deli5) / SIGMA_Deli5
    Deli5_tau_2 = (tau_2 - MODEL_Deli5) / SIGMA_Deli5
    Deli5_tau_3 = (tau_3 - MODEL_Deli5) / SIGMA_Deli5
    Deli5_tau_4 = (tau_4 - MODEL_Deli5) / SIGMA_Deli5
    IndiDeli5 = {
        1: bioNormalCdf(Deli5_tau_1),
        2: bioNormalCdf(Deli5_tau_2) - bioNormalCdf(Deli5_tau_1),
        3: bioNormalCdf(Deli5_tau_3) - bioNormalCdf(Deli5_tau_2),
        4: bioNormalCdf(Deli5_tau_4) - bioNormalCdf(Deli5_tau_3),
        5: 1 - bioNormalCdf(Deli5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Deli5 = Elem(IndiDeli5, Deli5)

    Deli4_tau_1 = (tau_1 - MODEL_Deli4) / SIGMA_Deli4
    Deli4_tau_2 = (tau_2 - MODEL_Deli4) / SIGMA_Deli4
    Deli4_tau_3 = (tau_3 - MODEL_Deli4) / SIGMA_Deli4
    Deli4_tau_4 = (tau_4 - MODEL_Deli4) / SIGMA_Deli4
    IndiDeli4 = {
        1: bioNormalCdf(Deli4_tau_1),
        2: bioNormalCdf(Deli4_tau_2) - bioNormalCdf(Deli4_tau_1),
        3: bioNormalCdf(Deli4_tau_3) - bioNormalCdf(Deli4_tau_2),
        4: bioNormalCdf(Deli4_tau_4) - bioNormalCdf(Deli4_tau_3),
        5: 1 - bioNormalCdf(Deli4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Deli4 = Elem(IndiDeli4, Deli4)

    Habit1_tau_1 = (tau_1 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_2 = (tau_2 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_3 = (tau_3 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_4 = (tau_4 - MODEL_Habit1) / SIGMA_Habit1
    IndiHabit1 = {
        1: bioNormalCdf(Habit1_tau_1),
        2: bioNormalCdf(Habit1_tau_2) - bioNormalCdf(Habit1_tau_1),
        3: bioNormalCdf(Habit1_tau_3) - bioNormalCdf(Habit1_tau_2),
        4: bioNormalCdf(Habit1_tau_4) - bioNormalCdf(Habit1_tau_3),
        5: 1 - bioNormalCdf(Habit1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit1 = Elem(IndiHabit1, Habit1)

    Habit2_tau_1 = (tau_1 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_2 = (tau_2 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_3 = (tau_3 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_4 = (tau_4 - MODEL_Habit2) / SIGMA_Habit2
    IndiHabit2 = {
        1: bioNormalCdf(Habit2_tau_1),
        2: bioNormalCdf(Habit2_tau_2) - bioNormalCdf(Habit2_tau_1),
        3: bioNormalCdf(Habit2_tau_3) - bioNormalCdf(Habit2_tau_2),
        4: bioNormalCdf(Habit2_tau_4) - bioNormalCdf(Habit2_tau_3),
        5: 1 - bioNormalCdf(Habit2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit2 = Elem(IndiHabit2, Habit2)

    Habit3_tau_1 = (tau_1 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_2 = (tau_2 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_3 = (tau_3 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_4 = (tau_4 - MODEL_Habit3) / SIGMA_Habit3
    IndiHabit3 = {
        1: bioNormalCdf(Habit3_tau_1),
        2: bioNormalCdf(Habit3_tau_2) - bioNormalCdf(Habit3_tau_1),
        3: bioNormalCdf(Habit3_tau_3) - bioNormalCdf(Habit3_tau_2),
        4: bioNormalCdf(Habit3_tau_4) - bioNormalCdf(Habit3_tau_3),
        5: 1 - bioNormalCdf(Habit3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit3 = Elem(IndiHabit3, Habit3)

    Habit4_tau_1 = (tau_1 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_2 = (tau_2 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_3 = (tau_3 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_4 = (tau_4 - MODEL_Habit4) / SIGMA_Habit4
    IndiHabit4 = {
        1: bioNormalCdf(Habit4_tau_1),
        2: bioNormalCdf(Habit4_tau_2) - bioNormalCdf(Habit4_tau_1),
        3: bioNormalCdf(Habit4_tau_3) - bioNormalCdf(Habit4_tau_2),
        4: bioNormalCdf(Habit4_tau_4) - bioNormalCdf(Habit4_tau_3),
        5: 1 - bioNormalCdf(Habit4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit4 = Elem(IndiHabit4, Habit4)

    Habit5_tau_1 = (tau_1 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_2 = (tau_2 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_3 = (tau_3 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_4 = (tau_4 - MODEL_Habit5) / SIGMA_Habit5
    IndiHabit5 = {
        1: bioNormalCdf(Habit5_tau_1),
        2: bioNormalCdf(Habit5_tau_2) - bioNormalCdf(Habit5_tau_1),
        3: bioNormalCdf(Habit5_tau_3) - bioNormalCdf(Habit5_tau_2),
        4: bioNormalCdf(Habit5_tau_4) - bioNormalCdf(Habit5_tau_3),
        5: 1 - bioNormalCdf(Habit5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit5 = Elem(IndiHabit5, Habit5)

    Sub1_tau_1 = (tau_1 - MODEL_Sub1) / SIGMA_Sub1
    Sub1_tau_2 = (tau_2 - MODEL_Sub1) / SIGMA_Sub1
    Sub1_tau_3 = (tau_3 - MODEL_Sub1) / SIGMA_Sub1
    Sub1_tau_4 = (tau_4 - MODEL_Sub1) / SIGMA_Sub1
    IndiSub1 = {
        1: bioNormalCdf(Sub1_tau_1),
        2: bioNormalCdf(Sub1_tau_2) - bioNormalCdf(Sub1_tau_1),
        3: bioNormalCdf(Sub1_tau_3) - bioNormalCdf(Sub1_tau_2),
        4: bioNormalCdf(Sub1_tau_4) - bioNormalCdf(Sub1_tau_3),
        5: 1 - bioNormalCdf(Sub1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Sub1 = Elem(IndiSub1, Subjective1)

    Sub2_tau_1 = (tau_1 - MODEL_Sub2) / SIGMA_Sub2
    Sub2_tau_2 = (tau_2 - MODEL_Sub2) / SIGMA_Sub2
    Sub2_tau_3 = (tau_3 - MODEL_Sub2) / SIGMA_Sub2
    Sub2_tau_4 = (tau_4 - MODEL_Sub2) / SIGMA_Sub2
    IndiSub2 = {
        1: bioNormalCdf(Sub2_tau_1),
        2: bioNormalCdf(Sub2_tau_2) - bioNormalCdf(Sub2_tau_1),
        3: bioNormalCdf(Sub2_tau_3) - bioNormalCdf(Sub2_tau_2),
        4: bioNormalCdf(Sub2_tau_4) - bioNormalCdf(Sub2_tau_3),
        5: 1 - bioNormalCdf(Sub2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Sub2 = Elem(IndiSub2, Subjective2)

    Sub3_tau_1 = (tau_1 - MODEL_Sub3) / SIGMA_Sub3
    Sub3_tau_2 = (tau_2 - MODEL_Sub3) / SIGMA_Sub3
    Sub3_tau_3 = (tau_3 - MODEL_Sub3) / SIGMA_Sub3
    Sub3_tau_4 = (tau_4 - MODEL_Sub3) / SIGMA_Sub3
    IndiSub3 = {
        1: bioNormalCdf(Sub3_tau_1),
        2: bioNormalCdf(Sub3_tau_2) - bioNormalCdf(Sub3_tau_1),
        3: bioNormalCdf(Sub3_tau_3) - bioNormalCdf(Sub3_tau_2),
        4: bioNormalCdf(Sub3_tau_4) - bioNormalCdf(Sub3_tau_3),
        5: 1 - bioNormalCdf(Sub3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Sub3 = Elem(IndiSub3, Subjective3)

    Sub4_tau_1 = (tau_1 - MODEL_Sub4) / SIGMA_Sub4
    Sub4_tau_2 = (tau_2 - MODEL_Sub4) / SIGMA_Sub4
    Sub4_tau_3 = (tau_3 - MODEL_Sub4) / SIGMA_Sub4
    Sub4_tau_4 = (tau_4 - MODEL_Sub4) / SIGMA_Sub4
    IndiSub4 = {
        1: bioNormalCdf(Sub4_tau_1),
        2: bioNormalCdf(Sub4_tau_2) - bioNormalCdf(Sub4_tau_1),
        3: bioNormalCdf(Sub4_tau_3) - bioNormalCdf(Sub4_tau_2),
        4: bioNormalCdf(Sub4_tau_4) - bioNormalCdf(Sub4_tau_3),
        5: 1 - bioNormalCdf(Sub4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Sub4 = Elem(IndiSub4, Subjective4)


    #* choice model

    if num_idx == 1:
        latent_att = [
        # [Unsafe, Att, Habit], 
        # [Unsafe, Att, Sub], 
        # [Unsafe, Att, Press], 
        # [Unsafe, Att, Envi], 
        # [Unsafe, Att, Habit, Sub], 
        # [Unsafe, Att, Habit, Press], 
        # [Unsafe, Att, Habit, Envi],
        # [Att, Con], 
        # [Att, Sub], 
        # [Att, Press], 
        # [Att, Envi],
        [Att, Habit],
        ]

        latent_att_beta = [
        # [BETA_Unsafe, BETA_Att, BETA_Habit],
        # [BETA_Unsafe, BETA_Att, BETA_Sub],
        # [BETA_Unsafe, BETA_Att, BETA_Press],
        # [BETA_Unsafe, BETA_Att, BETA_Envi],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Sub],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Press],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Envi],
        # [BETA_Att, BETA_Con],
        # [BETA_Att, BETA_Sub],
        # [BETA_Att, BETA_Press],
        # [BETA_Att, BETA_Envi],
        [BETA_Att, BETA_Habit],
        ]

        latent_att_name = [
        # 'Unsafe_Att_Habit', 
        # 'Unsafe_Att_Sub',
        # 'Unsafe_Att_Press',
        # 'Unsafe_Att_Envi',
        # 'Unsafe_Att_Habit_Sub',
        # 'Unsafe_Att_Habit_Press', 
        # 'Unsafe_Att_Habit_Envi',
        # 'Att_Con', 
        # 'Att_Sub',
        # 'Att_Press',
        # 'Att_Envi',
        'Att_Habit',
        ]

        latent_P = [
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Sub1,  P_Sub2, P_Sub3, P_Sub4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Press1,  P_Press2, P_Press3, P_Press4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Envi1, P_Envi2,  P_Envi3, P_Envi4, P_Envi5],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Sub1,  P_Sub2, P_Sub3, P_Sub4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Press1,  P_Press2, P_Press3, P_Press4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Envi1, P_Envi2,  P_Envi3, P_Envi4, P_Envi5],
        # [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
        #  P_Con1,  P_Con2, P_Con3, P_Con4],
        # [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
        #  P_Sub1,  P_Sub2, P_Sub3, P_Sub4],
        # [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
        #  P_Press1,  P_Press2, P_Press3, P_Press4],
        # [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
        #  P_Envi1, P_Envi2,  P_Envi3,  P_Envi4,  P_Envi5],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],
        ]
            
    if num_idx == 2:

        latent_att = [
        # [Unsafe, Att, Habit], 
        # [Unsafe, Att, Deli], 
        # [Unsafe, Att, Con], 
        # [Unsafe, Att, Sub], 
        # [Unsafe, Att, Press], 
        # [Unsafe, Att, Habit, Deli], 
        # [Unsafe, Att, Habit, Con],
        # [Unsafe, Att, Habit, Sub],
        # [Unsafe, Att, Habit, Press]
        [Att, Con], 
        [Att, Sub], 
        [Att, Press], 
        [Att, Envi],
        [Att, Habit],
        ]

        latent_att_beta = [
        # [BETA_Unsafe, BETA_Att, BETA_Habit],
        # [BETA_Unsafe, BETA_Att, BETA_Deli],
        # [BETA_Unsafe, BETA_Att, BETA_Con],
        # [BETA_Unsafe, BETA_Att, BETA_Sub],
        # [BETA_Unsafe, BETA_Att, BETA_Press],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Deli],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Con],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Sub],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Press],
        [BETA_Att, BETA_Con],
        [BETA_Att, BETA_Sub],
        [BETA_Att, BETA_Press],
        [BETA_Att, BETA_Envi],
        [BETA_Att, BETA_Habit],
        ]

        latent_att_name = [
        # 'Unsafe_Att_Habit', 
        # 'Unsafe_Att_Deli',
        # 'Unsafe_Att_Con',
        # 'Unsafe_Att_Sub',
        # 'Unsafe_Att_Press',
        # 'Unsafe_Att_Habit_Deli', 
        # 'Unsafe_Att_Habit_Con',
        # 'Unsafe_Att_Habit_Sub',
        # 'Unsafe_Att_Habit_Press',
        'Att_Con', 
        'Att_Sub',
        'Att_Press',
        'Att_Envi',
        'Att_Habit',
        ]

        latent_P = [
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Deli1, P_Deli4, P_Deli5, P_Deli6],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Con1, P_Con2, P_Con3, P_Con4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Sub1, P_Sub2, P_Sub3, P_Sub4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Press1, P_Press2, P_Press3, P_Press4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Deli1,  P_Deli4, P_Deli5, P_Deli6],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Con1,  P_Con2, P_Con3, P_Con4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Sub1, P_Sub2, P_Sub3, P_Sub4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Press1, P_Press2, P_Press3, P_Press4]
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Con1,  P_Con2, P_Con3, P_Con4],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Sub1,  P_Sub2, P_Sub3, P_Sub4],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Press1,  P_Press2, P_Press3, P_Press4],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Envi1, P_Envi2,  P_Envi3,  P_Envi4,  P_Envi5],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],
        ]

    if num_idx == 3:

        latent_att = [
        # [Unsafe, Att, Habit], 
        # [Unsafe, Att, Deli], 
        # [Unsafe, Att, Con],  
        # [Unsafe, Att, Press],
        # [Unsafe, Att, Sub],
        # [Unsafe, Att, Habit, Deli], 
        # [Unsafe, Att, Habit, Con],
        # [Unsafe, Att, Habit, Press],
        # [Unsafe, Att, Habit, Sub],
        [Att, Con], 
        [Att, Sub], 
        [Att, Press], 
        [Att, Envi],
        [Att, Habit],
        ]

        latent_att_beta = [
        # [BETA_Unsafe, BETA_Att, BETA_Habit],
        # [BETA_Unsafe, BETA_Att, BETA_Deli],
        # [BETA_Unsafe, BETA_Att, BETA_Con],
        # [BETA_Unsafe, BETA_Att, BETA_Press],
        # [BETA_Unsafe, BETA_Att, BETA_Sub],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Deli],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Con],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Press],
        # [BETA_Unsafe, BETA_Att, BETA_Habit, BETA_Sub],
        [BETA_Att, BETA_Con],
        [BETA_Att, BETA_Sub],
        [BETA_Att, BETA_Press],
        [BETA_Att, BETA_Envi],
        [BETA_Att, BETA_Habit],
            
        ]

        latent_att_name = [
        # 'Unsafe_Att_Habit', 
        # 'Unsafe_Att_Deli',
        # 'Unsafe_Att_Con',
        # 'Unsafe_Att_Press',
        # 'Unsafe_Att_Sub',
        # 'Unsafe_Att_Habit_Deli', 
        # 'Unsafe_Att_Habit_Con',
        # 'Unsafe_Att_Habit_Press',
        # 'Unsafe_Att_Habit_Sub',
        'Att_Con', 
        'Att_Sub',
        'Att_Press',
        'Att_Envi',
        'Att_Habit',
        ]

        latent_P = [
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Deli1, P_Deli4, P_Deli5, P_Deli6],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Con1, P_Con2, P_Con3, P_Con4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Press1, P_Press2, P_Press3, P_Press4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Sub1, P_Sub2, P_Sub3, P_Sub4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Deli1,  P_Deli4, P_Deli5, P_Deli6],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Con1,  P_Con2, P_Con3, P_Con4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Press1, P_Press2, P_Press3, P_Press4],
        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Sub1, P_Sub2, P_Sub3, P_Sub4],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Con1,  P_Con2, P_Con3, P_Con4],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Sub1,  P_Sub2, P_Sub3, P_Sub4],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Press1,  P_Press2, P_Press3, P_Press4],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Envi1, P_Envi2,  P_Envi3,  P_Envi4,  P_Envi5],
        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, 
         P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],
        ]

    for i in range(len(latent_att)):
        func_sum = 0
        for j in range(len(latent_att[i])):
            element_name = latent_att[i][j]
            beta_element_name = latent_att_beta[i][j]
            func_sum += element_name * beta_element_name

        

        # V1 = BETA_intercept + \
        #     BETA_deliverytime_short * deliverytime_short + \
        #     BETA_deliverymode_full * deliverymode_full + \
        #     BETA_deliveryposition_short * deliveryposition_short + \
        #     BETA_workingdays_standard * workingdays_standard + \
        #     BETA_workinghours_brief * workinghours_brief + \
        #     BETA_deliveryorders_brief * deliveryorders_brief + \
        #     BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
        #     BETA_deliveryspeed_extended * deliveryspeed_extended + \
        #     BETA_gender_female  * gender_female + \
        #     BETA_age_less_24 * age_less_24 + \
        #     BETA_edu_less_junior * edu_less_junior + \
        #     BETA_marriage * marriage_not + \
        #     BETA_children * children + \
        #     BETA_family_big * family_big + \
        #     BETA_monthincome_less_4000 * monthincome_less_4000 + \
        #     BETA_disposableincome_more_10000 * disposableincome_more_10000 + \
        #     func_sum
            
        V1 = BETA_intercept + \
            BETA_deliverytime_short * deliverytime_short + \
            BETA_deliverymode_full * deliverymode_full + \
            BETA_deliveryposition_extended * deliveryposition_extended + \
            BETA_workingdays_standard * workingdays_standard + \
            BETA_workinghours_brief * workinghours_brief + \
            BETA_deliveryorders_brief * deliveryorders_brief + \
            BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
            BETA_deliveryspeed_extended * deliveryspeed_extended + \
            BETA_gender_female  * gender_female + \
            BETA_age_less_24 * age_less_24 + \
            BETA_edu_less_junior * edu_less_junior + \
            BETA_marriage * marriage_not + \
            BETA_children * children + \
            BETA_family_big * family_big + \
            BETA_monthincome_less_4000 * monthincome_less_4000 + \
            BETA_disposableincome_more_10000 * disposableincome_more_10000 + \
            func_sum

        V2 = 0

        # Associate utility functions with the numbering of alternatives
        V = {1: V1,
            2: V2,
            }

        condprob = models.logit(V, None, UnsafeAccident)

        sublist_product = 1  # Initialize the product for the current sublist to 1
        for element in latent_P[i]:
            sublist_product *= element  # Multiply each element in the sublist
        
        condlike = sublist_product * condprob

        loglike = log(MonteCarlo(condlike))

        # Define level of verbosity
        logger = msg.bioMessage()
        # logger.setSilent()
        # logger.setWarning()
        logger.setGeneral()
        # logger.setDetailed()

        # Create the Biogeme object
        # biogeme = bio.BIOGEME(database, loglike, numberOfDraws = 500)
        biogeme = bio.BIOGEME(database, loglike, numberOfDraws = 500)

        # 获取当前日期和时间
        now = datetime.now()

        # 格式化日期和时间
        date_time = now.strftime("%Y-%m-%d_%H-%M-%S")

        biogeme.modelName = 'accident_iclv_' + latent_att_name[i] + date_time
        biogeme.generate_pickle = False
        biogeme.generatePickle = False

        # Estimate the parameters
        results = biogeme.estimate()

        biogeme.generate_pickle = False
        biogeme.generatePickle = False

        print(f'Estimated betas: {len(results.data.betaValues)}')
        print(f'Final log likelihood: {results.data.logLike:.3f}')
        print(f'Output file: {results.data.htmlFileName}')

        shutil.move(str(results.data.htmlFileName), str('new_questionaire/iclv_mnl_once%s/'%(num_idx)+results.data.htmlFileName))
        # shutil.move(str(results.data.htmlFileName), str('new_questionaire/iclv_mnl_once3/'+results.data.htmlFileName))

        # print(results.short_summary())

        pandas_results = results.getEstimatedParameters()
        # print(pandas_results)

        beta_rows = pandas_results[pandas_results.index.str.startswith('BETA_')]

        # 在筛选的结果中，进一步筛选出 p-value 列小于 0.1 的行
        filtered_rows = beta_rows[beta_rows['p-value'] < 0.1]
        print(filtered_rows)
        # 计算满足条件的行的数量
        count_of_filtered_rows = filtered_rows.shape[0]

        # 打印结果
        print("满足条件的行的数量为:", count_of_filtered_rows)

