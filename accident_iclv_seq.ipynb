{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import sys\n", "from datetime import datetime\n", "import biogeme.messaging as msg\n", "import pandas as pd\n", "import biogeme.database as db\n", "import biogeme.biogeme_logging as blog\n", "from biogeme.models import piecewiseFormula\n", "import biogeme.distributions as dist\n", "from biogeme.results import bioResults\n", "from biogeme import models\n", "import biogeme.biogeme as bio\n", "from biogeme.expressions import Beta, log, Elem, bioNormalCdf, Variable, bioDraws, MonteCarlo, RandomVariable, Integrate\n", "import shutil\n", "import matplotlib.pyplot as plt\n", "\n", "import biogeme.exceptions as excep"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Read the estimates from the structural equation estimation.\n", "\n", "MODELNAME = 'accident_iclv_measure'\n", "try:\n", "    struct_results = bioResults(pickleFile=f'{MODELNAME}.pickle')\n", "except excep.BiogemeError:\n", "    print(\n", "        f'Run first the script {MODELNAME}.py in order to generate the '\n", "        f'file {MODELNAME}.pickle, and move it to the directory saved_results'\n", "    )\n", "    sys.exit()\n", "struct_betas = struct_results.getBetaValues()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["df = pd.read_excel('new_412.xlsx')\n", "# unreli_index = pd.read_excel('问题问卷编号.xlsx', header= None)\n", "# 筛选无效问卷 删除 回答时间小于2min且 Attitude1 和 Attitude5回答一致 的问卷\n", "df = df[df['QeTime'] >= 120]\n", "df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]\n", "\n", "database = db.Database('data', df)\n", "globals().update(database.variables)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["#* variable definition\n", "# 场景属性自变量\n", "deliVolume = Variable('Volume')\n", "deliTime = Variable('Time')\n", "deliWeather = Variable('Weather')\n", "deliLocation = Variable('Location')\n", "RemainTime = Variable('RemainTime')\n", "CauseUnsafe = Variable('CauseUnsafe')\n", "\n", "# 因变量\n", "UnsafeAccident = Variable('UnsafeAccident')\n", "\n", "# 社会经济属性自变量\n", "gender = Variable('gender')\n", "age = Variable('age')\n", "education = Variable('education')\n", "marriage = Variable('marriage')\n", "family = Variable('family')\n", "citytime = Variable('citytime')\n", "monthincome = Variable('monthincome')\n", "disposableincome = Variable('disposableincome')\n", "\n", "# 配送属性自变量\n", "deliverytime = Variable('deliverytime')\n", "deliverymode = Variable('deliverymode')\n", "deliveryposition = Variable('deliveryposition')\n", "workingdays = Variable('workingdays')\n", "workinghours = Variable('workinghours')\n", "deliveryorders = Variable('deliveryorders')\n", "deliveryrange = Variable('deliveryrange')\n", "deliveryspeed = Variable('deliveryspeed')\n", "\n", "# 社会经济属性二分类变量\n", "gender_female = database.DefineVariable('gender_female', gender == 2)\n", "\n", "age_less_24 = database.DefineVariable('age_less_24', age <= 2 )\n", "age_between_24_35 = database.DefineVariable('age_between_24_35', age == 3 )\n", "age_more_35 = database.DefineVariable('age_more_35', age >= 4 )\n", "\n", "edu_less_junior = database.DefineVariable('edu_less_junior', education <= 2 )\n", "edu_more_uni = database.DefineVariable('edu_more_uni', education >= 4 )\n", "\n", "marriage_not = database.DefineVariable('marriage_not', marriage <= 1 )\n", "children = database.DefineVariable('children', ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0) )\n", "\n", "family_small = database.DefineVariable('family_small', family <= 2 )\n", "family_middle = database.DefineVariable('family_middle', (family <= 4) + (family >= 3) > 0 )\n", "family_big = database.DefineVariable('family_big', (family >= 5) &  (family <= 7) )\n", "\n", "citytime_less_3 = database.DefineVariable('citytime_less_3', citytime <= 2 )\n", "citytime_more_6 = database.DefineVariable('citytime_more_6', citytime >= 4 )\n", "citytime_local = database.DefineVariable('citytime_local', citytime == 5 )\n", "\n", "monthincome_less_2000 = database.DefineVariable('monthincome_less_2000', monthincome <= 1 )\n", "monthincome_less_4000 = database.DefineVariable('monthincome_less_4000', monthincome <= 2 )\n", "monthincome_less_6000 = database.DefineVariable('monthincome_less_6000', monthincome <= 3 )\n", "monthincome_more_8000 = database.DefineVariable('monthincome_more_8000', monthincome >= 5 )\n", "monthincome_4000_8000 = database.DefineVariable('monthincome_4000_8000', (monthincome >= 3) & (monthincome <= 4) )\n", "\n", "disposableincome_less_5000 = database.DefineVariable('disposableincome_less_5000', disposableincome <= 1 )\n", "disposableincome_more_10000 = database.DefineVariable('disposableincome_more_10000', disposableincome >= 3 )\n", "\n", "# 场景属性二分类变量\n", "deliVolume_low = database.DefineVariable('deliVolume_low', deliVolume <= 1 )\n", "deliVolume_middle = database.DefineVariable('deliVolume_middle', deliVolume == 2 )\n", "deliVolume_high = database.DefineVariable('deliVolume_high', deliVolume >= 3 )\n", "\n", "deliTime_morning = database.DefineVariable('deliTime_morning', deliTime == 3 )\n", "deliTime_noon = database.DefineVariable('deliTime_noon', deliTime == 1 )\n", "deliTime_evening = database.DefineVariable('deliTime_evening', deliTime == 2 )\n", "deliTime_afternoon = database.DefineVariable('deliTime_afternoon', deliTime == 4 )\n", "deliTime_night = database.DefineVariable('deliTime_night', deliTime >= 5 )\n", "\n", "deliWeather_heavy = database.DefineVariable('deliWeather_heavy', deliWeather == 1 )\n", "deliWeather_good = database.DefineVariable('deliWeather_good', deliWeather == 2 )\n", "deliWeather_spit = database.DefineVariable('deliWeather_spit', deliWeather == 3 )\n", "\n", "deliLocation_inter = database.DefineVariable('deliWeather_inter', deliLocation == 1 )\n", "deliLocation_straight = database.DefineVariable('deliLocation_straight', deliLocation == 2 )\n", "deliLocation_curve = database.DefineVariable('deliLocation_curve', deliLocation == 3 )\n", "deliLocation_pede = database.DefineVariable('deliLocation_pede', deliLocation == 5 )\n", "\n", "RemainTime_short = database.DefineVariable('RemainTime_short', RemainTime <= 1 )\n", "\n", "CauseUnsafe_overspeed = database.DefineVariable('CauseUnsafe_overspeed', CauseUnsafe == 1 )\n", "CauseUnsafe_breakrule = database.DefineVariable('CauseUnsafe_breakrule', CauseUnsafe == 2 )\n", "CauseUnsafe_drowsy = database.DefineVariable('CauseUnsafe_drowsy', CauseUnsafe == 3 )\n", "CauseUnsafe_emergency = database.DefineVariable('CauseUnsafe_emergency', CauseUnsafe == 4 )\n", "CauseUnsafe_phone = database.DefineVariable('CauseUnsafe_phone', CauseUnsafe == 5 )\n", "CauseUnsafe_without = database.DefineVariable('CauseUnsafe_without', CauseUnsafe == 6 )\n", "\n", "# 配送属性二分类变量\n", "deliverytime_brief = database.DefineVariable('deliverytime_brief', deliverytime == 1 )\n", "deliverytime_short = database.DefineVariable('deliverytime_short', deliverytime <= 2 )\n", "deliverytime_extended = database.DefineVariable('deliverytime_extended', deliverytime >= 3 )\n", "deliverytime_prolonged = database.DefineVariable('deliverytime_prolonged', deliverytime  == 4 )\n", "\n", "deliverymode_full = database.DefineVariable('deliverymode_full', deliverymode >= 2 )\n", "\n", "deliveryposition_brief = database.DefineVariable('deliveryposition_brief', deliveryposition <= 1 )\n", "deliveryposition_short = database.DefineVariable('deliveryposition_short', deliveryposition <= 2 )\n", "deliveryposition_extended = database.DefineVariable('deliveryposition_extended', deliveryposition >= 3 )\n", "deliveryposition_prolonged = database.DefineVariable('deliveryposition_prolonged', deliveryposition  >= 4 )\n", "\n", "workingdays_brief = database.DefineVariable('workingdays_brief', workingdays <= 1 )\n", "workingdays_short = database.DefineVariable('workingdays_short', workingdays <= 2 )\n", "workingdays_standard = database.DefineVariable('workingdays_standard', workingdays <= 3 )\n", "workingdays_extended = database.DefineVariable('workingdays_extended', workingdays  >= 5 )\n", "workingdays_prolonged = database.DefineVariable('workingdays_prolonged', workingdays  >= 6 )\n", "\n", "workinghours_brief = database.DefineVariable('workinghours_brief', workinghours <= 1 )\n", "workinghours_short = database.DefineVariable('workinghours_short', workinghours <= 2 )\n", "workinghours_standard = database.DefineVariable('workinghours_standard', workinghours >= 3 )\n", "workinghours_extended = database.DefineVariable('workinghours_extended', workinghours >= 4 )\n", "workinghours_prolonged = database.DefineVariable('workinghours_prolonged', workinghours >= 5 )\n", "\n", "deliveryorders_brief = database.DefineVariable('deliveryorders_brief', deliveryorders <= 1 )\n", "deliveryorders_short = database.DefineVariable('deliveryorders_short', deliveryorders <= 2 )\n", "deliveryorders_standard = database.DefineVariable('deliveryorders_standard', deliveryorders <= 3 )\n", "deliveryorders_extended = database.DefineVariable('deliveryorders_extended', deliveryorders >= 4 )\n", "deliveryorders_prolonged = database.DefineVariable('deliveryorders_prolonged', deliveryorders >= 5 )\n", "\n", "deliveryrange_brief = database.DefineVariable('deliveryrange_brief', deliveryrange <= 1 )\n", "deliveryrange_short = database.DefineVariable('deliveryrange_short', deliveryrange <= 2 )\n", "deliveryrange_standard = database.DefineVariable('deliveryrange_standard', deliveryrange <= 3 )\n", "deliveryrange_extended = database.DefineVariable('deliveryrange_extended', deliveryrange >= 4 )\n", "deliveryrange_prolonged = database.DefineVariable('deliveryrange_prolonged', deliveryrange >= 5 )\n", "\n", "deliveryspeed_brief = database.DefineVariable('deliveryspeed_brief', deliveryspeed <= 1 )\n", "deliveryspeed_short = database.DefineVariable('deliveryspeed_short', deliveryspeed <= 2 )\n", "deliveryspeed_standard = database.DefineVariable('deliveryspeed_standard', deliveryspeed >= 3 )\n", "#!\n", "deliveryspeed_extended = database.DefineVariable('deliveryspeed_extended', deliveryspeed == 4 )\n", "deliveryspeed_prolonged = database.DefineVariable('deliveryspeed_prolonged', deliveryspeed == 5 )\n", "\n", "#* structual coefficient\n", "# Attitude\n", "coef_intercept_Att = Beta('coef_intercept_Att', struct_betas['coef_intercept_Att'], None, None, 0)\n", "coef_gender_Att = Beta('coef_gender_Att', struct_betas['coef_gender_Att'], None, None, 0)\n", "coef_age_Att = Beta('coef_age_Att', struct_betas['coef_age_Att'], None, None, 0)\n", "coef_edu_Att = Beta('coef_edu_Att', struct_betas['coef_edu_Att'], None, None, 0)\n", "coef_marriage_Att = Beta('coef_marriage_Att', struct_betas['coef_marriage_Att'], None, None, 0)\n", "coef_children_Att = Beta('coef_children_Att', struct_betas['coef_children_Att'], None, None, 0)\n", "coef_family_Att = Beta('coef_family_Att', struct_betas['coef_family_Att'], None, None, 0)\n", "coef_monthincome_Att = Beta('coef_monthincome_Att', struct_betas['coef_monthincome_Att'], None, None, 0)\n", "coef_disposableincome_Att = Beta('coef_disposableincome_Att', struct_betas['coef_disposableincome_Att'], None, None, 0)\n", "\n", "# coef_intercept_Att = struct_betas['coef_intercept_Att']\n", "# coef_gender_Att = struct_betas['coef_gender_Att']\n", "# coef_age_Att = struct_betas['coef_age_Att']\n", "# coef_edu_Att = struct_betas['coef_edu_Att']\n", "# coef_marriage_Att = struct_betas['coef_marriage_Att']\n", "# coef_children_Att = struct_betas['coef_children_Att']\n", "# coef_family_Att = struct_betas['coef_family_Att']\n", "# coef_monthincome_Att = struct_betas['coef_monthincome_Att']\n", "# coef_disposableincome_Att = struct_betas['coef_disposableincome_Att']\n", "\n", "# coef_intercept_Att = -1.21\n", "# coef_gender_Att = -0.407\n", "# coef_age_Att = 0.357\n", "# coef_edu_Att = 0.137\n", "# coef_marriage_Att = 0.451\n", "# coef_children_Att = 0.805\n", "# coef_family_Att = -0.559\n", "# coef_monthincome_Att = \t-0.178\n", "# coef_disposableincome_Att = -0.342\n", "\n", "# Habit\n", "coef_intercept_Habit = Beta('coef_intercept_Habit', struct_betas['coef_intercept_Habit'], None, None, 0)\n", "coef_gender_Habit = Beta('coef_gender_Habit', struct_betas['coef_gender_Habit'], None, None, 0)\n", "coef_age_Habit = Beta('coef_age_Habit', struct_betas['coef_age_Habit'], None, None, 0)\n", "coef_edu_Habit = Beta('coef_edu_Habit', struct_betas['coef_edu_Habit'], None, None, 0)\n", "coef_marriage_Habit = Beta('coef_marriage_Habit', struct_betas['coef_marriage_Habit'], None, None, 0)\n", "coef_children_Habit = Beta('coef_children_Habit', struct_betas['coef_children_Habit'], None, None, 0)\n", "coef_family_Habit = Beta('coef_family_Habit', struct_betas['coef_family_Habit'], None, None, 0)\n", "coef_monthincome_Habit = Beta('coef_monthincome_Habit', struct_betas['coef_monthincome_Habit'], None, None, 0)\n", "coef_disposableincome_Habit = Beta('coef_disposableincome_Habit', struct_betas['coef_disposableincome_Habit'], None, None, 0)\n", "\n", "# coef_intercept_Habit = struct_betas['coef_intercept_Habit']\n", "# coef_gender_Habit = struct_betas['coef_gender_Habit']\n", "# coef_age_Habit = struct_betas['coef_age_Habit']\n", "# coef_edu_Habit = struct_betas['coef_edu_Habit']\n", "# coef_marriage_Habit = struct_betas['coef_marriage_Habit']\n", "# coef_children_Habit = struct_betas['coef_children_Habit']\n", "# coef_family_Habit = struct_betas['coef_family_Habit']\n", "# coef_monthincome_Habit = struct_betas['coef_monthincome_Habit']\n", "# coef_disposableincome_Habit = struct_betas['coef_disposableincome_Habit']\n", "\n", "# coef_intercept_Habit = -1.03\n", "# coef_gender_Habit = -0.689\n", "# coef_age_Habit = 0.102\n", "# coef_edu_Habit = -0.0209\n", "# coef_marriage_Habit = 0.681\n", "# coef_children_Habit = 0.558\n", "# coef_family_Habit = -0.0583\n", "# coef_monthincome_Habit = -0.175\n", "# coef_disposableincome_Habit = 0.201\n", "\n", "#* latent variables\n", "# The coefficient of the latent variable should be initialized to something different from zero. \n", "# If not, the algorithm may be trapped in a local optimum, and never change the value.\n", "BETA_Habit = Beta('BETA_Habit', -0.01, None, None, 0)\n", "BETA_Att = Beta('BETA_Att', -0.01, None, None, 0)\n", "\n", "#* choice model coefficient\n", "\n", "BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)\n", "BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)\n", "# BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)\n", "BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)\n", "BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)\n", "\n", "BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)\n", "\n", "BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)\n", "BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)\n", "# BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)\n", "BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)\n", "BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)\n", "\n", "BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)\n", "BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)\n", "BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)\n", "BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)\n", "BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)\n", "\n", "BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)\n", "BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)\n", "BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)\n", "BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)\n", "BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)\n", "\n", "BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)\n", "BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)\n", "BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)\n", "BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)\n", "BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)\n", "\n", "BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)\n", "BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)\n", "BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)\n", "BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)\n", "BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)\n", "\n", "BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)\n", "BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)\n", "BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)\n", "BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)\n", "BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)\n", "\n", "BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)\n", "\n", "BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)\n", "BETA_age_between_24_35 = Beta('BETA_age_between_24_35', 0.0, None, None, 0)\n", "BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)\n", "\n", "BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)\n", "BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)\n", "\n", "BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)\n", "\n", "BETA_children = Beta('BETA_children', 0.0, None, None, 0)\n", "\n", "BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)\n", "BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)\n", "BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)\n", "\n", "BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)\n", "BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)\n", "BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)\n", "\n", "BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)\n", "BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)\n", "BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)\n", "BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)\n", "\n", "BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)\n", "BETA_disposableincome_more_10000 = Beta('BETA_disposableincome_more_10000', 0.0, None, None, 0)\n", "\n", "BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)\n", "\n", "#* structual equations\n", "# sigma_1 = Beta('sigma_1', 1, None, None, 0)\n", "# omega_1 = bioDraws('omega_1', 'NORMAL_MLHS')\n", "omega = RandomVariable('omega')\n", "density = dist.normalpdf(omega)\n", "sigma_s1 = Beta('sigma_s1', 1, None, None, 0)\n", "\n", "Att = coef_intercept_Att + \\\n", "    coef_age_Att  * age_less_24 + \\\n", "    coef_gender_Att  * gender_female + \\\n", "    coef_edu_Att * edu_less_junior + \\\n", "    coef_marriage_Att * marriage_not + \\\n", "    coef_children_Att * children + \\\n", "    coef_family_Att * family_big + \\\n", "    coef_monthincome_Att * monthincome_less_4000 + \\\n", "    coef_disposableincome_Att * disposableincome_more_10000 + \\\n", "    omega * sigma_s1\n", "\n", "# sigma_7 = Beta('sigma_7', 1, None, None, 0)\n", "# omega_7 = bioDraws('omega_7', 'NORMAL_MLHS')\n", "sigma_s2 = Beta('sigma_s2', 1, None, None, 0)\n", "\n", "\n", "Habit = coef_intercept_Habit + \\\n", "    coef_gender_Habit  * gender_female + \\\n", "    coef_age_Habit  * age_less_24 + \\\n", "    coef_edu_Habit * edu_less_junior + \\\n", "    coef_marriage_Habit * marriage_not + \\\n", "    coef_children_Habit * children + \\\n", "    coef_family_Habit * family_big + \\\n", "    coef_monthincome_Habit * monthincome_less_4000 + \\\n", "    coef_disposableincome_Habit * disposableincome_more_10000 + \\\n", "    omega * sigma_s2\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["#* measurement coefficient\n", "INTER_Att2 = Beta('INTER_Att2', 0, None, None, 1)\n", "INTER_Att3 = Beta('INTER_Att3', 0, None, None, 0)\n", "INTER_Att4 = Beta('INTER_Att4', 0, None, None, 0)\n", "INTER_Att5 = Beta('INTER_Att5', 0, None, None, 0)\n", "INTER_Att6 = Beta('INTER_Att6', 0, None, None, 0)\n", "\n", "B_Att2 = Beta('B_Att2', 1, None, None, 1)\n", "B_Att3 = Beta('B_Att3', 1, None, None, 0)\n", "B_Att4 = Beta('B_Att4', 1, None, None, 0)\n", "B_Att5 = Beta('B_Att5', 1, None, None, 0)\n", "B_Att6 = Beta('B_Att6', 1, None, None, 0)\n", "\n", "SIGMA_Att2 = Beta('SIGMA_Att2', 1, 1.0e-5, None, 1)\n", "SIGMA_Att3 = Beta('SIGMA_Att3', 1, 1.0e-5, None, 0)\n", "SIGMA_Att4 = Beta('SIGMA_Att4', 1, 1.0e-5, None, 0)\n", "SIGMA_Att5 = Beta('SIGMA_Att5', 1, 1.0e-5, None, 0)\n", "SIGMA_Att6 = Beta('SIGMA_Att6', 1, 1.0e-5, None, 0)\n", "\n", "INTER_Habit_1 = Beta('INTER_Habit_1', 0, None, None, 1)\n", "INTER_Habit_2 = Beta('INTER_Habit_2', 0, None, None, 0)\n", "INTER_Habit_3 = Beta('INTER_Habit_3', 0, None, None, 0)\n", "INTER_Habit_4 = Beta('INTER_Habit_4', 0, None, None, 0)\n", "INTER_Habit_5 = Beta('INTER_Habit_5', 0, None, None, 0)\n", "\n", "B_Habit1 = Beta('B_Habit1', 1, None, None, 1)\n", "B_Habit2 = Beta('B_Habit2', 1, None, None, 0)\n", "B_Habit3 = Beta('B_Habit3', 1, None, None, 0)\n", "B_Habit4 = Beta('B_Habit4', 1, None, None, 0)\n", "B_Habit5 = Beta('B_Habit5', 1, None, None, 0)\n", "\n", "SIGMA_Habit1 = Beta('SIGMA_Habit1', 1, 1.0e-5, None, 1)\n", "SIGMA_Habit2 = Beta('SIGMA_Habit2', 1, 1.0e-5, None, 0)\n", "SIGMA_Habit3 = Beta('SIGMA_Habit3', 1, 1.0e-5, None, 0)\n", "SIGMA_Habit4 = Beta('SIGMA_Habit4', 1, 1.0e-5, None, 0)\n", "SIGMA_Habit5 = Beta('SIGMA_Habit5', 1, 1.0e-5, None, 0)\n", "\n", "MODEL_Att2 = INTER_Att2 + B_Att2 * Att\n", "MODEL_Att3 = INTER_Att3 + B_Att3 * Att\n", "MODEL_Att4 = INTER_Att4 + B_Att4 * Att\n", "MODEL_Att5 = INTER_Att5 + B_Att5 * Att\n", "MODEL_Att6 = INTER_Att6 + B_Att6 * Att\n", "\n", "MODEL_Habit1 = INTER_Habit_1 + B_Habit1 * Habit\n", "MODEL_Habit2 = INTER_Habit_2 + B_Habit2 * Habit\n", "MODEL_Habit3 = INTER_Habit_3 + B_Habit3 * Habit\n", "MODEL_Habit4 = INTER_Habit_4 + B_Habit4 * Habit\n", "MODEL_Habit5 = INTER_Habit_5 + B_Habit5 * Habit"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# As the measurements are using a Likert scale with M = 5 levels, we deﬁne 4 parameters\n", "delta_1 = Beta('delta_1', 0.1, 1.0e-5, None, 0)\n", "delta_2 = Beta('delta_2', 0.2, 1.0e-5, None, 0)\n", "tau_1 = -delta_1 - delta_2\n", "tau_2 = -delta_1\n", "tau_3 = delta_1\n", "tau_4 = delta_1 + delta_2\n", "\n", "#* measurement equations\n", "Att2_tau_1 = (tau_1 - MODEL_Att2) / SIGMA_Att2\n", "Att2_tau_2 = (tau_2 - MODEL_Att2) / SIGMA_Att2\n", "Att2_tau_3 = (tau_3 - MODEL_Att2) / SIGMA_Att2\n", "Att2_tau_4 = (tau_4 - MODEL_Att2) / SIGMA_Att2\n", "IndiAtt2 = {\n", "    1: bioNormalCdf(Att2_tau_1),\n", "    2: bioNormalCdf(Att2_tau_2) - bioNormalCdf(Att2_tau_1),\n", "    3: bioNormalCdf(Att2_tau_3) - bioNormalCdf(Att2_tau_2),\n", "    4: bioNormalCdf(Att2_tau_4) - bioNormalCdf(Att2_tau_3),\n", "    5: 1 - bioNormalCdf(Att2_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att2 = Elem(IndiAtt2, Attitude2)\n", "\n", "Att3_tau_1 = (tau_1 - MODEL_Att3) / SIGMA_Att3\n", "Att3_tau_2 = (tau_2 - MODEL_Att3) / SIGMA_Att3\n", "Att3_tau_3 = (tau_3 - MODEL_Att3) / SIGMA_Att3\n", "Att3_tau_4 = (tau_4 - MODEL_Att3) / SIGMA_Att3\n", "IndiAtt3 = {\n", "    1: bioNormalCdf(Att3_tau_1),\n", "    2: bioNormalCdf(Att3_tau_2) - bioNormalCdf(Att3_tau_1),\n", "    3: bioNormalCdf(Att3_tau_3) - bioNormalCdf(Att3_tau_2),\n", "    4: bioNormalCdf(Att3_tau_4) - bioNormalCdf(Att3_tau_3),\n", "    5: 1 - bioNormalCdf(Att3_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att3 = Elem(IndiAtt3, Attitude3)\n", "\n", "Att4_tau_1 = (tau_1 - MODEL_Att4) / SIGMA_Att4\n", "Att4_tau_2 = (tau_2 - MODEL_Att4) / SIGMA_Att4\n", "Att4_tau_3 = (tau_3 - MODEL_Att4) / SIGMA_Att4\n", "Att4_tau_4 = (tau_4 - MODEL_Att4) / SIGMA_Att4\n", "IndiAtt4 = {\n", "    1: bioNormalCdf(Att4_tau_1),\n", "    2: bioNormalCdf(Att4_tau_2) - bioNormalCdf(Att4_tau_1),\n", "    3: bioNormalCdf(Att4_tau_3) - bioNormalCdf(Att4_tau_2),\n", "    4: bioNormalCdf(Att4_tau_4) - bioNormalCdf(Att4_tau_3),\n", "    5: 1 - bioNormalCdf(Att4_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att4 = Elem(IndiAtt4, Attitude4)\n", "\n", "Att5_tau_1 = (tau_1 - MODEL_Att5) / SIGMA_Att5\n", "Att5_tau_2 = (tau_2 - MODEL_Att5) / SIGMA_Att5\n", "Att5_tau_3 = (tau_3 - MODEL_Att5) / SIGMA_Att5\n", "Att5_tau_4 = (tau_4 - MODEL_Att5) / SIGMA_Att5\n", "IndiAtt5 = {\n", "    1: bioNormalCdf(Att5_tau_1),\n", "    2: bioNormalCdf(Att5_tau_2) - bioNormalCdf(Att5_tau_1),\n", "    3: bioNormalCdf(Att5_tau_3) - bioNormalCdf(Att5_tau_2),\n", "    4: bioNormalCdf(Att5_tau_4) - bioNormalCdf(Att5_tau_3),\n", "    5: 1 - bioNormalCdf(Att5_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att5 = Elem(IndiAtt5, Attitude5)\n", "\n", "Att6_tau_1 = (tau_1 - MODEL_Att6) / SIGMA_Att6\n", "Att6_tau_2 = (tau_2 - MODEL_Att6) / SIGMA_Att6\n", "Att6_tau_3 = (tau_3 - MODEL_Att6) / SIGMA_Att6\n", "Att6_tau_4 = (tau_4 - MODEL_Att6) / SIGMA_Att6\n", "IndiAtt6 = {\n", "    1: bioNormalCdf(Att6_tau_1),\n", "    2: bioNormalCdf(Att6_tau_2) - bioNormalCdf(Att6_tau_1),\n", "    3: bioNormalCdf(Att6_tau_3) - bioNormalCdf(Att6_tau_2),\n", "    4: bioNormalCdf(Att6_tau_4) - bioNormalCdf(Att6_tau_3),\n", "    5: 1 - bioNormalCdf(Att6_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Att6 = Elem(IndiAtt6, Attitude6)\n", "\n", "Habit1_tau_1 = (tau_1 - MODEL_Habit1) / SIGMA_Habit1\n", "Habit1_tau_2 = (tau_2 - MODEL_Habit1) / SIGMA_Habit1\n", "Habit1_tau_3 = (tau_3 - MODEL_Habit1) / SIGMA_Habit1\n", "Habit1_tau_4 = (tau_4 - MODEL_Habit1) / SIGMA_Habit1\n", "IndiHabit1 = {\n", "    1: bioNormalCdf(Habit1_tau_1),\n", "    2: bioNormalCdf(Habit1_tau_2) - bioNormalCdf(Habit1_tau_1),\n", "    3: bioNormalCdf(Habit1_tau_3) - bioNormalCdf(Habit1_tau_2),\n", "    4: bioNormalCdf(Habit1_tau_4) - bioNormalCdf(Habit1_tau_3),\n", "    5: 1 - bioNormalCdf(Habit1_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit1 = Elem(IndiHabit1, Habit1)\n", "\n", "Habit2_tau_1 = (tau_1 - MODEL_Habit2) / SIGMA_Habit2\n", "Habit2_tau_2 = (tau_2 - MODEL_Habit2) / SIGMA_Habit2\n", "Habit2_tau_3 = (tau_3 - MODEL_Habit2) / SIGMA_Habit2\n", "Habit2_tau_4 = (tau_4 - MODEL_Habit2) / SIGMA_Habit2\n", "IndiHabit2 = {\n", "    1: bioNormalCdf(Habit2_tau_1),\n", "    2: bioNormalCdf(Habit2_tau_2) - bioNormalCdf(Habit2_tau_1),\n", "    3: bioNormalCdf(Habit2_tau_3) - bioNormalCdf(Habit2_tau_2),\n", "    4: bioNormalCdf(Habit2_tau_4) - bioNormalCdf(Habit2_tau_3),\n", "    5: 1 - bioNormalCdf(Habit2_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit2 = Elem(IndiHabit2, Habit2)\n", "\n", "Habit3_tau_1 = (tau_1 - MODEL_Habit3) / SIGMA_Habit3\n", "Habit3_tau_2 = (tau_2 - MODEL_Habit3) / SIGMA_Habit3\n", "Habit3_tau_3 = (tau_3 - MODEL_Habit3) / SIGMA_Habit3\n", "Habit3_tau_4 = (tau_4 - MODEL_Habit3) / SIGMA_Habit3\n", "IndiHabit3 = {\n", "    1: bioNormalCdf(Habit3_tau_1),\n", "    2: bioNormalCdf(Habit3_tau_2) - bioNormalCdf(Habit3_tau_1),\n", "    3: bioNormalCdf(Habit3_tau_3) - bioNormalCdf(Habit3_tau_2),\n", "    4: bioNormalCdf(Habit3_tau_4) - bioNormalCdf(Habit3_tau_3),\n", "    5: 1 - bioNormalCdf(Habit3_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit3 = Elem(IndiHabit3, Habit3)\n", "\n", "Habit4_tau_1 = (tau_1 - MODEL_Habit4) / SIGMA_Habit4\n", "Habit4_tau_2 = (tau_2 - MODEL_Habit4) / SIGMA_Habit4\n", "Habit4_tau_3 = (tau_3 - MODEL_Habit4) / SIGMA_Habit4\n", "Habit4_tau_4 = (tau_4 - MODEL_Habit4) / SIGMA_Habit4\n", "IndiHabit4 = {\n", "    1: bioNormalCdf(Habit4_tau_1),\n", "    2: bioNormalCdf(Habit4_tau_2) - bioNormalCdf(Habit4_tau_1),\n", "    3: bioNormalCdf(Habit4_tau_3) - bioNormalCdf(Habit4_tau_2),\n", "    4: bioNormalCdf(Habit4_tau_4) - bioNormalCdf(Habit4_tau_3),\n", "    5: 1 - bioNormalCdf(Habit4_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit4 = Elem(IndiHabit4, Habit4)\n", "\n", "Habit5_tau_1 = (tau_1 - MODEL_Habit5) / SIGMA_Habit5\n", "Habit5_tau_2 = (tau_2 - MODEL_Habit5) / SIGMA_Habit5\n", "Habit5_tau_3 = (tau_3 - MODEL_Habit5) / SIGMA_Habit5\n", "Habit5_tau_4 = (tau_4 - MODEL_Habit5) / SIGMA_Habit5\n", "IndiHabit5 = {\n", "    1: bioNormalCdf(Habit5_tau_1),\n", "    2: bioNormalCdf(Habit5_tau_2) - bioNormalCdf(Habit5_tau_1),\n", "    3: bioNormalCdf(Habit5_tau_3) - bioNormalCdf(Habit5_tau_2),\n", "    4: bioNormalCdf(Habit5_tau_4) - bioNormalCdf(Habit5_tau_3),\n", "    5: 1 - bioNormalCdf(Habit5_tau_4),\n", "    6: 1.0,\n", "    -1: 1.0,\n", "    -2: 1.0}\n", "\n", "P_Habit5 = Elem(IndiHabit5, Habit5)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimated betas: 65\n", "Final log likelihood: -3229.962\n", "Output file: accident_iclv_full.html\n"]}, {"ename": "ImportError", "evalue": "Missing optional dependency 'Jinja2'. DataFrame.style requires jinja2. Use pip or conda to install Jinja2.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\tevrp\\lib\\site-packages\\pandas\\compat\\_optional.py:135\u001b[0m, in \u001b[0;36mimport_optional_dependency\u001b[1;34m(name, extra, errors, min_version)\u001b[0m\n\u001b[0;32m    134\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 135\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[43mimportlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    136\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\tevrp\\lib\\importlib\\__init__.py:127\u001b[0m, in \u001b[0;36mimport_module\u001b[1;34m(name, package)\u001b[0m\n\u001b[0;32m    126\u001b[0m         level \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m--> 127\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1030\u001b[0m, in \u001b[0;36m_gcd_import\u001b[1;34m(name, package, level)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1007\u001b[0m, in \u001b[0;36m_find_and_load\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:984\u001b[0m, in \u001b[0;36m_find_and_load_unlocked\u001b[1;34m(name, import_)\u001b[0m\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'jinja2'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[1;32mIn[14], line 57\u001b[0m\n\u001b[0;32m     55\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFinal log likelihood: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresults\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mlogLike\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.3f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     56\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mOutput file: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresults\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mhtmlFileName\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m---> 57\u001b[0m \u001b[43mresults\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwriteLaTeX\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     58\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLaTeX file: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresults\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mlatexFileName\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\tevrp\\lib\\site-packages\\biogeme\\results.py:1285\u001b[0m, in \u001b[0;36mbioResults.writeLaTeX\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1283\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mlatexFileName \u001b[38;5;241m=\u001b[39m bf\u001b[38;5;241m.\u001b[39mget_new_file_name(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mmodelName, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtex\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m   1284\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mlatexFileName, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mw\u001b[39m\u001b[38;5;124m'\u001b[39m, encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[1;32m-> 1285\u001b[0m     f\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetLaTeX\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[0;32m   1286\u001b[0m logger\u001b[38;5;241m.\u001b[39minfo(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mResults saved in file \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mlatexFileName\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\tevrp\\lib\\site-packages\\biogeme\\results.py:754\u001b[0m, in \u001b[0;36mbioResults.getLaTeX\u001b[1;34m(self, onlyRobust)\u001b[0m\n\u001b[0;32m    752\u001b[0m \u001b[38;5;66;03m# Need to check for old versions of Pandas.\u001b[39;00m\n\u001b[0;32m    753\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 754\u001b[0m     latex \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[43mtable\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstyle\u001b[49m\u001b[38;5;241m.\u001b[39mformat(formatting)\u001b[38;5;241m.\u001b[39mto_latex()\n\u001b[0;32m    755\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m:\n\u001b[0;32m    756\u001b[0m     latex \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m table\u001b[38;5;241m.\u001b[39mto_latex(float_format\u001b[38;5;241m=\u001b[39mformatting)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\tevrp\\lib\\site-packages\\pandas\\core\\frame.py:1430\u001b[0m, in \u001b[0;36mDataFrame.style\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1410\u001b[0m \u001b[38;5;129m@property\u001b[39m\n\u001b[0;32m   1411\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mstyle\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Styler:\n\u001b[0;32m   1412\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m   1413\u001b[0m \u001b[38;5;124;03m    Returns a Styler object.\u001b[39;00m\n\u001b[0;32m   1414\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1428\u001b[0m \u001b[38;5;124;03m    `Table Visualization <../../user_guide/style.ipynb>`_ for more examples.\u001b[39;00m\n\u001b[0;32m   1429\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m-> 1430\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mio\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mformats\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mstyle\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Styler\n\u001b[0;32m   1432\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m Styler(\u001b[38;5;28mself\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\tevrp\\lib\\site-packages\\pandas\\io\\formats\\style.py:44\u001b[0m\n\u001b[0;32m     40\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mshared_docs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _shared_docs\n\u001b[0;32m     42\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mio\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mformats\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mformat\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m save_to_buffer\n\u001b[1;32m---> 44\u001b[0m jinja2 \u001b[38;5;241m=\u001b[39m \u001b[43mimport_optional_dependency\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mjinja2\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mDataFrame.style requires jinja2.\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     46\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mio\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mformats\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mstyle_render\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     47\u001b[0m     CSSProperties,\n\u001b[0;32m     48\u001b[0m     CSSStyles,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     56\u001b[0m     refactor_levels,\n\u001b[0;32m     57\u001b[0m )\n\u001b[0;32m     59\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m TYPE_CHECKING:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\tevrp\\lib\\site-packages\\pandas\\compat\\_optional.py:138\u001b[0m, in \u001b[0;36mimport_optional_dependency\u001b[1;34m(name, extra, errors, min_version)\u001b[0m\n\u001b[0;32m    136\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[0;32m    137\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m errors \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mraise\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m--> 138\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m(msg)\n\u001b[0;32m    139\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    141\u001b[0m \u001b[38;5;66;03m# Handle submodules: if we have submodule, grab parent module from sys.modules\u001b[39;00m\n", "\u001b[1;31mImportError\u001b[0m: Missing optional dependency 'Jinja2'. DataFrame.style requires jinja2. Use pip or conda to install Jinja2."]}], "source": ["V1 = BETA_intercept + \\\n", "     BETA_age_less_24 * age_less_24 + \\\n", "     BETA_children * children + \\\n", "     BETA_deliverymode_full * deliverymode_full + \\\n", "     BETA_deliveryorders_brief * deliveryorders_brief + \\\n", "     BETA_deliveryposition_extended * deliveryposition_extended+ \\\n", "     BETA_deliveryrange_prolonged * deliveryrange_prolonged + \\\n", "     BETA_deliveryspeed_extended * deliveryspeed_extended + \\\n", "     BETA_deliverytime_short * deliverytime_short + \\\n", "     BETA_disposableincome_more_10000 * disposableincome_more_10000 + \\\n", "     BETA_edu_less_junior * edu_less_junior + \\\n", "     BETA_family_big * family_big + \\\n", "     BETA_gender_female  * gender_female + \\\n", "     BETA_marriage * marriage_not + \\\n", "     BETA_monthincome_less_4000 * monthincome_less_4000 + \\\n", "     BETA_workingdays_standard * workingdays_standard + \\\n", "     BETA_workinghours_brief * workinghours_brief + \\\n", "     BETA_Att * Att + \\\n", "     BETA_Habit * Habit\n", "\n", "V2 = 0\n", "\n", "# Associate utility functions with the numbering of alternatives\n", "V = {1: V1,\n", "     2: V2,\n", "     }\n", "\n", "condprob = models.logit(V, None, UnsafeAccident)\n", "\n", "condlike = (\n", "     P_Habit1 \n", "    * P_Habit2\n", "    * P_Habit3\n", "    * P_Habit4\n", "    * P_Habit5\n", "    * P_Att2\n", "    * P_Att3\n", "    * P_Att4\n", "    * P_Att5\n", "    * P_Att6\n", "    * condprob\n", ")\n", "\n", "# loglike = log(Monte<PERSON>ar<PERSON>(condprob))\n", "\n", "loglike = log(Integrate(condlike * density, 'omega'))\n", "\n", "\n", "the_biogeme = bio.BIOGEME(database, loglike, parameter_file='few_draws.toml')\n", "the_biogeme.modelName = 'accident_iclv_full'\n", "\n", "results = the_biogeme.estimate()\n", "\n", "print(f'Estimated betas: {len(results.data.betaValues)}')\n", "print(f'Final log likelihood: {results.data.logLike:.3f}')\n", "print(f'Output file: {results.data.htmlFileName}')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Value</th>\n", "      <th><PERSON><PERSON>d err</th>\n", "      <th>Rob. t-test</th>\n", "      <th>Rob. p-value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>BETA_Att</th>\n", "      <td>0.249175</td>\n", "      <td>0.203042</td>\n", "      <td>1.227208</td>\n", "      <td>2.197445e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BETA_Habit</th>\n", "      <td>0.998787</td>\n", "      <td>0.184013</td>\n", "      <td>5.427815</td>\n", "      <td>5.704827e-08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BETA_age_less_24</th>\n", "      <td>-0.617782</td>\n", "      <td>0.457505</td>\n", "      <td>-1.350328</td>\n", "      <td>1.769108e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BETA_children</th>\n", "      <td>0.146870</td>\n", "      <td>0.560510</td>\n", "      <td>0.262030</td>\n", "      <td>7.932987e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BETA_deliverymode_full</th>\n", "      <td>-0.202038</td>\n", "      <td>0.536170</td>\n", "      <td>-0.376818</td>\n", "      <td>7.063090e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coef_monthincome_Habit</th>\n", "      <td>-0.035844</td>\n", "      <td>0.187595</td>\n", "      <td>-0.191072</td>\n", "      <td>8.484695e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>delta_1</th>\n", "      <td>0.405043</td>\n", "      <td>0.031138</td>\n", "      <td>13.007914</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>delta_2</th>\n", "      <td>0.973098</td>\n", "      <td>0.066491</td>\n", "      <td>14.635132</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sigma_s1</th>\n", "      <td>1.434073</td>\n", "      <td>0.171559</td>\n", "      <td>8.359058</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sigma_s2</th>\n", "      <td>1.033062</td>\n", "      <td>0.136025</td>\n", "      <td>7.594622</td>\n", "      <td>3.086420e-14</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>65 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                           Value  Rob. Std err  Rob. t-test  Rob. p-value\n", "BETA_Att                0.249175      0.203042     1.227208  2.197445e-01\n", "BETA_Habit              0.998787      0.184013     5.427815  5.704827e-08\n", "BETA_age_less_24       -0.617782      0.457505    -1.350328  1.769108e-01\n", "BETA_children           0.146870      0.560510     0.262030  7.932987e-01\n", "BETA_deliverymode_full -0.202038      0.536170    -0.376818  7.063090e-01\n", "...                          ...           ...          ...           ...\n", "coef_monthincome_Habit -0.035844      0.187595    -0.191072  8.484695e-01\n", "delta_1                 0.405043      0.031138    13.007914  0.000000e+00\n", "delta_2                 0.973098      0.066491    14.635132  0.000000e+00\n", "sigma_s1                1.434073      0.171559     8.359058  0.000000e+00\n", "sigma_s2                1.033062      0.136025     7.594622  3.086420e-14\n", "\n", "[65 rows x 4 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["results.getEstimatedParameters()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results for model accident_iclv_full\n", "Nbr of parameters:\t\t65\n", "Sample size:\t\t\t276\n", "Excluded data:\t\t\t0\n", "Final log likelihood:\t\t-3229.962\n", "Akaike Information Criterion:\t6589.923\n", "Bayesian Information Criterion:\t6825.249\n", "\n"]}], "source": ["print(results.short_summary())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tevrp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}