from datetime import datetime
import pandas as pd
import biogeme.database as db
import biogeme.biogeme as bio
import biogeme.models as models
import biogeme.messaging as msg
import os
from biogeme.expressions import (
    Beta,
    Variable,
    log,
    RandomVariable,
    Integrate,
    Elem,
    bioNormalCdf,
    exp,
    bioDraws,
    MonteCarlo,
)

import shutil
import matplotlib.pyplot as plt

df = pd.read_excel('new_412.xlsx')
unreli_index = pd.read_excel('问题问卷编号.xlsx', header= None)
# 筛选无效问卷 删除 回答时间小于2min且 Attitude1 和 Attitude5回答一致 的问卷
df = df[df['QeTime'] >= 120]
df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]

database = db.Database('data', df)
globals().update(database.variables)

#* variable definition
# 场景属性自变量
deliVolume = Variable('Volume')
deliTime = Variable('Time')
deliWeather = Variable('Weather')
deliLocation = Variable('Location')
RemainTime = Variable('RemainTime')
CauseUnsafe = Variable('CauseUnsafe')

# 因变量
UnsafeAccident = Variable('UnsafeAccident')

# 社会经济属性自变量
gender = Variable('gender')
age = Variable('age')
education = Variable('education')
marriage = Variable('marriage')
family = Variable('family')
citytime = Variable('citytime')
monthincome = Variable('monthincome')
disposableincome = Variable('disposableincome')

# 配送属性自变量
deliverytime = Variable('deliverytime')
deliverymode = Variable('deliverymode')
deliveryposition = Variable('deliveryposition')
workingdays = Variable('workingdays')
workinghours = Variable('workinghours')
deliveryorders = Variable('deliveryorders')
deliveryrange = Variable('deliveryrange')
deliveryspeed = Variable('deliveryspeed')

# 社会经济属性二分类变量
gender_female = database.DefineVariable('gender_female', gender == 2)

age_less_24 = database.DefineVariable('age_less_24', age <= 2)
age_between_24_35 = database.DefineVariable('age_between_24_35', age == 3)
age_more_35 = database.DefineVariable('age_more_35', age >= 4)

edu_less_junior = database.DefineVariable('edu_less_junior', education <= 2)
edu_junior = database.DefineVariable('edu_junior', education == 3)
edu_more_uni = database.DefineVariable('edu_more_uni', education >= 4)

marriage_not = database.DefineVariable('marriage_not', marriage <= 1)

children = database.DefineVariable('children', ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0))

family_small = database.DefineVariable('family_small', family <= 2)
family_middle = database.DefineVariable('family_middle', (family <= 4) + (family >= 3) > 0)
family_big = database.DefineVariable('family_big', family >= 5)

citytime_less_3 = database.DefineVariable('citytime_less_3', citytime <= 2)
citytime_betwen_3_6 = database.DefineVariable('citytime_betwen_3_6', citytime == 3)
citytime_more_6 = database.DefineVariable('citytime_more_6', citytime >= 4)
citytime_local = database.DefineVariable('citytime_local', citytime == 5)

monthincome_less_2000 = database.DefineVariable('monthincome_less_2000', monthincome <= 1)
monthincome_less_4000 = database.DefineVariable('monthincome_less_4000', monthincome <= 2)
monthincome_between_4000_8000 = database.DefineVariable('monthincome_between_4000_8000',( monthincome >= 3)&( monthincome <= 4))
monthincome_less_6000 = database.DefineVariable('monthincome_less_6000', monthincome <= 3)
monthincome_more_8000 = database.DefineVariable('monthincome_more_8000', monthincome >= 5)

disposableincome_less_5000 = database.DefineVariable('disposableincome_less_5000', disposableincome <= 1)
disposableincome_between_5000_10000 = database.DefineVariable('disposableincome_between_5000_10000', disposableincome == 2)
disposableincome_more_10000 = database.DefineVariable('disposableincome_more_10000', disposableincome >= 3)
disposableincome_less_10000 = database.DefineVariable('disposableincome_less_10000', disposableincome <= 2)

# 场景属性二分类变量
deliVolume_low = database.DefineVariable('deliVolume_low', deliVolume <= 1)
deliVolume_middle = database.DefineVariable('deliVolume_middle', deliVolume == 2)
deliVolume_high = database.DefineVariable('deliVolume_high', deliVolume >= 3)

deliTime_morning = database.DefineVariable('deliTime_morning', deliTime == 3)
deliTime_noon = database.DefineVariable('deliTime_noon', deliTime == 1)
deliTime_evening = database.DefineVariable('deliTime_evening', deliTime == 2)
deliTime_afternoon = database.DefineVariable('deliTime_afternoon', deliTime == 4)
deliTime_night = database.DefineVariable('deliTime_night', deliTime >= 5)

deliWeather_heavy = database.DefineVariable('deliWeather_heavy', deliWeather == 1)
deliWeather_good = database.DefineVariable('deliWeather_good', deliWeather == 2)
deliWeather_spit = database.DefineVariable('deliWeather_spit', deliWeather == 3)

deliLocation_inter = database.DefineVariable('deliWeather_inter', deliLocation == 1)
deliLocation_straight = database.DefineVariable('deliLocation_straight', deliLocation == 2)
deliLocation_curve = database.DefineVariable('deliLocation_curve', deliLocation == 3)
deliLocation_pede = database.DefineVariable('deliLocation_pede', deliLocation == 5)

RemainTime_short = database.DefineVariable('RemainTime_short', RemainTime <= 1)

CauseUnsafe_overspeed = database.DefineVariable('CauseUnsafe_overspeed', CauseUnsafe == 1)
CauseUnsafe_breakrule = database.DefineVariable('CauseUnsafe_breakrule', CauseUnsafe == 2)
CauseUnsafe_drowsy = database.DefineVariable('CauseUnsafe_drowsy', CauseUnsafe == 3)
CauseUnsafe_emergency = database.DefineVariable('CauseUnsafe_emergency', CauseUnsafe == 4)
CauseUnsafe_phone = database.DefineVariable('CauseUnsafe_phone', CauseUnsafe == 5)
CauseUnsafe_without = database.DefineVariable('CauseUnsafe_without', CauseUnsafe == 6)

# 配送属性二分类变量
deliverytime_brief = database.DefineVariable('deliverytime_brief', deliverytime == 1)
deliverytime_short = database.DefineVariable('deliverytime_short', deliverytime <= 2)
deliverytime_extended = database.DefineVariable('deliverytime_extended', deliverytime >= 3)
deliverytime_prolonged = database.DefineVariable('deliverytime_prolonged', deliverytime  == 4)

deliverymode_full = database.DefineVariable('deliverymode_full', deliverymode >= 2)

deliveryposition_brief = database.DefineVariable('deliveryposition_brief', deliveryposition <= 1)
deliveryposition_short = database.DefineVariable('deliveryposition_short', deliveryposition <= 2)
deliveryposition_extended = database.DefineVariable('deliveryposition_extended', deliveryposition >= 3)
deliveryposition_prolonged = database.DefineVariable('deliveryposition_prolonged', deliveryposition  >= 4)

workingdays_brief = database.DefineVariable('workingdays_brief', workingdays <= 1)
workingdays_short = database.DefineVariable('workingdays_short', workingdays <= 2)
workingdays_standard = database.DefineVariable('workingdays_standard', workingdays <= 3)
workingdays_extended = database.DefineVariable('workingdays_extended', workingdays  >= 5)
workingdays_prolonged = database.DefineVariable('workingdays_prolonged', workingdays  >= 6)

workinghours_brief = database.DefineVariable('workinghours_brief', workinghours <= 1)
workinghours_short = database.DefineVariable('workinghours_short', workinghours <= 2)
workinghours_standard = database.DefineVariable('workinghours_standard', workinghours >= 3)
workinghours_extended = database.DefineVariable('workinghours_extended', workinghours >= 4)
workinghours_prolonged = database.DefineVariable('workinghours_prolonged', workinghours >= 5)

deliveryorders_brief = database.DefineVariable('deliveryorders_brief', deliveryorders <= 1)
deliveryorders_short = database.DefineVariable('deliveryorders_short', deliveryorders <= 2)
deliveryorders_standard = database.DefineVariable('deliveryorders_standard', deliveryorders <= 3)
deliveryorders_extended = database.DefineVariable('deliveryorders_extended', deliveryorders >= 4)
deliveryorders_prolonged = database.DefineVariable('deliveryorders_prolonged', deliveryorders >= 5)

deliveryrange_brief = database.DefineVariable('deliveryrange_brief', deliveryrange <= 1)
deliveryrange_short = database.DefineVariable('deliveryrange_short', deliveryrange <= 2)
deliveryrange_standard = database.DefineVariable('deliveryrange_standard', deliveryrange <= 3)
deliveryrange_extended = database.DefineVariable('deliveryrange_extended', deliveryrange >= 4)
deliveryrange_prolonged = database.DefineVariable('deliveryrange_prolonged', deliveryrange >= 5)

deliveryspeed_brief = database.DefineVariable('deliveryspeed_brief', deliveryspeed <= 1)
deliveryspeed_short = database.DefineVariable('deliveryspeed_short', deliveryspeed <= 2)
deliveryspeed_standard = database.DefineVariable('deliveryspeed_standard', deliveryspeed >= 3)
deliveryspeed_extended = database.DefineVariable('deliveryspeed_extended', deliveryspeed >= 4)
deliveryspeed_prolonged = database.DefineVariable('deliveryspeed_prolonged', deliveryspeed == 5)

#* choice model coefficient

BETA_Volume = Beta('BETA_Volume', 0, None, None, 0)
BETA_Time= Beta('BETA_Time', 0, None, None, 0)
BETA_Weather= Beta('BETA_Weather', 0, None, None, 0)
BETA_Location = Beta('BETA_Location', 0, None, None, 0)
BETA_RemainTime = Beta('BETA_RemainTime', 0, None, None, 0)
BETA_CauseUnsafe = Beta('BETA_CauseUnsafe', 0, None, None, 0)

BETA_deliVolume_low = Beta('BETA_deliVolume_low', 0, None, None, 0)
BETA_deliVolume_high = Beta('BETA_deliVolume_high', 0, None, None, 0)

BETA_deliTime_morning = Beta('BETA_deliTime_morning', 0, None, None, 0)
BETA_deliTime_noon = Beta('BETA_deliTime_noon', 0, None, None, 0)
BETA_deliTime_evening = Beta('BETA_deliTime_evening', 0, None, None, 0)
BETA_deliTime_afternoon = Beta('BETA_deliTime_afternoon', 0, None, None, 0)
BETA_deliTime_night = Beta('BETA_deliTime_night', 0, None, None, 0)

BETA_deliWeather_heavy = Beta('BETA_deliWeather_heavy', 0, None, None, 0)
BETA_deliWeather_good = Beta('BETA_deliWeather_good', 0, None, None, 0)
BETA_deliWeather_spit = Beta('BETA_deliWeather_spit', 0, None, None, 0)

BETA_deliLocation_inter = Beta('BETA_deliLocation_inter', 0, None, None, 0)
BETA_deliLocation_straight = Beta('BETA_deliLocation_straight', 0, None, None, 0)
BETA_deliLocation_curve = Beta('BETA_deliLocation_curve', 0, None, None, 0)
BETA_deliLocation_pede = Beta('BETA_deliLocation_pede', 0, None, None, 0)

BETA_RemainTime_short = Beta('BETA_RemainTime_short', 0, None, None, 0)

BETA_CauseUnsafe_overspeed = Beta('BETA_CauseUnsafe_overspeed', 0, None, None, 0)
BETA_CauseUnsafe_breakrule = Beta('BETA_CauseUnsafe_breakrule', 0, None, None, 0)
BETA_CauseUnsafe_drowsy = Beta('BETA_CauseUnsafe_drowsy', 0, None, None, 0)
BETA_CauseUnsafe_emergency = Beta('BETA_CauseUnsafe_emergency', 0, None, None, 0)
BETA_CauseUnsafe_phone = Beta('BETA_CauseUnsafe_phone', 0, None, None, 0)
BETA_CauseUnsafe_without = Beta('BETA_CauseUnsafe_without', 0, None, None, 0)

BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)
BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)
# BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)
BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)
BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)

BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)

BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)
BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)
# BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)
BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)
BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)

BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)
BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)
BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)
BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)
BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)

BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)
BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)
BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)
BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)
BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)

BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)
BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)
BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)
BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)
BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)

BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)
BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)
BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)
BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)
BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)

BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)
BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)
BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)
BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)
BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)

BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)

BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)
BETA_age_between_24_35 = Beta('BETA_age_between_24_35', 0.0, None, None, 0)
BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)

BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)
BETA_edu_junior = Beta('BETA_edu_junior', 0.0, None, None, 0)
BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)

BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)

BETA_children = Beta('BETA_children', 0.0, None, None, 0)

BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)
BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)
BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)

BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)
BETA_citytime_betwen_3_6 = Beta('BETA_citytime_betwen_3_6', 0.0, None, None, 0)
BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)
BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)

BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)
BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)
BETA_monthincome_between_4000_8000 = Beta('BETA_monthincome_between_4000_8000', 0.0, None, None, 0)
BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)
BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)

BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)
BETA_disposableincome_between_5000_10000 = Beta('BETA_disposableincome_between_5000_10000', 0.0, None, None, 0)
BETA_disposableincome_more_10000 = Beta('BETA_disposableincome_more_10000', 0.0, None, None, 0)
BETA_disposableincome_less_10000 = Beta('BETA_disposableincome_less_10000', 0.0, None, None, 0)

BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)

#* choice model 
# V1 = BETA_intercept + \
#      BETA_deliverytime_brief * deliverytime_brief + \
#      BETA_deliverytime_short * deliverytime_short + \
#      BETA_deliverytime_extended * deliverytime_extended + \
#      BETA_deliverytime_prolonged * deliverytime_prolonged + \
#      BETA_deliverymode_full * deliverymode_full + \
#      BETA_deliveryposition_brief * deliveryposition_brief + \
#      BETA_deliveryposition_short * deliveryposition_short + \
#      BETA_deliveryposition_extended * deliveryposition_extended + \
#      BETA_deliveryposition_prolonged * deliveryposition_prolonged + \
#      BETA_workingdays_brief * workingdays_brief + \
#      BETA_workingdays_short * workingdays_short + \
#      BETA_workingdays_standard * workingdays_standard + \
#      BETA_workingdays_extended * workingdays_extended + \
#      BETA_workingdays_prolonged * workingdays_prolonged + \
#      BETA_workinghours_brief * workinghours_brief + \
#      BETA_workinghours_short * workinghours_short + \
#      BETA_workinghours_standard * workinghours_standard + \
#      BETA_workinghours_extended * workinghours_extended + \
#      BETA_workinghours_prolonged * workinghours_prolonged + \
#      BETA_deliveryorders_brief * deliveryorders_brief + \
#      BETA_deliveryorders_short * deliveryorders_short + \
#      BETA_deliveryorders_standard * deliveryorders_standard + \
#      BETA_deliveryorders_extended * deliveryorders_extended + \
#      BETA_deliveryorders_prolonged * deliveryorders_prolonged + \
#      BETA_deliveryrange_brief * deliveryrange_brief + \
#      BETA_deliveryrange_short * deliveryrange_short + \
#      BETA_deliveryrange_standard * deliveryrange_standard + \
#      BETA_deliveryrange_extended * deliveryrange_extended + \
#      BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
#      BETA_deliveryspeed_brief * deliveryspeed_brief + \
#      BETA_deliveryspeed_short * deliveryspeed_short + \
#      BETA_deliveryspeed_standard * deliveryspeed_standard + \
#      BETA_deliveryspeed_extended * deliveryspeed_extended + \
#      BETA_deliveryspeed_prolonged * deliveryspeed_prolonged + \
#      BETA_gender_female  * gender_female + \
#      BETA_age_less_24 * age_less_24 + \
#      BETA_age_between_24_35 * age_between_24_35 +\
#      BETA_age_more_35 * age_more_35 + \
#      BETA_edu_less_junior * edu_less_junior + \
#      BETA_edu_junior * edu_junior +\
#      BETA_edu_more_uni * edu_more_uni + \
#      BETA_marriage * marriage_not + \
#      BETA_children * children + \
#      BETA_family_small * family_small + \
#      BETA_family_middle * family_middle + \
#      BETA_family_big * family_big + \
#      BETA_citytime_less_3 * citytime_less_3 + \
#      BETA_citytime_betwen_3_6 * citytime_betwen_3_6 + \
#      BETA_citytime_more_6 * citytime_more_6 + \
#      BETA_citytime_local * citytime_local + \
#      BETA_monthincome_less_2000 * monthincome_less_2000 + \
#      BETA_monthincome_less_4000 * monthincome_less_4000 + \
#      BETA_monthincome_between_4000_8000 * monthincome_between_4000_8000 +\
#      BETA_monthincome_more_8000 * monthincome_more_8000 + \
#      BETA_disposableincome_less_5000 * disposableincome_less_5000 + \
#      BETA_disposableincome_between_5000_10000 * disposableincome_between_5000_10000 + \
#      BETA_disposableincome_more_10000 * disposableincome_more_10000
# V2 = 0

# V1 = BETA_intercept + \
#      BETA_deliverytime_brief * deliverytime_brief + \
#      BETA_deliverytime_short * deliverytime_short + \
#      BETA_deliveryposition_prolonged * deliveryposition_prolonged + \
#      BETA_workingdays_standard * workingdays_standard + \
#      BETA_workingdays_prolonged * workingdays_prolonged + \
#      BETA_workinghours_extended * workinghours_extended + \
#      BETA_deliveryorders_brief * deliveryorders_brief + \
#      BETA_deliveryorders_extended * deliveryorders_extended + \
#      BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
#      BETA_deliveryspeed_extended * deliveryspeed_extended + \
#      BETA_deliveryspeed_prolonged * deliveryspeed_prolonged + \
#      BETA_gender_female  * gender_female + \
#      BETA_age_less_24 * age_less_24 + \
#      BETA_edu_less_junior * edu_less_junior + \
#      BETA_edu_more_uni * edu_more_uni + \
#      BETA_marriage * marriage_not + \
#      BETA_children * children + \
#      BETA_family_big * family_big + \
#      BETA_monthincome_less_4000 * monthincome_less_4000 + \
#      BETA_monthincome_more_8000 * monthincome_more_8000 + \
#      BETA_disposableincome_more_10000 * disposableincome_more_10000
# V2 = 0

#* 第一次once model
V1 = BETA_intercept + \
     BETA_age_less_24 * age_less_24 + \
     BETA_children * children + \
     BETA_deliverymode_full * deliverymode_full + \
     BETA_deliveryorders_brief * deliveryorders_brief + \
     BETA_deliveryposition_extended * deliveryposition_extended+ \
     BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
     BETA_deliveryspeed_extended * deliveryspeed_extended + \
     BETA_deliverytime_short * deliverytime_short + \
     BETA_disposableincome_more_10000 * disposableincome_more_10000 + \
     BETA_edu_less_junior * edu_less_junior + \
     BETA_family_big * family_big + \
     BETA_gender_female  * gender_female + \
     BETA_marriage * marriage_not + \
     BETA_monthincome_less_4000 * monthincome_less_4000 + \
     BETA_workingdays_standard * workingdays_standard + \
     BETA_workinghours_brief * workinghours_brief

V2 = 0

# Associate utility functions with the numbering of alternatives
V = {1: V1,
     2: V2,
     }

condprob = models.loglogit(V, None, UnsafeAccident)

# Define level of verbosity
logger = msg.bioMessage()
# logger.setSilent()
# logger.setWarning()
logger.setGeneral()
# logger.setDetailed()

# 获取当前日期和时间
now = datetime.now()
date_time = now.strftime("%Y-%m-%d_%H-%M-%S")

# Create the Biogeme object
the_biogeme  = bio.BIOGEME(database, condprob)
the_biogeme.modelName = 'accident_mnl_' + date_time
# the_biogeme.modelName = 'accident_mnl_once_1' # 根据时间和条件同时删除问卷
# the_biogeme.modelName = 'accident_mnl_once_2' # 不删除问卷
# the_biogeme.modelName = 'accident_mnl_once_3' # 根据条删除问卷
the_biogeme.generatePickle = False
# the_biogeme.generate_html = False

# Estimate the parameters
results = the_biogeme.estimate()

print(results.short_summary())

# print(f'Estimated betas: {len(results.data.betaValues)}')
# print(f'Final log likelihood: {results.data.logLike:.3f}')
# print(f'Output file: {results.data.htmlFileName}')

shutil.move(str(results.data.htmlFileName), str('mnl/'+results.data.htmlFileName))

# print(results.short_summary())

pandas_results = results.getEstimatedParameters()
# print(pandas_results)

# 获取p-value列小于0.1的行
# filtered_rows = pandas_results[pandas_results['p-value'] < 0.1]
# print(filtered_rows)
# # 计算满足条件的行的数量
# count_of_filtered_rows = filtered_rows.shape[0]
# # 打印结果
# print("满足条件的变量的数量为:", count_of_filtered_rows)

beta_rows = pandas_results[pandas_results.index.str.startswith('BETA_')]
print(beta_rows)
# # 在筛选的结果中，进一步筛选出 p-value 列小于 0.1 的行
# filtered_rows = beta_rows[beta_rows['p-value'] < 0.1]
# print(filtered_rows)
# # 计算满足条件的行的数量
# count_of_filtered_rows = filtered_rows.shape[0]

# # 打印结果
# print("满足条件的行的数量为:", count_of_filtered_rows)