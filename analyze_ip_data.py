#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析df_offline中其['序号']存在df['ID']的行，并统计其来自 IP 列包括多少个省份和城市
"""

import pandas as pd
import re

def parse_ip_location(ip_string):
    """
    解析IP地址字符串，提取省份和城市
    格式: *************(广东-广州)
    返回: (省份, 城市)
    """
    if pd.isna(ip_string) or ip_string == '':
        return None, None
    
    # 使用正则表达式匹配括号内的地理位置信息
    pattern = r'\(([^-]+)-([^)]+)\)'
    match = re.search(pattern, str(ip_string))
    
    if match:
        province = match.group(1).strip()
        city = match.group(2).strip()
        return province, city
    else:
        # 如果没有匹配到标准格式，尝试其他可能的格式
        return None, None

def main():
    try:
        # 读取数据
        print("正在读取数据...")
        dforigin = pd.read_excel('new_412.xlsx')
        df = dforigin[dforigin['QeTime'] >= 120]
        df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]
        
        df_offline = pd.read_excel('484.xlsx', sheet_name='Sheet2')
        
        print(f"df中共有{len(df)}行数据，{len(df['ID'].unique())}个唯一ID")
        print(f"df_offline中共有{len(df_offline)}行数据")
        
        # 获取df中的ID列表
        df_ids = set(df['ID'].values)
        print(f"df中共有{len(df_ids)}个ID")
        
        # 筛选出df_offline中序号存在于df['ID']中的行
        matched_rows = df_offline[df_offline['序号'].isin(df_ids)]
        print(f"匹配的行数: {len(matched_rows)}")
        
        if len(matched_rows) == 0:
            print("没有找到匹配的行")
            return
        
        # 查看匹配行的基本信息
        print(f"\n匹配的序号: {sorted(matched_rows['序号'].tolist())}")
        
        # 检查df_offline的列名
        print(f"\ndf_offline的列名: {df_offline.columns.tolist()}")
        
        # 查找可能包含IP信息的列
        ip_columns = []
        for col in df_offline.columns:
            if 'IP' in str(col) or '来自' in str(col) or 'ip' in str(col).lower():
                ip_columns.append(col)
        
        print(f"可能包含IP信息的列: {ip_columns}")
        
        if not ip_columns:
            print("未找到包含IP信息的列，显示所有列的样本数据:")
            for col in df_offline.columns:
                sample_data = df_offline[col].dropna().head(3)
                print(f"\n列 '{col}' 的样本数据:")
                for i, value in enumerate(sample_data):
                    print(f"  {i+1}: {value}")
            return
        
        # 使用第一个找到的IP列
        ip_column = ip_columns[0]
        print(f"\n使用列: {ip_column}")
        
        # 查看该列的样本数据
        print(f"\n{ip_column}列的样本数据:")
        sample_data = df_offline[ip_column].dropna().head(10)
        for i, value in enumerate(sample_data):
            print(f"  {i+1}: {value}")
        
        # 测试解析函数
        test_ip = "*************(广东-广州)"
        province, city = parse_ip_location(test_ip)
        print(f"\n测试解析: {test_ip} -> 省份: {province}, 城市: {city}")
        
        # 获取匹配行的IP信息
        matched_ip_data = matched_rows[ip_column]
        
        # 解析所有IP地址信息
        provinces = []
        cities = []
        failed_parses = []
        
        for idx, ip_info in matched_ip_data.items():
            province, city = parse_ip_location(ip_info)
            if province and city:
                provinces.append(province)
                cities.append(city)
            else:
                failed_parses.append(ip_info)
        
        # 统计唯一的省份和城市数量
        unique_provinces = set(provinces)
        unique_cities = set(cities)
        
        print(f"\n=== 统计结果 ===")
        print(f"匹配的总行数: {len(matched_rows)}")
        print(f"成功解析的IP地址数: {len(provinces)}")
        print(f"解析失败的IP地址数: {len(failed_parses)}")
        print(f"涉及的省份数量: {len(unique_provinces)}")
        print(f"涉及的城市数量: {len(unique_cities)}")
        
        print(f"\n省份列表: {sorted(list(unique_provinces))}")
        print(f"\n城市列表: {sorted(list(unique_cities))}")
        
        # 统计每个省份的城市数量
        province_city_count = {}
        for i, province in enumerate(provinces):
            city = cities[i]
            if province not in province_city_count:
                province_city_count[province] = set()
            province_city_count[province].add(city)
        
        print(f"\n=== 各省份城市分布 ===")
        for province, city_set in sorted(province_city_count.items()):
            print(f"{province}: {len(city_set)}个城市 - {sorted(list(city_set))}")

        # 统计每个省份的问卷数量
        province_questionnaire_count = {}
        for province in provinces:
            if province not in province_questionnaire_count:
                province_questionnaire_count[province] = 0
            province_questionnaire_count[province] += 1

        print(f"\n=== 各省份问卷数量统计 ===")
        total_questionnaires = sum(province_questionnaire_count.values())
        print(f"总问卷数: {total_questionnaires}")
        print(f"省份数量: {len(province_questionnaire_count)}")
        print("\n各省份问卷数量（按数量降序排列）:")

        # 按问卷数量降序排列
        sorted_provinces = sorted(province_questionnaire_count.items(), key=lambda x: x[1], reverse=True)
        for i, (province, count) in enumerate(sorted_provinces, 1):
            percentage = (count / total_questionnaires) * 100
            print(f"  {i:2d}. {province:4s}: {count:2d}份问卷 ({percentage:5.1f}%)")

        # 创建省份问卷数量的DataFrame并保存
        province_df = pd.DataFrame(list(province_questionnaire_count.items()),
                                 columns=['省份', '问卷数量'])
        province_df = province_df.sort_values('问卷数量', ascending=False).reset_index(drop=True)
        province_df['占比(%)'] = (province_df['问卷数量'] / province_df['问卷数量'].sum() * 100).round(1)

        print(f"\n=== 省份问卷统计表 ===")
        print(province_df.to_string(index=False))

        # 保存到Excel文件
        try:
            province_df.to_excel('省份问卷统计.xlsx', index=False)
            print(f"\n统计结果已保存到 '省份问卷统计.xlsx'")
        except Exception as e:
            print(f"\n保存Excel文件时出错: {e}")

        if failed_parses:
            print(f"\n=== 解析失败的IP信息 ===")
            for i, failed_ip in enumerate(failed_parses[:10]):  # 只显示前10个
                print(f"  {i+1}: {failed_ip}")
            if len(failed_parses) > 10:
                print(f"  ... 还有{len(failed_parses) - 10}个解析失败的IP")
        
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
