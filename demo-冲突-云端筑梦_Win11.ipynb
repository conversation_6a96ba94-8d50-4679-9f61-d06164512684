{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集大小： (455, 30)\n", "测试集大小： (114, 30)\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.datasets import load_breast_cancer\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 加载数据集\n", "data = load_breast_cancer()\n", "X, y = data.data, data.target\n", "feature_names = data.feature_names\n", "\n", "# 划分训练集与测试集\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=2023\n", ")\n", "\n", "print(\"训练集大小：\", X_train.shape)\n", "print(\"测试集大小：\", X_test.shape)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CatBoost 测试集准确率： 0.9824561403508771\n"]}], "source": ["from catboost import CatBoostClassifier\n", "\n", "# 定义并训练模型\n", "model = CatBoostClassifier(\n", "    iterations=100,      # 迭代次数\n", "    learning_rate=0.1,   # 学习率\n", "    depth=3,             # 树深度\n", "    verbose=False        # 训练时不输出详细信息\n", ")\n", "\n", "model.fit(X_train, y_train)\n", "\n", "# 预测并计算准确率\n", "y_pred = model.predict(X_test)\n", "accuracy = (y_pred == y_test).mean()\n", "print(\"CatBoost 测试集准确率：\", accuracy)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1,\n", "       1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0,\n", "       1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 1, 1,\n", "       0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 0, 0, 1,\n", "       1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1,\n", "       1, 1, 1, 0])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Applications/anaconda3/envs/bev_analysis/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import shap\n", "\n", "# 创建解释器（TreeExplainer 支持决策树类型的模型，包括CatBoost、XGBoost、LightGBM等）\n", "explainer = shap.<PERSON>Explainer(model)\n", "\n", "# 计算测试集的 SHAP 值\n", "shap_values = explainer.shap_values(X_test)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ID", "rawType": "int64", "type": "integer"}], "ref": "ae8cff3c-0c67-4aa4-ac93-53b29bc7f31f", "rows": [["2", "3"], ["6", "7"], ["9", "10"], ["10", "11"], ["11", "352"], ["12", "13"], ["15", "16"], ["17", "18"], ["18", "19"], ["19", "20"], ["20", "1"], ["21", "22"], ["22", "65"], ["24", "25"], ["25", "26"], ["26", "27"], ["27", "28"], ["28", "29"], ["29", "99"], ["30", "31"], ["31", "175"], ["34", "35"], ["35", "36"], ["38", "39"], ["39", "40"], ["40", "41"], ["45", "46"], ["46", "176"], ["49", "50"], ["51", "180"], ["53", "54"], ["54", "55"], ["55", "56"], ["57", "58"], ["58", "203"], ["59", "219"], ["60", "61"], ["61", "62"], ["63", "64"], ["64", "242"], ["65", "66"], ["66", "67"], ["67", "247"], ["68", "69"], ["69", "70"], ["70", "71"], ["72", "73"], ["73", "74"], ["75", "76"], ["76", "77"]], "shape": {"columns": 1, "rows": 276}}, "text/plain": ["2        3\n", "6        7\n", "9       10\n", "10      11\n", "11     352\n", "      ... \n", "408    409\n", "410    411\n", "411    412\n", "412    413\n", "413    414\n", "Name: ID, Length: 276, dtype: int64"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "dforigin = pd.read_excel('new_412.xlsx')\n", "df = dforigin[dforigin['QeTime'] >= 120]\n", "df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]\n", "df['ID']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "序号", "rawType": "int64", "type": "integer"}], "ref": "4c0bd662-2b5d-42eb-97d8-b64d13a1d8fe", "rows": [["0", "31"], ["1", "40"], ["2", "43"], ["3", "48"], ["4", "49"], ["5", "50"], ["6", "51"], ["7", "52"], ["8", "53"], ["9", "55"], ["10", "56"], ["11", "58"], ["12", "60"], ["13", "61"], ["14", "62"], ["15", "63"], ["16", "65"], ["17", "67"], ["18", "68"], ["19", "69"], ["20", "70"], ["21", "72"], ["22", "73"], ["23", "87"], ["24", "91"], ["25", "93"], ["26", "95"], ["27", "96"], ["28", "98"], ["29", "99"], ["30", "104"], ["31", "117"], ["32", "134"], ["33", "138"], ["34", "140"], ["35", "152"], ["36", "164"], ["37", "167"], ["38", "169"], ["39", "174"], ["40", "176"], ["41", "177"], ["42", "178"], ["43", "179"], ["44", "180"], ["45", "181"], ["46", "183"], ["47", "185"], ["48", "186"], ["49", "187"]], "shape": {"columns": 1, "rows": 141}}, "text/plain": ["0       31\n", "1       40\n", "2       43\n", "3       48\n", "4       49\n", "      ... \n", "136    570\n", "137    578\n", "138    580\n", "139    591\n", "140    625\n", "Name: 序号, Length: 141, dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_offline = pd.read_excel('484.xlsx', sheet_name= 'Sheet2')\n", "df_offline['序号']"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["141"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df_offline)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df中共有276个ID\n", "匹配的行数: 73\n", "\n", "匹配的序号:\n", "[31, 40, 50, 55, 56, 58, 60, 61, 62, 65, 67, 68, 69, 70, 73, 87, 91, 93, 95, 96, 98, 99, 104, 117, 134, 152, 164, 167, 174, 176, 177, 178, 179, 180, 181, 183, 188, 189, 190, 194, 196, 200, 213, 214, 215, 221, 225, 235, 237, 240, 242, 247, 250, 251, 254, 258, 261, 264, 312, 330, 331, 332, 334, 336, 338, 355, 357, 359, 374, 388, 392, 393, 404]\n"]}], "source": ["# 找出 df_offline 中其['序号']存在df['ID']的行，并统计其来自 IP 列包括多少个省份和城市\n", "import re\n", "\n", "# 获取df中的ID列表\n", "df_ids = set(df['ID'].values)\n", "print(f\"df中共有{len(df_ids)}个ID\")\n", "\n", "# 筛选出df_offline中序号存在于df['ID']中的行\n", "matched_rows = df_offline[df_offline['序号'].isin(df_ids)]\n", "print(f\"匹配的行数: {len(matched_rows)}\")\n", "\n", "# 查看匹配行的基本信息\n", "print(\"\\n匹配的序号:\")\n", "print(matched_rows['序号'].tolist())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df_offline的列名:\n", "['序号', '提交答卷时间', '所用时间', '来源', '来源详情', '来自IP', '1、调查者编号', '2、您的性别', '3、您的年龄', '4、您的受教育程度', '5、您的婚姻状况', '6、目前您的家庭中共有几人？（包含您）', '7、您在现居住城市的生活时间', '8、您做外卖骑手多久了', '9、您属于哪种配送模式', '10、您的骑手段位是（括号内容仅供参考）？', '11、您每周工作的天数', '12、您工作日平均每天在岗时间', '13、您每月的收入情况（单位：人民币）', '14、您家庭每月可支配收入（单位：人民币）参考：可支配收入=家庭总收入-交纳的所得税-个人交纳的社会保障支出-记账补贴', '15、您平均每天的配送单量', '16、您平均每天骑行的公里数', '17、您的平均骑行速度为', '18、请根据您对以下有关健康状况的问题进行评价—我目前非常健康', '18、我经常感到身体不舒服', '18、这份工作带给我很多病痛困扰（如腰痛、颈椎问题、胃病、眼科疾病等）', '19、请您对配送过程的安全程度进行评价', '20、请您根据以下有关配送环境的问题进行评价—配送过程中天气状况良好', '20、车流量的变化会影响我配送', '20、配送时会经过非常多的交叉路口', '20、路边停放的车辆影响我配送', '20、非机动车道的宽度影响我配送', '21、请您根据以下有关配送压力的问题进行评价—背单量过多时我会感到焦虑', '21、每天达不到一定的配送单量我会感到焦虑', '21、我认为我每一单的配送时长都很长', '21、我认为目前平台要求的送达时间很紧张', '22、请您根据以下有关生活习惯的问题进行评价—我经常抽烟', '22、我经常饮酒', '22、我习惯熬夜', '22、我经常一边骑行一边接打电话', '22、我经常骑行的时候不带安全帽', '23、请您对以下有关工作环境的问题进行评价—我认为公司给我的归属感很强', '23、我认为每天的工作具有很高的灵活性', '23、我认为所处的工作环境是较为舒适的', '23、我与同事、上级相处的很愉快', '23、工作期间有足够时间进行休息（例如每一单之间和骑手个人用餐时间）', '24、请您对以下有关工作待遇的问题进行评价—当前收入', '24、工作中的奖励政策', '24、工作中的处罚政策', '24、工作中提供的社会保障', '25、请根据您对以下有关社会地位的问题进行评价—我感觉顾客对我的态度往往很差', '25、我感觉餐厅工作人员对我的态度往往很差', '25、当我穿着外卖工作服时，我感觉会被歧视', '25、我认为以后这份工作会得到越来越多人的认可', '26、请根据您对以下有关职业前景的问题进行评价—我认为这份工作以后的薪资提高水平不大', '26、我认为这份工作未来的竞争会比较激烈', '26、我认为随着年龄的增长我将会被淘汰', '26、我认为新兴技术（例如无人车、无人机等）的加入会使我工作机会减少', '26、我认为未来升职空间较大', '27、请您对这份工作的满意程度进行评价', '28、请您对以下有关骑行态度的问题进行打分—我认为不遵守交通规则可以更快地完成配送任务', '28、有时为了获得更高的报酬，可以有一些不安全的骑行行为', '28、在我能确保安全的情况下，可以违反交通规则', '28、我认为按时完成订单比遵守交通规则更重要', '29、请您对以下有关主观观念的问题进行打分—家人和朋友经常强调安全骑行时，会影响我的骑行行为', '29、交通管理部门对违反交通规则的惩罚力度，会影响我的骑行行为', '29、配送平台对违反交通规则的态度，会影响我的骑行行为', '29、社会舆论对不安全骑行的态度，会影响我的骑行行为', '30、请您对以下有关行为控制的问题进行打分—对我来说，当意外发生时我可以快速反应', '30、对我来说，我有足够的经验来处理骑行时的各种突发情况', '30、对我来说，当违反交通规则时，仍能保持自身安全', '31、请您对以下有关行为意向的问题进行打分—未来在配送时，我会为了保证订单及时送达而违反交通规则', '31、未来在配送时，我会为了完成更多的订单而违反交通规则', '31、未来在配送时，不论什么情况下我都会选择安全骑行', '32、您最近一个月内是否出现了不安全骑行行为（如闯红灯、逆行、抢占机动车道、乱停乱放和无牌无证等）？', '33、当时的背单量为', '34、该行为发生的时间为', '35、当时的天气状况为', '36、当时所处的道路位置为', '37、当时正在配送的订单预计到达时间还剩多久', '38、该不安全骑行行为是否导致事故发生', '39、事故严重程度为', '40、请问您对换电柜的了解程度是怎样的', '41、您每天使用多少次换电柜', '42、请问一天中您在哪个时段使用换电柜的次数最多?', '43、在剩余多少电量时您会选择去换电？ ', '44、您使用换电柜的原因有（可多选）(方便快捷，有利于延长配送时长)', '44、(APP操作方便，容易学会)', '44、(价格合适，能够接受)', '44、(电池相对安全)', '44、(其他)', '45、您一般在选择换电柜的时候都会考虑什么？ （可多选）(选择靠近取餐地点的换电柜)', '45、(选择靠近送餐地点的换电柜)', '45、(选择距离当前地点最近的换电柜)', '45、(电量非常低时选择最近的换电柜)', '45、(位于车流量小地区的换电柜)', '45、(具有非机动车道道路的换电柜)', '45、(位于配送站处的换电柜)', '45、(习惯性地使用1-3个换电柜，基本只使用这几个换电柜，不考虑其他的换电柜)', '45、(其他)', '46、请问您在给电瓶车换电时还有几个未配送订单？', '47、请问您是否遇到“没有可用的电池”这一状况？如果有，请写出该状况一周发生的平均次数。', '48、请问调查员编号为？', '总分']\n", "\n", "df_offline前5行数据:\n", "   序号              提交答卷时间  所用时间  来源      来源详情                    来自IP 1、调查者编号  \\\n", "0  31  2023/12/3 15:50:02  153秒  微信       NaN   **************(上海-上海)       2   \n", "1  40  2023/12/7 22:09:47  141秒  微信       哇咔咔     ************(河南-郑州)     (空)   \n", "2  43  2023/12/7 22:14:40  173秒  微信  别叫我，起不来床   **************(北京-北京)     (空)   \n", "3  48  2023/12/7 22:30:15  183秒  微信        小新   **************(江西-九江)     (空)   \n", "4  49  2023/12/7 22:30:24  126秒  微信         刘  ***************(山东-济南)     (空)   \n", "\n", "   2、您的性别  3、您的年龄  4、您的受教育程度  ...  45、(电量非常低时选择最近的换电柜)  45、(位于车流量小地区的换电柜)  \\\n", "0       1       5          2  ...                    1                  0   \n", "1       1       2          3  ...                   -3                 -3   \n", "2       1       3          4  ...                    0                  0   \n", "3       1       2          4  ...                   -3                 -3   \n", "4       1       3          4  ...                   -3                 -3   \n", "\n", "   45、(具有非机动车道道路的换电柜)  45、(位于配送站处的换电柜)  \\\n", "0                   1                0   \n", "1                  -3               -3   \n", "2                   0                0   \n", "3                  -3               -3   \n", "4                  -3               -3   \n", "\n", "   45、(习惯性地使用1-3个换电柜，基本只使用这几个换电柜，不考虑其他的换电柜)  45、(其他)  \\\n", "0                                         0        0   \n", "1                                        -3       -3   \n", "2                                         0        0   \n", "3                                        -3       -3   \n", "4                                        -3       -3   \n", "\n", "   46、请问您在给电瓶车换电时还有几个未配送订单？  47、请问您是否遇到“没有可用的电池”这一状况？如果有，请写出该状况一周发生的平均次数。  \\\n", "0                         5                                            没有   \n", "1                        -3                                            -3   \n", "2                         1                                            wu   \n", "3                        -3                                            -3   \n", "4                        -3                                            -3   \n", "\n", "   48、请问调查员编号为？   总分  \n", "0             2  158  \n", "1             4  160  \n", "2            wu  192  \n", "3             4  142  \n", "4             4  166  \n", "\n", "[5 rows x 104 columns]\n"]}], "source": ["# 检查df_offline是否有'来自 IP'列\n", "print(\"df_offline的列名:\")\n", "print(df_offline.columns.tolist())\n", "\n", "# 显示前几行数据以了解结构\n", "print(\"\\ndf_offline前5行数据:\")\n", "print(df_offline.head())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["匹配行的数据:\n", "      序号              提交答卷时间  所用时间  来源     来源详情                    来自IP  \\\n", "0     31  2023/12/3 15:50:02  153秒  微信      NaN   **************(上海-上海)   \n", "1     40  2023/12/7 22:09:47  141秒  微信      哇咔咔     ************(河南-郑州)   \n", "5     50  2023/12/7 22:31:20  131秒  微信      利奥张  ***************(江西-九江)   \n", "9     55  2023/12/7 22:40:18  135秒  微信    不是梵高.  ***************(江西-赣州)   \n", "10    56  2023/12/7 22:40:46  103秒  微信      李小红  ***************(山东-济南)   \n", "..   ...                 ...   ...  ..      ...                     ...   \n", "103  374  2023/12/9 19:46:25   54秒  微信  A.6-7.9     ************(河南-新乡)   \n", "105  388  2023/12/9 20:26:51  171秒  微信     日月同明   **************(安徽-合肥)   \n", "107  392  2023/12/9 20:40:16  138秒  微信       M.   **************(湖南-长沙)   \n", "108  393  2023/12/9 20:46:53  147秒  微信       嗯哼     ************(河南-郑州)   \n", "110  404  2023/12/9 21:43:33   81秒  微信       TT    49.93.186.194(江苏-未知)   \n", "\n", "    1、调查者编号  2、您的性别  3、您的年龄  4、您的受教育程度  ...  45、(电量非常低时选择最近的换电柜)  \\\n", "0         2       1       5          2  ...                    1   \n", "1       (空)       1       2          3  ...                   -3   \n", "5       (空)       2       2          4  ...                   -3   \n", "9       (空)       3       2          4  ...                   -3   \n", "10      (空)       1       3          3  ...                   -3   \n", "..      ...     ...     ...        ...  ...                  ...   \n", "103     (空)       1       2          4  ...                   -3   \n", "105     (空)       1       4          5  ...                    1   \n", "107     (空)       2       3          3  ...                   -3   \n", "108     (空)       1       2          4  ...                    0   \n", "110     (空)       1       3          4  ...                   -3   \n", "\n", "     45、(位于车流量小地区的换电柜)  45、(具有非机动车道道路的换电柜)  45、(位于配送站处的换电柜)  \\\n", "0                    0                   1                0   \n", "1                   -3                  -3               -3   \n", "5                   -3                  -3               -3   \n", "9                   -3                  -3               -3   \n", "10                  -3                  -3               -3   \n", "..                 ...                 ...              ...   \n", "103                 -3                  -3               -3   \n", "105                  1                   1                1   \n", "107                 -3                  -3               -3   \n", "108                  0                   0                0   \n", "110                 -3                  -3               -3   \n", "\n", "     45、(习惯性地使用1-3个换电柜，基本只使用这几个换电柜，不考虑其他的换电柜)  45、(其他)  \\\n", "0                                           0        0   \n", "1                                          -3       -3   \n", "5                                          -3       -3   \n", "9                                          -3       -3   \n", "10                                         -3       -3   \n", "..                                        ...      ...   \n", "103                                        -3       -3   \n", "105                                         0        0   \n", "107                                        -3       -3   \n", "108                                         0        0   \n", "110                                        -3       -3   \n", "\n", "     46、请问您在给电瓶车换电时还有几个未配送订单？  47、请问您是否遇到“没有可用的电池”这一状况？如果有，请写出该状况一周发生的平均次数。  \\\n", "0                           5                                            没有   \n", "1                          -3                                            -3   \n", "5                          -3                                            -3   \n", "9                          -3                                            -3   \n", "10                         -3                                            -3   \n", "..                        ...                                           ...   \n", "103                        -3                                            -3   \n", "105                         3                                            没有   \n", "107                        -3                                            -3   \n", "108                         3                                             4   \n", "110                        -3                                            -3   \n", "\n", "     48、请问调查员编号为？   总分  \n", "0               2  158  \n", "1               4  160  \n", "5               4  154  \n", "9               4  147  \n", "10              4  147  \n", "..            ...  ...  \n", "103             4  147  \n", "105             4  231  \n", "107             4  147  \n", "108           007  180  \n", "110             4  245  \n", "\n", "[73 rows x 104 columns]\n"]}], "source": ["# 假设IP列的名称可能不同，让我们查找包含IP信息的列\n", "# 先查看匹配行的所有数据\n", "if len(matched_rows) > 0:\n", "    print(\"匹配行的数据:\")\n", "    print(matched_rows)\n", "else:\n", "    print(\"没有找到匹配的行\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["可能包含IP信息的列: ['来自IP']\n", "\n", "使用列: 来自IP\n", "\n", "来自IP列的样本数据:\n", "1: **************(上海-上海)\n", "2: ************(河南-郑州)\n", "3: **************(北京-北京)\n", "4: **************(江西-九江)\n", "5: ***************(山东-济南)\n", "6: ***************(江西-九江)\n", "7: *************(北京-北京)\n", "8: ***************(山东-济南)\n", "9: ***************(山东-济南)\n", "10: ***************(江西-赣州)\n"]}], "source": ["# 查找可能包含IP信息的列\n", "ip_columns = []\n", "for col in df_offline.columns:\n", "    if 'IP' in str(col) or '来自' in str(col) or 'ip' in str(col).lower():\n", "        ip_columns.append(col)\n", "        \n", "print(f\"可能包含IP信息的列: {ip_columns}\")\n", "\n", "# 如果找到了IP列，继续处理\n", "if ip_columns:\n", "    ip_column = ip_columns[0]  # 使用第一个找到的IP列\n", "    print(f\"\\n使用列: {ip_column}\")\n", "    \n", "    # 查看该列的样本数据\n", "    print(f\"\\n{ip_column}列的样本数据:\")\n", "    sample_data = df_offline[ip_column].dropna().head(10)\n", "    for i, value in enumerate(sample_data):\n", "        print(f\"{i+1}: {value}\")\n", "else:\n", "    print(\"未找到包含IP信息的列，请检查列名\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试解析: *************(广东-广州) -> 省份: 广东, 城市: 广州\n"]}], "source": ["# 定义函数来解析IP地址信息\n", "def parse_ip_location(ip_string):\n", "    \"\"\"\n", "    解析IP地址字符串，提取省份和城市\n", "    格式: *************(广东-广州)\n", "    返回: (省份, 城市)\n", "    \"\"\"\n", "    if pd.isna(ip_string) or ip_string == '':\n", "        return None, None\n", "    \n", "    # 使用正则表达式匹配括号内的地理位置信息\n", "    pattern = r'\\(([^-]+)-([^)]+)\\)'\n", "    match = re.search(pattern, str(ip_string))\n", "    \n", "    if match:\n", "        province = match.group(1).strip()\n", "        city = match.group(2).strip()\n", "        return province, city\n", "    else:\n", "        # 如果没有匹配到标准格式，尝试其他可能的格式\n", "        # 比如只有省份，或者用其他分隔符\n", "        return None, None\n", "\n", "# 测试解析函数\n", "test_ip = \"*************(广东-广州)\"\n", "province, city = parse_ip_location(test_ip)\n", "print(f\"测试解析: {test_ip} -> 省份: {province}, 城市: {city}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 统计结果 ===\n", "匹配的总行数: 73\n", "成功解析的IP地址数: 73\n", "涉及的省份数量: 16\n", "涉及的城市数量: 33\n", "\n", "省份列表: ['上海', '北京', '安徽', '山东', '山西', '广东', '广西', '江苏', '江西', '河南', '浙江', '海南', '湖南', '贵州', '重庆', '陕西']\n", "\n", "城市列表: ['上海', '上饶', '东莞', '九江', '北京', '南宁', '南昌', '合肥', '吕梁', '嘉兴', '威海', '宁波', '安康', '广州', '开封', '徐州', '新乡', '无锡', '未知', '汕头', '济南', '海口', '深圳', '温州', '茂名', '西安', '贵阳', '赣州', '郑州', '重庆', '长沙', '青岛', '驻马店']\n", "\n", "=== 各省份城市分布 ===\n", "上海: 1个城市 - ['上海']\n", "北京: 1个城市 - ['北京']\n", "安徽: 1个城市 - ['合肥']\n", "山东: 3个城市 - ['威海', '济南', '青岛']\n", "山西: 1个城市 - ['吕梁']\n", "广东: 5个城市 - ['东莞', '广州', '汕头', '深圳', '茂名']\n", "广西: 1个城市 - ['南宁']\n", "江苏: 3个城市 - ['徐州', '无锡', '未知']\n", "江西: 4个城市 - ['上饶', '九江', '南昌', '赣州']\n", "河南: 4个城市 - ['开封', '新乡', '郑州', '驻马店']\n", "浙江: 3个城市 - ['嘉兴', '宁波', '温州']\n", "海南: 2个城市 - ['未知', '海口']\n", "湖南: 1个城市 - ['长沙']\n", "贵州: 1个城市 - ['贵阳']\n", "重庆: 1个城市 - ['重庆']\n", "陕西: 2个城市 - ['安康', '西安']\n"]}], "source": ["# 如果找到了IP列，进行省份和城市统计\n", "if ip_columns:\n", "    ip_column = ip_columns[0]\n", "    \n", "    # 获取匹配行的IP信息\n", "    matched_ip_data = matched_rows[ip_column]\n", "    \n", "    # 解析所有IP地址信息\n", "    provinces = []\n", "    cities = []\n", "    \n", "    for ip_info in matched_ip_data:\n", "        province, city = parse_ip_location(ip_info)\n", "        if province and city:\n", "            provinces.append(province)\n", "            cities.append(city)\n", "    \n", "    # 统计唯一的省份和城市数量\n", "    unique_provinces = set(provinces)\n", "    unique_cities = set(cities)\n", "    \n", "    print(f\"\\n=== 统计结果 ===\")\n", "    print(f\"匹配的总行数: {len(matched_rows)}\")\n", "    print(f\"成功解析的IP地址数: {len(provinces)}\")\n", "    print(f\"涉及的省份数量: {len(unique_provinces)}\")\n", "    print(f\"涉及的城市数量: {len(unique_cities)}\")\n", "    \n", "    print(f\"\\n省份列表: {sorted(list(unique_provinces))}\")\n", "    print(f\"\\n城市列表: {sorted(list(unique_cities))}\")\n", "    \n", "    # 统计每个省份的城市数量\n", "    province_city_count = {}\n", "    for i, province in enumerate(provinces):\n", "        city = cities[i]\n", "        if province not in province_city_count:\n", "            province_city_count[province] = set()\n", "        province_city_count[province].add(city)\n", "    \n", "    print(f\"\\n=== 各省份城市分布 ===\")\n", "    for province, city_set in sorted(province_city_count.items()):\n", "        print(f\"{province}: {len(city_set)}个城市 - {sorted(list(city_set))}\")\n", "        \n", "else:\n", "    print(\"无法找到IP列，请检查数据\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\bev_analysis\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Explaining instance:\n", "     MedInc  HouseAge  AveRooms  AveBedrms  Population  AveOccup  Latitude  Longitude\n", "565  3.0718      52.0  5.068966   1.113027      1128.0   2.16092     37.76    -122.24\n", "\n", "Starting bootstrap with 100 iterations...\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import shap\n", "import xgboost\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "\n", "# --- 1. <PERSON><PERSON> Data and Train a Model ---\n", "# Using the classic California Housing dataset from sklearn\n", "from sklearn.datasets import fetch_california_housing\n", "housing = fetch_california_housing()\n", "X = pd.DataFrame(housing.data, columns=housing.feature_names)\n", "y = housing.target\n", "\n", "# For demonstration, let's use a subset of the data\n", "X_sample = X.sample(n=1000, random_state=1)\n", "y_sample = y[X_sample.index]\n", "\n", "# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(X_sample, y_sample, test_size=0.2, random_state=1)\n", "\n", "# Train an XGBoost model\n", "model = xgboost.XGBRegressor(objective='reg:squarederror', eval_metric='rmse')\n", "model.fit(X_train, y_train)\n", "\n", "# --- 2. Setup for SHAP Value Bootstrapping ---\n", "# Let's explain the first instance in the test set\n", "instance_to_explain = X_test.iloc[[0]]\n", "\n", "# The background dataset for calculating expectations.\n", "# A common choice is a representative sample of the training data.\n", "# Let's use 100 random samples from the training set as our background data.\n", "background_data = shap.sample(X_train, 100)\n", "\n", "# Number of bootstrap iterations\n", "n_bootstrap_iterations = 100\n", "shap_values_bootstrap = []\n", "\n", "print(f\"Explaining instance:\\n{instance_to_explain.to_string()}\")\n", "print(f\"\\nStarting bootstrap with {n_bootstrap_iterations} iterations...\")\n", "\n", "# --- 3. Run the Bootstrap Loop ---\n", "for i in range(n_bootstrap_iterations):\n", "    # Resample the background data with replacement\n", "    bootstrap_background = background_data.sample(n=len(background_data), replace=True)\n", "\n", "    # Create a new explainer with the bootstrapped background data\n", "    explainer = shap.TreeExplainer(model, bootstrap_background)\n", "\n", "    # Calculate SHAP values for the instance\n", "    shap_values = explainer.shap_values(instance_to_explain)\n", "\n", "    # Store the results\n", "    shap_values_bootstrap.append(shap_values[0]) # We have one instance, so get the first row\n", "\n", "    if (i + 1) % 10 == 0:\n", "        print(f\"Completed iteration {i+1}/{n_bootstrap_iterations}\")\n", "\n", "# Convert the list of arrays into a 2D numpy array\n", "shap_values_bootstrap = np.array(shap_values_bootstrap)\n", "# Shape will be (n_bootstrap_iterations, n_features)\n", "\n", "# --- 4. Calculate Confidence Intervals ---\n", "# Calculate the mean SHAP value for each feature\n", "mean_shap_values = shap_values_bootstrap.mean(axis=0)\n", "\n", "# Calculate the 95% confidence interval for each feature's SHAP value\n", "# We use the 2.5th and 97.5th percentiles\n", "lower_bounds = np.percentile(shap_values_bootstrap, 2.5, axis=0)\n", "upper_bounds = np.percentile(shap_values_bootstrap, 97.5, axis=0)\n", "\n", "# The size of the confidence interval (for the error bars)\n", "ci_size = upper_bounds - lower_bounds\n", "\n", "# --- 5. Visualize the Results ---\n", "feature_names = X.columns\n", "# Create a dataframe for easier plotting\n", "results_df = pd.DataFrame({\n", "    'feature': feature_names,\n", "    'mean_shap': mean_shap_values,\n", "    'ci_lower': lower_bounds,\n", "    'ci_upper': upper_bounds\n", "})\n", "results_df = results_df.sort_values('mean_shap', ascending=False)\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 6))\n", "# Error bars will be the distance from the mean to the CI bounds\n", "y_err = np.array([results_df['mean_shap'] - results_df['ci_lower'],\n", "                  results_df['ci_upper'] - results_df['mean_shap']])\n", "\n", "plt.bar(results_df['feature'], results_df['mean_shap'], yerr=y_err, capsize=4)\n", "plt.title('SHAP Values with 95% Confidence Intervals for a Single Prediction')\n", "plt.ylabel('SHAP Value')\n", "plt.xlabel('Feature')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\nMean SHAP values and their 95% Confidence Intervals:\")\n", "print(results_df.to_string())"]}], "metadata": {"kernelspec": {"display_name": "bev_analysis", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}