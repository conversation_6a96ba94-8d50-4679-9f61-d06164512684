{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集大小： (455, 30)\n", "测试集大小： (114, 30)\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.datasets import load_breast_cancer\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 加载数据集\n", "data = load_breast_cancer()\n", "X, y = data.data, data.target\n", "feature_names = data.feature_names\n", "\n", "# 划分训练集与测试集\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=2023\n", ")\n", "\n", "print(\"训练集大小：\", X_train.shape)\n", "print(\"测试集大小：\", X_test.shape)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CatBoost 测试集准确率： 0.9824561403508771\n"]}], "source": ["from catboost import CatBoostClassifier\n", "\n", "# 定义并训练模型\n", "model = CatBoostClassifier(\n", "    iterations=100,      # 迭代次数\n", "    learning_rate=0.1,   # 学习率\n", "    depth=3,             # 树深度\n", "    verbose=False        # 训练时不输出详细信息\n", ")\n", "\n", "model.fit(X_train, y_train)\n", "\n", "# 预测并计算准确率\n", "y_pred = model.predict(X_test)\n", "accuracy = (y_pred == y_test).mean()\n", "print(\"CatBoost 测试集准确率：\", accuracy)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1,\n", "       1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0,\n", "       1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 1, 1,\n", "       0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 0, 0, 1,\n", "       1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1,\n", "       1, 1, 1, 0])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Applications/anaconda3/envs/bev_analysis/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import shap\n", "\n", "# 创建解释器（TreeExplainer 支持决策树类型的模型，包括CatBoost、XGBoost、LightGBM等）\n", "explainer = shap.<PERSON>Explainer(model)\n", "\n", "# 计算测试集的 SHAP 值\n", "shap_values = explainer.shap_values(X_test)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ID", "rawType": "int64", "type": "integer"}], "ref": "ae8cff3c-0c67-4aa4-ac93-53b29bc7f31f", "rows": [["2", "3"], ["6", "7"], ["9", "10"], ["10", "11"], ["11", "352"], ["12", "13"], ["15", "16"], ["17", "18"], ["18", "19"], ["19", "20"], ["20", "1"], ["21", "22"], ["22", "65"], ["24", "25"], ["25", "26"], ["26", "27"], ["27", "28"], ["28", "29"], ["29", "99"], ["30", "31"], ["31", "175"], ["34", "35"], ["35", "36"], ["38", "39"], ["39", "40"], ["40", "41"], ["45", "46"], ["46", "176"], ["49", "50"], ["51", "180"], ["53", "54"], ["54", "55"], ["55", "56"], ["57", "58"], ["58", "203"], ["59", "219"], ["60", "61"], ["61", "62"], ["63", "64"], ["64", "242"], ["65", "66"], ["66", "67"], ["67", "247"], ["68", "69"], ["69", "70"], ["70", "71"], ["72", "73"], ["73", "74"], ["75", "76"], ["76", "77"]], "shape": {"columns": 1, "rows": 276}}, "text/plain": ["2        3\n", "6        7\n", "9       10\n", "10      11\n", "11     352\n", "      ... \n", "408    409\n", "410    411\n", "411    412\n", "412    413\n", "413    414\n", "Name: ID, Length: 276, dtype: int64"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "dforigin = pd.read_excel('new_412.xlsx')\n", "df = dforigin[dforigin['QeTime'] >= 120]\n", "df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]\n", "df['ID']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "序号", "rawType": "int64", "type": "integer"}], "ref": "4c0bd662-2b5d-42eb-97d8-b64d13a1d8fe", "rows": [["0", "31"], ["1", "40"], ["2", "43"], ["3", "48"], ["4", "49"], ["5", "50"], ["6", "51"], ["7", "52"], ["8", "53"], ["9", "55"], ["10", "56"], ["11", "58"], ["12", "60"], ["13", "61"], ["14", "62"], ["15", "63"], ["16", "65"], ["17", "67"], ["18", "68"], ["19", "69"], ["20", "70"], ["21", "72"], ["22", "73"], ["23", "87"], ["24", "91"], ["25", "93"], ["26", "95"], ["27", "96"], ["28", "98"], ["29", "99"], ["30", "104"], ["31", "117"], ["32", "134"], ["33", "138"], ["34", "140"], ["35", "152"], ["36", "164"], ["37", "167"], ["38", "169"], ["39", "174"], ["40", "176"], ["41", "177"], ["42", "178"], ["43", "179"], ["44", "180"], ["45", "181"], ["46", "183"], ["47", "185"], ["48", "186"], ["49", "187"]], "shape": {"columns": 1, "rows": 141}}, "text/plain": ["0       31\n", "1       40\n", "2       43\n", "3       48\n", "4       49\n", "      ... \n", "136    570\n", "137    578\n", "138    580\n", "139    591\n", "140    625\n", "Name: 序号, Length: 141, dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_offline = pd.read_excel('484.xlsx', sheet_name= 'Sheet2')\n", "df_offline['序号']"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["141"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df_offline)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df中共有276个ID\n", "匹配的行数: 73\n", "\n", "匹配的序号:\n", "[31, 40, 50, 55, 56, 58, 60, 61, 62, 65, 67, 68, 69, 70, 73, 87, 91, 93, 95, 96, 98, 99, 104, 117, 134, 152, 164, 167, 174, 176, 177, 178, 179, 180, 181, 183, 188, 189, 190, 194, 196, 200, 213, 214, 215, 221, 225, 235, 237, 240, 242, 247, 250, 251, 254, 258, 261, 264, 312, 330, 331, 332, 334, 336, 338, 355, 357, 359, 374, 388, 392, 393, 404]\n"]}], "source": ["# 找出 df_offline 中其['序号']存在df['ID']的行，并统计其来自 IP 列包括多少个省份和城市\n", "import re\n", "\n", "# 获取df中的ID列表\n", "df_ids = set(df['ID'].values)\n", "print(f\"df中共有{len(df_ids)}个ID\")\n", "\n", "# 筛选出df_offline中序号存在于df['ID']中的行\n", "matched_rows = df_offline[df_offline['序号'].isin(df_ids)]\n", "print(f\"匹配的行数: {len(matched_rows)}\")\n", "\n", "# 查看匹配行的基本信息\n", "print(\"\\n匹配的序号:\")\n", "print(matched_rows['序号'].tolist())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df_offline的列名:\n", "['序号', '提交答卷时间', '所用时间', '来源', '来源详情', '来自IP', '1、调查者编号', '2、您的性别', '3、您的年龄', '4、您的受教育程度', '5、您的婚姻状况', '6、目前您的家庭中共有几人？（包含您）', '7、您在现居住城市的生活时间', '8、您做外卖骑手多久了', '9、您属于哪种配送模式', '10、您的骑手段位是（括号内容仅供参考）？', '11、您每周工作的天数', '12、您工作日平均每天在岗时间', '13、您每月的收入情况（单位：人民币）', '14、您家庭每月可支配收入（单位：人民币）参考：可支配收入=家庭总收入-交纳的所得税-个人交纳的社会保障支出-记账补贴', '15、您平均每天的配送单量', '16、您平均每天骑行的公里数', '17、您的平均骑行速度为', '18、请根据您对以下有关健康状况的问题进行评价—我目前非常健康', '18、我经常感到身体不舒服', '18、这份工作带给我很多病痛困扰（如腰痛、颈椎问题、胃病、眼科疾病等）', '19、请您对配送过程的安全程度进行评价', '20、请您根据以下有关配送环境的问题进行评价—配送过程中天气状况良好', '20、车流量的变化会影响我配送', '20、配送时会经过非常多的交叉路口', '20、路边停放的车辆影响我配送', '20、非机动车道的宽度影响我配送', '21、请您根据以下有关配送压力的问题进行评价—背单量过多时我会感到焦虑', '21、每天达不到一定的配送单量我会感到焦虑', '21、我认为我每一单的配送时长都很长', '21、我认为目前平台要求的送达时间很紧张', '22、请您根据以下有关生活习惯的问题进行评价—我经常抽烟', '22、我经常饮酒', '22、我习惯熬夜', '22、我经常一边骑行一边接打电话', '22、我经常骑行的时候不带安全帽', '23、请您对以下有关工作环境的问题进行评价—我认为公司给我的归属感很强', '23、我认为每天的工作具有很高的灵活性', '23、我认为所处的工作环境是较为舒适的', '23、我与同事、上级相处的很愉快', '23、工作期间有足够时间进行休息（例如每一单之间和骑手个人用餐时间）', '24、请您对以下有关工作待遇的问题进行评价—当前收入', '24、工作中的奖励政策', '24、工作中的处罚政策', '24、工作中提供的社会保障', '25、请根据您对以下有关社会地位的问题进行评价—我感觉顾客对我的态度往往很差', '25、我感觉餐厅工作人员对我的态度往往很差', '25、当我穿着外卖工作服时，我感觉会被歧视', '25、我认为以后这份工作会得到越来越多人的认可', '26、请根据您对以下有关职业前景的问题进行评价—我认为这份工作以后的薪资提高水平不大', '26、我认为这份工作未来的竞争会比较激烈', '26、我认为随着年龄的增长我将会被淘汰', '26、我认为新兴技术（例如无人车、无人机等）的加入会使我工作机会减少', '26、我认为未来升职空间较大', '27、请您对这份工作的满意程度进行评价', '28、请您对以下有关骑行态度的问题进行打分—我认为不遵守交通规则可以更快地完成配送任务', '28、有时为了获得更高的报酬，可以有一些不安全的骑行行为', '28、在我能确保安全的情况下，可以违反交通规则', '28、我认为按时完成订单比遵守交通规则更重要', '29、请您对以下有关主观观念的问题进行打分—家人和朋友经常强调安全骑行时，会影响我的骑行行为', '29、交通管理部门对违反交通规则的惩罚力度，会影响我的骑行行为', '29、配送平台对违反交通规则的态度，会影响我的骑行行为', '29、社会舆论对不安全骑行的态度，会影响我的骑行行为', '30、请您对以下有关行为控制的问题进行打分—对我来说，当意外发生时我可以快速反应', '30、对我来说，我有足够的经验来处理骑行时的各种突发情况', '30、对我来说，当违反交通规则时，仍能保持自身安全', '31、请您对以下有关行为意向的问题进行打分—未来在配送时，我会为了保证订单及时送达而违反交通规则', '31、未来在配送时，我会为了完成更多的订单而违反交通规则', '31、未来在配送时，不论什么情况下我都会选择安全骑行', '32、您最近一个月内是否出现了不安全骑行行为（如闯红灯、逆行、抢占机动车道、乱停乱放和无牌无证等）？', '33、当时的背单量为', '34、该行为发生的时间为', '35、当时的天气状况为', '36、当时所处的道路位置为', '37、当时正在配送的订单预计到达时间还剩多久', '38、该不安全骑行行为是否导致事故发生', '39、事故严重程度为', '40、请问您对换电柜的了解程度是怎样的', '41、您每天使用多少次换电柜', '42、请问一天中您在哪个时段使用换电柜的次数最多?', '43、在剩余多少电量时您会选择去换电？ ', '44、您使用换电柜的原因有（可多选）(方便快捷，有利于延长配送时长)', '44、(APP操作方便，容易学会)', '44、(价格合适，能够接受)', '44、(电池相对安全)', '44、(其他)', '45、您一般在选择换电柜的时候都会考虑什么？ （可多选）(选择靠近取餐地点的换电柜)', '45、(选择靠近送餐地点的换电柜)', '45、(选择距离当前地点最近的换电柜)', '45、(电量非常低时选择最近的换电柜)', '45、(位于车流量小地区的换电柜)', '45、(具有非机动车道道路的换电柜)', '45、(位于配送站处的换电柜)', '45、(习惯性地使用1-3个换电柜，基本只使用这几个换电柜，不考虑其他的换电柜)', '45、(其他)', '46、请问您在给电瓶车换电时还有几个未配送订单？', '47、请问您是否遇到“没有可用的电池”这一状况？如果有，请写出该状况一周发生的平均次数。', '48、请问调查员编号为？', '总分']\n", "\n", "df_offline前5行数据:\n", "   序号              提交答卷时间  所用时间  来源      来源详情                    来自IP 1、调查者编号  \\\n", "0  31  2023/12/3 15:50:02  153秒  微信       NaN   **************(上海-上海)       2   \n", "1  40  2023/12/7 22:09:47  141秒  微信       哇咔咔     ************(河南-郑州)     (空)   \n", "2  43  2023/12/7 22:14:40  173秒  微信  别叫我，起不来床   **************(北京-北京)     (空)   \n", "3  48  2023/12/7 22:30:15  183秒  微信        小新   **************(江西-九江)     (空)   \n", "4  49  2023/12/7 22:30:24  126秒  微信         刘  ***************(山东-济南)     (空)   \n", "\n", "   2、您的性别  3、您的年龄  4、您的受教育程度  ...  45、(电量非常低时选择最近的换电柜)  45、(位于车流量小地区的换电柜)  \\\n", "0       1       5          2  ...                    1                  0   \n", "1       1       2          3  ...                   -3                 -3   \n", "2       1       3          4  ...                    0                  0   \n", "3       1       2          4  ...                   -3                 -3   \n", "4       1       3          4  ...                   -3                 -3   \n", "\n", "   45、(具有非机动车道道路的换电柜)  45、(位于配送站处的换电柜)  \\\n", "0                   1                0   \n", "1                  -3               -3   \n", "2                   0                0   \n", "3                  -3               -3   \n", "4                  -3               -3   \n", "\n", "   45、(习惯性地使用1-3个换电柜，基本只使用这几个换电柜，不考虑其他的换电柜)  45、(其他)  \\\n", "0                                         0        0   \n", "1                                        -3       -3   \n", "2                                         0        0   \n", "3                                        -3       -3   \n", "4                                        -3       -3   \n", "\n", "   46、请问您在给电瓶车换电时还有几个未配送订单？  47、请问您是否遇到“没有可用的电池”这一状况？如果有，请写出该状况一周发生的平均次数。  \\\n", "0                         5                                            没有   \n", "1                        -3                                            -3   \n", "2                         1                                            wu   \n", "3                        -3                                            -3   \n", "4                        -3                                            -3   \n", "\n", "   48、请问调查员编号为？   总分  \n", "0             2  158  \n", "1             4  160  \n", "2            wu  192  \n", "3             4  142  \n", "4             4  166  \n", "\n", "[5 rows x 104 columns]\n"]}], "source": ["# 检查df_offline是否有'来自 IP'列\n", "print(\"df_offline的列名:\")\n", "print(df_offline.columns.tolist())\n", "\n", "# 显示前几行数据以了解结构\n", "print(\"\\ndf_offline前5行数据:\")\n", "print(df_offline.head())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["匹配行的数据:\n", "      序号              提交答卷时间  所用时间  来源     来源详情                    来自IP  \\\n", "0     31  2023/12/3 15:50:02  153秒  微信      NaN   **************(上海-上海)   \n", "1     40  2023/12/7 22:09:47  141秒  微信      哇咔咔     ************(河南-郑州)   \n", "5     50  2023/12/7 22:31:20  131秒  微信      利奥张  ***************(江西-九江)   \n", "9     55  2023/12/7 22:40:18  135秒  微信    不是梵高.  ***************(江西-赣州)   \n", "10    56  2023/12/7 22:40:46  103秒  微信      李小红  ***************(山东-济南)   \n", "..   ...                 ...   ...  ..      ...                     ...   \n", "103  374  2023/12/9 19:46:25   54秒  微信  A.6-7.9     ************(河南-新乡)   \n", "105  388  2023/12/9 20:26:51  171秒  微信     日月同明   **************(安徽-合肥)   \n", "107  392  2023/12/9 20:40:16  138秒  微信       M.   **************(湖南-长沙)   \n", "108  393  2023/12/9 20:46:53  147秒  微信       嗯哼     ************(河南-郑州)   \n", "110  404  2023/12/9 21:43:33   81秒  微信       TT    49.93.186.194(江苏-未知)   \n", "\n", "    1、调查者编号  2、您的性别  3、您的年龄  4、您的受教育程度  ...  45、(电量非常低时选择最近的换电柜)  \\\n", "0         2       1       5          2  ...                    1   \n", "1       (空)       1       2          3  ...                   -3   \n", "5       (空)       2       2          4  ...                   -3   \n", "9       (空)       3       2          4  ...                   -3   \n", "10      (空)       1       3          3  ...                   -3   \n", "..      ...     ...     ...        ...  ...                  ...   \n", "103     (空)       1       2          4  ...                   -3   \n", "105     (空)       1       4          5  ...                    1   \n", "107     (空)       2       3          3  ...                   -3   \n", "108     (空)       1       2          4  ...                    0   \n", "110     (空)       1       3          4  ...                   -3   \n", "\n", "     45、(位于车流量小地区的换电柜)  45、(具有非机动车道道路的换电柜)  45、(位于配送站处的换电柜)  \\\n", "0                    0                   1                0   \n", "1                   -3                  -3               -3   \n", "5                   -3                  -3               -3   \n", "9                   -3                  -3               -3   \n", "10                  -3                  -3               -3   \n", "..                 ...                 ...              ...   \n", "103                 -3                  -3               -3   \n", "105                  1                   1                1   \n", "107                 -3                  -3               -3   \n", "108                  0                   0                0   \n", "110                 -3                  -3               -3   \n", "\n", "     45、(习惯性地使用1-3个换电柜，基本只使用这几个换电柜，不考虑其他的换电柜)  45、(其他)  \\\n", "0                                           0        0   \n", "1                                          -3       -3   \n", "5                                          -3       -3   \n", "9                                          -3       -3   \n", "10                                         -3       -3   \n", "..                                        ...      ...   \n", "103                                        -3       -3   \n", "105                                         0        0   \n", "107                                        -3       -3   \n", "108                                         0        0   \n", "110                                        -3       -3   \n", "\n", "     46、请问您在给电瓶车换电时还有几个未配送订单？  47、请问您是否遇到“没有可用的电池”这一状况？如果有，请写出该状况一周发生的平均次数。  \\\n", "0                           5                                            没有   \n", "1                          -3                                            -3   \n", "5                          -3                                            -3   \n", "9                          -3                                            -3   \n", "10                         -3                                            -3   \n", "..                        ...                                           ...   \n", "103                        -3                                            -3   \n", "105                         3                                            没有   \n", "107                        -3                                            -3   \n", "108                         3                                             4   \n", "110                        -3                                            -3   \n", "\n", "     48、请问调查员编号为？   总分  \n", "0               2  158  \n", "1               4  160  \n", "5               4  154  \n", "9               4  147  \n", "10              4  147  \n", "..            ...  ...  \n", "103             4  147  \n", "105             4  231  \n", "107             4  147  \n", "108           007  180  \n", "110             4  245  \n", "\n", "[73 rows x 104 columns]\n"]}], "source": ["# 假设IP列的名称可能不同，让我们查找包含IP信息的列\n", "# 先查看匹配行的所有数据\n", "if len(matched_rows) > 0:\n", "    print(\"匹配行的数据:\")\n", "    print(matched_rows)\n", "else:\n", "    print(\"没有找到匹配的行\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["可能包含IP信息的列: ['来自IP']\n", "\n", "使用列: 来自IP\n", "\n", "来自IP列的样本数据:\n", "1: **************(上海-上海)\n", "2: ************(河南-郑州)\n", "3: **************(北京-北京)\n", "4: **************(江西-九江)\n", "5: ***************(山东-济南)\n", "6: ***************(江西-九江)\n", "7: *************(北京-北京)\n", "8: ***************(山东-济南)\n", "9: ***************(山东-济南)\n", "10: ***************(江西-赣州)\n"]}], "source": ["# 查找可能包含IP信息的列\n", "ip_columns = []\n", "for col in df_offline.columns:\n", "    if 'IP' in str(col) or '来自' in str(col) or 'ip' in str(col).lower():\n", "        ip_columns.append(col)\n", "        \n", "print(f\"可能包含IP信息的列: {ip_columns}\")\n", "\n", "# 如果找到了IP列，继续处理\n", "if ip_columns:\n", "    ip_column = ip_columns[0]  # 使用第一个找到的IP列\n", "    print(f\"\\n使用列: {ip_column}\")\n", "    \n", "    # 查看该列的样本数据\n", "    print(f\"\\n{ip_column}列的样本数据:\")\n", "    sample_data = df_offline[ip_column].dropna().head(10)\n", "    for i, value in enumerate(sample_data):\n", "        print(f\"{i+1}: {value}\")\n", "else:\n", "    print(\"未找到包含IP信息的列，请检查列名\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试解析: *************(广东-广州) -> 省份: 广东, 城市: 广州\n"]}], "source": ["# 定义函数来解析IP地址信息\n", "def parse_ip_location(ip_string):\n", "    \"\"\"\n", "    解析IP地址字符串，提取省份和城市\n", "    格式: *************(广东-广州)\n", "    返回: (省份, 城市)\n", "    \"\"\"\n", "    if pd.isna(ip_string) or ip_string == '':\n", "        return None, None\n", "    \n", "    # 使用正则表达式匹配括号内的地理位置信息\n", "    pattern = r'\\(([^-]+)-([^)]+)\\)'\n", "    match = re.search(pattern, str(ip_string))\n", "    \n", "    if match:\n", "        province = match.group(1).strip()\n", "        city = match.group(2).strip()\n", "        return province, city\n", "    else:\n", "        # 如果没有匹配到标准格式，尝试其他可能的格式\n", "        # 比如只有省份，或者用其他分隔符\n", "        return None, None\n", "\n", "# 测试解析函数\n", "test_ip = \"*************(广东-广州)\"\n", "province, city = parse_ip_location(test_ip)\n", "print(f\"测试解析: {test_ip} -> 省份: {province}, 城市: {city}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 统计结果 ===\n", "匹配的总行数: 73\n", "成功解析的IP地址数: 73\n", "涉及的省份数量: 16\n", "涉及的城市数量: 33\n", "\n", "省份列表: ['上海', '北京', '安徽', '山东', '山西', '广东', '广西', '江苏', '江西', '河南', '浙江', '海南', '湖南', '贵州', '重庆', '陕西']\n", "\n", "城市列表: ['上海', '上饶', '东莞', '九江', '北京', '南宁', '南昌', '合肥', '吕梁', '嘉兴', '威海', '宁波', '安康', '广州', '开封', '徐州', '新乡', '无锡', '未知', '汕头', '济南', '海口', '深圳', '温州', '茂名', '西安', '贵阳', '赣州', '郑州', '重庆', '长沙', '青岛', '驻马店']\n", "\n", "=== 各省份城市分布 ===\n", "上海: 1个城市 - ['上海']\n", "北京: 1个城市 - ['北京']\n", "安徽: 1个城市 - ['合肥']\n", "山东: 3个城市 - ['威海', '济南', '青岛']\n", "山西: 1个城市 - ['吕梁']\n", "广东: 5个城市 - ['东莞', '广州', '汕头', '深圳', '茂名']\n", "广西: 1个城市 - ['南宁']\n", "江苏: 3个城市 - ['徐州', '无锡', '未知']\n", "江西: 4个城市 - ['上饶', '九江', '南昌', '赣州']\n", "河南: 4个城市 - ['开封', '新乡', '郑州', '驻马店']\n", "浙江: 3个城市 - ['嘉兴', '宁波', '温州']\n", "海南: 2个城市 - ['未知', '海口']\n", "湖南: 1个城市 - ['长沙']\n", "贵州: 1个城市 - ['贵阳']\n", "重庆: 1个城市 - ['重庆']\n", "陕西: 2个城市 - ['安康', '西安']\n"]}], "source": ["# 如果找到了IP列，进行省份和城市统计\n", "if ip_columns:\n", "    ip_column = ip_columns[0]\n", "    \n", "    # 获取匹配行的IP信息\n", "    matched_ip_data = matched_rows[ip_column]\n", "    \n", "    # 解析所有IP地址信息\n", "    provinces = []\n", "    cities = []\n", "    \n", "    for ip_info in matched_ip_data:\n", "        province, city = parse_ip_location(ip_info)\n", "        if province and city:\n", "            provinces.append(province)\n", "            cities.append(city)\n", "    \n", "    # 统计唯一的省份和城市数量\n", "    unique_provinces = set(provinces)\n", "    unique_cities = set(cities)\n", "    \n", "    print(f\"\\n=== 统计结果 ===\")\n", "    print(f\"匹配的总行数: {len(matched_rows)}\")\n", "    print(f\"成功解析的IP地址数: {len(provinces)}\")\n", "    print(f\"涉及的省份数量: {len(unique_provinces)}\")\n", "    print(f\"涉及的城市数量: {len(unique_cities)}\")\n", "    \n", "    print(f\"\\n省份列表: {sorted(list(unique_provinces))}\")\n", "    print(f\"\\n城市列表: {sorted(list(unique_cities))}\")\n", "    \n", "    # 统计每个省份的城市数量\n", "    province_city_count = {}\n", "    for i, province in enumerate(provinces):\n", "        city = cities[i]\n", "        if province not in province_city_count:\n", "            province_city_count[province] = set()\n", "        province_city_count[province].add(city)\n", "    \n", "    print(f\"\\n=== 各省份城市分布 ===\")\n", "    for province, city_set in sorted(province_city_count.items()):\n", "        print(f\"{province}: {len(city_set)}个城市 - {sorted(list(city_set))}\")\n", "        \n", "else:\n", "    print(\"无法找到IP列，请检查数据\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\bev_analysis\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Explaining instance:\n", "     MedInc  HouseAge  AveRooms  AveBedrms  Population  AveOccup  Latitude  Longitude\n", "565  3.0718      52.0  5.068966   1.113027      1128.0   2.16092     37.76    -122.24\n", "\n", "Starting bootstrap with 100 iterations...\n", "Completed iteration 10/100\n", "Completed iteration 20/100\n", "Completed iteration 30/100\n", "Completed iteration 40/100\n", "Completed iteration 50/100\n", "Completed iteration 60/100\n", "Completed iteration 70/100\n", "Completed iteration 80/100\n", "Completed iteration 90/100\n", "Completed iteration 100/100\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Mean SHAP values and their 95% Confidence Intervals:\n", "      feature  mean_shap  ci_lower  ci_upper\n", "1    HouseAge   0.379630  0.335600  0.412713\n", "7   Longitude   0.366028  0.284104  0.437313\n", "5    AveOccup   0.295463  0.247402  0.333888\n", "4  Population   0.033516  0.004276  0.066812\n", "3   AveBedrms  -0.016795 -0.035366  0.004314\n", "2    AveRooms  -0.052089 -0.080076 -0.028172\n", "6    Latitude  -0.069901 -0.135329 -0.001657\n", "0      MedInc  -0.126073 -0.262334  0.004034\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import shap\n", "import xgboost\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "\n", "# --- 1. <PERSON><PERSON> Data and Train a Model ---\n", "# Using the classic California Housing dataset from sklearn\n", "from sklearn.datasets import fetch_california_housing\n", "housing = fetch_california_housing()\n", "X = pd.DataFrame(housing.data, columns=housing.feature_names)\n", "y = housing.target\n", "\n", "# For demonstration, let's use a subset of the data\n", "X_sample = X.sample(n=1000, random_state=1)\n", "y_sample = y[X_sample.index]\n", "\n", "# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(X_sample, y_sample, test_size=0.2, random_state=1)\n", "\n", "# Train an XGBoost model\n", "model = xgboost.XGBRegressor(objective='reg:squarederror', eval_metric='rmse')\n", "model.fit(X_train, y_train)\n", "\n", "# --- 2. Setup for SHAP Value Bootstrapping ---\n", "# Let's explain the first instance in the test set\n", "instance_to_explain = X_test.iloc[[0]]\n", "\n", "# The background dataset for calculating expectations.\n", "# A common choice is a representative sample of the training data.\n", "# Let's use 100 random samples from the training set as our background data.\n", "background_data = shap.sample(X_train, 100)\n", "\n", "# Number of bootstrap iterations\n", "n_bootstrap_iterations = 100\n", "shap_values_bootstrap = []\n", "\n", "print(f\"Explaining instance:\\n{instance_to_explain.to_string()}\")\n", "print(f\"\\nStarting bootstrap with {n_bootstrap_iterations} iterations...\")\n", "\n", "# --- 3. Run the Bootstrap Loop ---\n", "for i in range(n_bootstrap_iterations):\n", "    # Resample the background data with replacement\n", "    bootstrap_background = background_data.sample(n=len(background_data), replace=True)\n", "\n", "    # Create a new explainer with the bootstrapped background data\n", "    explainer = shap.TreeExplainer(model, bootstrap_background)\n", "\n", "    # Calculate SHAP values for the instance\n", "    shap_values = explainer.shap_values(instance_to_explain)\n", "\n", "    # Store the results\n", "    shap_values_bootstrap.append(shap_values[0]) # We have one instance, so get the first row\n", "\n", "    if (i + 1) % 10 == 0:\n", "        print(f\"Completed iteration {i+1}/{n_bootstrap_iterations}\")\n", "\n", "# Convert the list of arrays into a 2D numpy array\n", "shap_values_bootstrap = np.array(shap_values_bootstrap)\n", "# Shape will be (n_bootstrap_iterations, n_features)\n", "\n", "# --- 4. Calculate Confidence Intervals ---\n", "# Calculate the mean SHAP value for each feature\n", "mean_shap_values = shap_values_bootstrap.mean(axis=0)\n", "\n", "# Calculate the 95% confidence interval for each feature's SHAP value\n", "# We use the 2.5th and 97.5th percentiles\n", "lower_bounds = np.percentile(shap_values_bootstrap, 2.5, axis=0)\n", "upper_bounds = np.percentile(shap_values_bootstrap, 97.5, axis=0)\n", "\n", "# The size of the confidence interval (for the error bars)\n", "ci_size = upper_bounds - lower_bounds\n", "\n", "# --- 5. Visualize the Results ---\n", "feature_names = X.columns\n", "# Create a dataframe for easier plotting\n", "results_df = pd.DataFrame({\n", "    'feature': feature_names,\n", "    'mean_shap': mean_shap_values,\n", "    'ci_lower': lower_bounds,\n", "    'ci_upper': upper_bounds\n", "})\n", "results_df = results_df.sort_values('mean_shap', ascending=False)\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 6))\n", "# Error bars will be the distance from the mean to the CI bounds\n", "y_err = np.array([results_df['mean_shap'] - results_df['ci_lower'],\n", "                  results_df['ci_upper'] - results_df['mean_shap']])\n", "\n", "plt.bar(results_df['feature'], results_df['mean_shap'], yerr=y_err, capsize=4)\n", "plt.title('SHAP Values with 95% Confidence Intervals for a Single Prediction')\n", "plt.ylabel('SHAP Value')\n", "plt.xlabel('Feature')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\nMean SHAP values and their 95% Confidence Intervals:\")\n", "print(results_df.to_string())"]}], "metadata": {"kernelspec": {"display_name": "bev_analysis", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}