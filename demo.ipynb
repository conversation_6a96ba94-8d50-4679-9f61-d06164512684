import numpy as np
import pandas as pd
from sklearn.datasets import load_breast_cancer
from sklearn.model_selection import train_test_split

# 加载数据集
data = load_breast_cancer()
X, y = data.data, data.target
feature_names = data.feature_names

# 划分训练集与测试集
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=2023
)

print("训练集大小：", X_train.shape)
print("测试集大小：", X_test.shape)


from catboost import CatBoostClassifier

# 定义并训练模型
model = CatBoostClassifier(
    iterations=100,      # 迭代次数
    learning_rate=0.1,   # 学习率
    depth=3,             # 树深度
    verbose=False        # 训练时不输出详细信息
)

model.fit(X_train, y_train)

# 预测并计算准确率
y_pred = model.predict(X_test)
accuracy = (y_pred == y_test).mean()
print("CatBoost 测试集准确率：", accuracy)


y_pred

import shap

# 创建解释器（TreeExplainer 支持决策树类型的模型，包括CatBoost、XGBoost、LightGBM等）
explainer = shap.TreeExplainer(model)

# 计算测试集的 SHAP 值
shap_values = explainer.shap_values(X_test)


import pandas as pd
dforigin = pd.read_excel('new_412.xlsx')
df = dforigin[dforigin['QeTime'] >= 120]
df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]
df['ID']

df_offline = pd.read_excel('484.xlsx', sheet_name= 'Sheet2')
df_offline['序号']

len(df_offline)

# 找出 df_offline 中其['序号']存在df['ID']的行，并统计其来自 IP 列包括多少个省份和城市
import re

# 获取df中的ID列表
df_ids = set(df['ID'].values)
print(f"df中共有{len(df_ids)}个ID")

# 筛选出df_offline中序号存在于df['ID']中的行
matched_rows = df_offline[df_offline['序号'].isin(df_ids)]
print(f"匹配的行数: {len(matched_rows)}")

# 查看匹配行的基本信息
print("\n匹配的序号:")
print(matched_rows['序号'].tolist())

# 检查df_offline是否有'来自 IP'列
print("df_offline的列名:")
print(df_offline.columns.tolist())

# 显示前几行数据以了解结构
print("\ndf_offline前5行数据:")
print(df_offline.head())

# 假设IP列的名称可能不同，让我们查找包含IP信息的列
# 先查看匹配行的所有数据
if len(matched_rows) > 0:
    print("匹配行的数据:")
    print(matched_rows)
else:
    print("没有找到匹配的行")

# 查找可能包含IP信息的列
ip_columns = []
for col in df_offline.columns:
    if 'IP' in str(col) or '来自' in str(col) or 'ip' in str(col).lower():
        ip_columns.append(col)
        
print(f"可能包含IP信息的列: {ip_columns}")

# 如果找到了IP列，继续处理
if ip_columns:
    ip_column = ip_columns[0]  # 使用第一个找到的IP列
    print(f"\n使用列: {ip_column}")
    
    # 查看该列的样本数据
    print(f"\n{ip_column}列的样本数据:")
    sample_data = df_offline[ip_column].dropna().head(10)
    for i, value in enumerate(sample_data):
        print(f"{i+1}: {value}")
else:
    print("未找到包含IP信息的列，请检查列名")

# 定义函数来解析IP地址信息
def parse_ip_location(ip_string):
    """
    解析IP地址字符串，提取省份和城市
    格式: *************(广东-广州)
    返回: (省份, 城市)
    """
    if pd.isna(ip_string) or ip_string == '':
        return None, None
    
    # 使用正则表达式匹配括号内的地理位置信息
    pattern = r'\(([^-]+)-([^)]+)\)'
    match = re.search(pattern, str(ip_string))
    
    if match:
        province = match.group(1).strip()
        city = match.group(2).strip()
        return province, city
    else:
        # 如果没有匹配到标准格式，尝试其他可能的格式
        # 比如只有省份，或者用其他分隔符
        return None, None

# 测试解析函数
test_ip = "*************(广东-广州)"
province, city = parse_ip_location(test_ip)
print(f"测试解析: {test_ip} -> 省份: {province}, 城市: {city}")

# 如果找到了IP列，进行省份和城市统计
if ip_columns:
    ip_column = ip_columns[0]
    
    # 获取匹配行的IP信息
    matched_ip_data = matched_rows[ip_column]
    
    # 解析所有IP地址信息
    provinces = []
    cities = []
    
    for ip_info in matched_ip_data:
        province, city = parse_ip_location(ip_info)
        if province and city:
            provinces.append(province)
            cities.append(city)
    
    # 统计唯一的省份和城市数量
    unique_provinces = set(provinces)
    unique_cities = set(cities)
    
    print(f"\n=== 统计结果 ===")
    print(f"匹配的总行数: {len(matched_rows)}")
    print(f"成功解析的IP地址数: {len(provinces)}")
    print(f"涉及的省份数量: {len(unique_provinces)}")
    print(f"涉及的城市数量: {len(unique_cities)}")
    
    print(f"\n省份列表: {sorted(list(unique_provinces))}")
    print(f"\n城市列表: {sorted(list(unique_cities))}")
    
    # 统计每个省份的城市数量
    province_city_count = {}
    for i, province in enumerate(provinces):
        city = cities[i]
        if province not in province_city_count:
            province_city_count[province] = set()
        province_city_count[province].add(city)
    
    print(f"\n=== 各省份城市分布 ===")
    for province, city_set in sorted(province_city_count.items()):
        print(f"{province}: {len(city_set)}个城市 - {sorted(list(city_set))}")
        
else:
    print("无法找到IP列，请检查数据")