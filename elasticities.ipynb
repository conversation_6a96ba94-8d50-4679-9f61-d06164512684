{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import biogeme.biogeme as bio\n", "from biogeme import models\n", "import biogeme.results as res\n", "import biogeme.exceptions as excep\n", "from biogeme.expressions import Derive\n", "\n", "import sys\n", "from biogeme import models\n", "import biogeme.biogeme as bio\n", "import biogeme.exceptions as excep\n", "import biogeme.results as res\n", "from datetime import datetime\n", "import biogeme.messaging as msg\n", "import pandas as pd\n", "import biogeme.database as db\n", "import biogeme.biogeme_logging as blog\n", "from biogeme.models import piecewiseFormula\n", "from biogeme.results import bioResults\n", "from biogeme import models\n", "import biogeme.biogeme as bio\n", "from biogeme.expressions import Beta, log, Elem, bioNormalCdf, Variable, bioDraws, MonteCarlo\n", "import shutil\n", "import matplotlib.pyplot as plt\n", "import biogeme.exceptions as excep\n", "import biogeme.results as res"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# elasticities"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def point_elasticities(df):\n", "    database = db.Database('data', df)\n", "    globals().update(database.variables)\n", "\n", "    # 场景属性自变量\n", "    deliVolume = Variable('Volume')\n", "    deliTime = Variable('Time')\n", "    deliWeather = Variable('Weather')\n", "    deliLocation = Variable('Location')\n", "    RemainTime = Variable('RemainTime')\n", "    CauseUnsafe = Variable('CauseUnsafe')\n", "\n", "    # 因变量\n", "    UnsafeAccident = Variable('UnsafeAccident')\n", "\n", "    # 社会经济属性自变量\n", "    gender = Variable('gender')\n", "    age = Variable('age')\n", "    education = Variable('education')\n", "    marriage = Variable('marriage')\n", "    family = Variable('family')\n", "    citytime = Variable('citytime')\n", "    monthincome = Variable('monthincome')\n", "    disposableincome = Variable('disposableincome')\n", "\n", "    # 配送属性自变量\n", "    deliverytime = Variable('deliverytime')\n", "    deliverymode = Variable('deliverymode')\n", "    deliveryposition = Variable('deliveryposition')\n", "    workingdays = Variable('workingdays')\n", "    workinghours = Variable('workinghours')\n", "    deliveryorders = Variable('deliveryorders')\n", "    deliveryrange = Variable('deliveryrange')\n", "    deliveryspeed = Variable('deliveryspeed')\n", "\n", "    # 社会经济属性二分类变量\n", "    gender_female = database.DefineVariable('gender_female', gender == 2) \n", "    age_less_24 = database.DefineVariable('age_less_24', age <= 2) \n", "    edu_less_junior = database.DefineVariable('edu_less_junior', education <= 2)\n", "    marriage_not = database.DefineVariable('marriage_not', marriage <= 1) \n", "    children = database.DefineVariable('children', ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0)) \n", "    family_big = database.DefineVariable('family_big', (family >= 5) &  (family <= 7)) \n", "    monthincome_less_4000 = database.DefineVariable('monthincome_less_4000', monthincome <= 2) \n", "    disposableincome_more_10000 = database.DefineVariable('disposableincome_more_10000', disposableincome >= 3) \n", "\n", "    # 配送属性二分类变量\n", "    deliverytime_short = database.DefineVariable('deliverytime_short', deliverytime <= 2) \n", "    deliverymode_full = database.DefineVariable('deliverymode_full', deliverymode >= 2) \n", "    deliveryposition_extended = database.DefineVariable('deliveryposition_extended', deliveryposition >= 3) \n", "    workinghours_brief = database.DefineVariable('workinghours_brief', workinghours <= 1) \n", "    workingdays_standard = database.DefineVariable('workingdays_standard', workingdays <= 3) \n", "    deliveryorders_brief = database.DefineVariable('deliveryorders_brief', deliveryorders <= 1)\n", "    deliveryrange_prolonged = database.DefineVariable('deliveryrange_prolonged', deliveryrange >= 5)\n", "    deliveryspeed_extended = database.DefineVariable('deliveryspeed_extended', deliveryspeed == 4) \n", "\n", "    #* structual coefficient\n", "\n", "    # Attitude\n", "    coef_intercept_Att = Beta('coef_intercept_Att', 0.0, None, None, 0)\n", "    coef_gender_Att = Beta('coef_gender_Att', 0.0, None, None, 0)\n", "    coef_age_Att = Beta('coef_age_Att', 0.0, None, None, 0)\n", "    coef_edu_Att = Beta('coef_edu_Att', 0.0, None, None, 0)\n", "    coef_marriage_Att = Beta('coef_marriage_Att', 0.0, None, None, 0)\n", "    coef_children_Att = Beta('coef_children_Att', 0.0, None, None, 0)\n", "    coef_family_Att = Beta('coef_family_Att', 0.0, None, None, 0)\n", "    coef_citytime_Att = Beta('coef_citytime_Att', 0.0, None, None, 0)\n", "    coef_monthincome_Att = Beta('coef_monthincome_Att', 0.0, None, None, 0)\n", "    coef_disposableincome_Att = Beta('coef_disposableincome_Att', 0.0, None, None, 0)\n", "\n", "    # Habit\n", "    coef_intercept_Habit = Beta('coef_intercept_Habit', 0.0, None, None, 0)\n", "    coef_gender_Habit = Beta('coef_gender_Habit', 0.0, None, None, 0)\n", "    coef_age_Habit = Beta('coef_age_Habit', 0.0, None, None, 0)\n", "    coef_edu_Habit = Beta('coef_edu_Habit', 0.0, None, None, 0)\n", "    coef_marriage_Habit = Beta('coef_marriage_Habit', 0.0, None, None, 0)\n", "    coef_children_Habit = Beta('coef_children_Habit', 0.0, None, None, 0)\n", "    coef_family_Habit = Beta('coef_family_Habit', 0.0, None, None, 0)\n", "    coef_citytime_Habit = Beta('coef_citytime_Habit', 0.0, None, None, 0)\n", "    coef_monthincome_Habit = Beta('coef_monthincome_Habit', 0.0, None, None, 0)\n", "    coef_disposableincome_Habit = Beta('coef_disposableincome_Habit', 0.0, None, None, 0)\n", "\n", "    #* measurement coefficient\n", "    INTER_Att2 = Beta('INTER_Att2', 0, None, None, 1)\n", "    INTER_Att3 = Beta('INTER_Att3', 0, None, None, 0)\n", "    INTER_Att4 = Beta('INTER_Att4', 0, None, None, 0)\n", "    INTER_Att5 = Beta('INTER_Att5', 0, None, None, 0)\n", "    INTER_Att6 = Beta('INTER_Att6', 0, None, None, 0)\n", "\n", "    B_Att2 = Beta('B_Att2', 1, None, None, 1)\n", "    B_Att3 = Beta('B_Att3', 1, None, None, 0)\n", "    B_Att4 = Beta('B_Att4', 1, None, None, 0)\n", "    B_Att5 = Beta('B_Att5', 1, None, None, 0)\n", "    B_Att6 = Beta('B_Att6', 1, None, None, 0)\n", "\n", "    SIGMA_Att2 = Beta('SIGMA_Att2', 1, 1.0e-5, None, 1)\n", "    SIGMA_Att3 = Beta('SIGMA_Att3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att4 = Beta('SIGMA_Att4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att5 = Beta('SIGMA_Att5', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att6 = Beta('SIGMA_Att6', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Habit_1 = Beta('INTER_Habit_1', 0, None, None, 1)\n", "    INTER_Habit_2 = Beta('INTER_Habit_2', 0, None, None, 0)\n", "    INTER_Habit_3 = Beta('INTER_Habit_3', 0, None, None, 0)\n", "    INTER_Habit_4 = Beta('INTER_Habit_4', 0, None, None, 0)\n", "    INTER_Habit_5 = Beta('INTER_Habit_5', 0, None, None, 0)\n", "\n", "    B_Habit1 = Beta('B_Habit1', 1, None, None, 1)\n", "    B_Habit2 = Beta('B_Habit2', 1, None, None, 0)\n", "    B_Habit3 = Beta('B_Habit3', 1, None, None, 0)\n", "    B_Habit4 = Beta('B_Habit4', 1, None, None, 0)\n", "    B_Habit5 = Beta('B_Habit5', 1, None, None, 0)\n", "\n", "    SIGMA_Habit1 = Beta('SIGMA_Habit1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Habit2 = Beta('SIGMA_Habit2', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit3 = Beta('SIGMA_Habit3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit4 = Beta('SIGMA_Habit4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit5 = Beta('SIGMA_Habit5', 1, 1.0e-5, None, 0)\n", "\n", "    #* latent variables\n", "    BETA_Habit = Beta('BETA_Habit', 0, None, None, 0)\n", "    BETA_Att = Beta('BETA_Att', 0, None, None, 0)\n", "\n", "    #* choice model coefficient\n", "    BETA_Volume = Beta('BETA_Volume', 0, None, None, 0)\n", "    BETA_Time= Beta('BETA_Time', 0, None, None, 0)\n", "    BETA_Weather= Beta('BETA_Weather', 0, None, None, 0)\n", "    BETA_Location = Beta('BETA_Location', 0, None, None, 0)\n", "    BETA_RemainTime = Beta('BETA_RemainTime', 0, None, None, 0)\n", "    BETA_CauseUnsafe = Beta('BETA_CauseUnsafe', 0, None, None, 0)\n", "\n", "    BETA_deliVolume_low = Beta('BETA_deliVolume_low', 0, None, None, 0)\n", "    BETA_deliVolume_high = Beta('BETA_deliVolume_high', 0, None, None, 0)\n", "\n", "    BETA_deliTime_morning = Beta('BETA_deliTime_morning', 0, None, None, 0)\n", "    BETA_deliTime_noon = Beta('BETA_deliTime_noon', 0, None, None, 0)\n", "    BETA_deliTime_evening = Beta('BETA_deliTime_evening', 0, None, None, 0)\n", "    BETA_deliTime_afternoon = Beta('BETA_deliTime_afternoon', 0, None, None, 0)\n", "    BETA_deliTime_night = Beta('BETA_deliTime_night', 0, None, None, 0)\n", "\n", "    BETA_deliWeather_heavy = Beta('BETA_deliWeather_heavy', 0, None, None, 0)\n", "    BETA_deliWeather_good = Beta('BETA_deliWeather_good', 0, None, None, 0)\n", "    BETA_deliWeather_spit = Beta('BETA_deliWeather_spit', 0, None, None, 0)\n", "\n", "    BETA_deliLocation_inter = Beta('BETA_deliLocation_inter', 0, None, None, 0)\n", "    BETA_deliLocation_straight = Beta('BETA_deliLocation_straight', 0, None, None, 0)\n", "    BETA_deliLocation_curve = Beta('BETA_deliLocation_curve', 0, None, None, 0)\n", "    BETA_deliLocation_pede = Beta('BETA_deliLocation_pede', 0, None, None, 0)\n", "\n", "    BETA_RemainTime_short = Beta('BETA_RemainTime_short', 0, None, None, 0)\n", "\n", "    BETA_CauseUnsafe_overspeed = Beta('BETA_CauseUnsafe_overspeed', 0, None, None, 0)\n", "    BETA_CauseUnsafe_breakrule = Beta('BETA_CauseUnsafe_breakrule', 0, None, None, 0)\n", "    BETA_CauseUnsafe_drowsy = Beta('BETA_CauseUnsafe_drowsy', 0, None, None, 0)\n", "    BETA_CauseUnsafe_emergency = Beta('BETA_CauseUnsafe_emergency', 0, None, None, 0)\n", "    BETA_CauseUnsafe_phone = Beta('BETA_CauseUnsafe_phone', 0, None, None, 0)\n", "    BETA_CauseUnsafe_without = Beta('BETA_CauseUnsafe_without', 0, None, None, 0)\n", "\n", "    BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)\n", "    BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)\n", "    # BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)\n", "    BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)\n", "    BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)\n", "\n", "    BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)\n", "    BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)\n", "    # BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)\n", "    BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)\n", "    BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)\n", "\n", "    BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)\n", "    BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)\n", "    BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)\n", "    BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)\n", "    BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)\n", "\n", "    BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)\n", "    BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)\n", "    BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)\n", "    BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)\n", "    BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)\n", "    BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)\n", "    BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)\n", "    BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)\n", "    BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)\n", "    BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)\n", "    BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)\n", "    BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)\n", "    BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)\n", "    BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)\n", "    BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)\n", "    BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)\n", "    BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)\n", "\n", "    BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)\n", "\n", "    BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)\n", "    BETA_age_between_24_35 = Beta('BETA_age_between_24_35', 0.0, None, None, 0)\n", "    BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)\n", "\n", "    BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)\n", "    BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)\n", "\n", "    BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)\n", "\n", "    BETA_children = Beta('BETA_children', 0.0, None, None, 0)\n", "\n", "    BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)\n", "    BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)\n", "    BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)\n", "\n", "    BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)\n", "    BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)\n", "    BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)\n", "\n", "    BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)\n", "    BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)\n", "    BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)\n", "    BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)\n", "\n", "    BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)\n", "    BETA_disposableincome_more_10000 = Beta('BETA_disposableincome_more_10000', 0.0, None, None, 0)\n", "\n", "    BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)\n", "\n", "    #* structual equations\n", "    sigma_1 = Beta('sigma_1', 1, None, None, 0)\n", "    omega_1 = bioDraws('omega_1', 'NORMAL_MLHS')\n", "\n", "    # coef_citytime_Att * citytime_less_3 + \\\n", "    # coef_age_Att  * age_less_24 + \\\n", "    Att = coef_intercept_Att + \\\n", "        coef_age_Att  * age_less_24 + \\\n", "        coef_gender_Att  * gender_female + \\\n", "        coef_edu_Att * edu_less_junior + \\\n", "        coef_marriage_Att * marriage_not + \\\n", "        coef_children_Att * children + \\\n", "        coef_family_Att * family_big + \\\n", "        coef_monthincome_Att * monthincome_less_4000 + \\\n", "        coef_disposableincome_Att * disposableincome_more_10000\n", "    # + \\\n", "    #     omega_1 * sigma_1\n", "\n", "    MODEL_Att2 = INTER_Att2 + B_Att2 * Att\n", "    MODEL_Att3 = INTER_Att3 + B_Att3 * Att\n", "    MODEL_Att4 = INTER_Att4 + B_Att4 * Att\n", "    MODEL_Att5 = INTER_Att5 + B_Att5 * Att\n", "    MODEL_Att6 = INTER_Att6 + B_Att6 * Att\n", "    \n", "\n", "    sigma_7 = Beta('sigma_7', 1, None, None, 0)\n", "    omega_7 = bioDraws('omega_7', 'NORMAL_MLHS')\n", "\n", "        # coef_citytime_Habit * citytime_less_3 + \\\n", "    Habit = coef_intercept_Habit + \\\n", "        coef_gender_Habit  * gender_female + \\\n", "        coef_age_Habit  * age_less_24 + \\\n", "        coef_edu_Habit * edu_less_junior + \\\n", "        coef_marriage_Habit * marriage_not + \\\n", "        coef_children_Habit * children + \\\n", "        coef_family_Habit * family_big + \\\n", "        coef_monthincome_Habit * monthincome_less_4000 + \\\n", "        coef_disposableincome_Habit * disposableincome_more_10000\n", "    # + \\\n", "    #     omega_7 * sigma_7\n", "\n", "    MODEL_Habit1 = INTER_Habit_1 + B_Habit1 * Habit\n", "    MODEL_Habit2 = INTER_Habit_2 + B_Habit2 * Habit\n", "    MODEL_Habit3 = INTER_Habit_3 + B_Habit3 * Habit\n", "    MODEL_Habit4 = INTER_Habit_4 + B_Habit4 * Habit\n", "    MODEL_Habit5 = INTER_Habit_5 + B_Habit5 * Habit\n", "\n", "    # As the measurements are using a Likert scale with M = 5 levels, we deﬁne 4 parameters\n", "    delta_1 = Beta('delta_1', 0.1, 1.0e-5, None, 0)\n", "    delta_2 = Beta('delta_2', 0.2, 1.0e-5, None, 0)\n", "    tau_1 = -delta_1 - delta_2\n", "    tau_2 = -delta_1\n", "    tau_3 = delta_1\n", "    tau_4 = delta_1 + delta_2\n", "\n", "    #* measurement equations\n", "    Att2_tau_1 = (tau_1 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_2 = (tau_2 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_3 = (tau_3 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_4 = (tau_4 - MODEL_Att2) / SIGMA_Att2\n", "    IndiAtt2 = {\n", "        1: bioNormalCdf(Att2_tau_1),\n", "        2: bioNormalCdf(Att2_tau_2) - bioNormalCdf(Att2_tau_1),\n", "        3: bioNormalCdf(Att2_tau_3) - bioNormalCdf(Att2_tau_2),\n", "        4: bioNormalCdf(Att2_tau_4) - bioNormalCdf(Att2_tau_3),\n", "        5: 1 - bioNormalCdf(Att2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att2 = Elem(IndiAtt2, Attitude2)\n", "\n", "    Att3_tau_1 = (tau_1 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_2 = (tau_2 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_3 = (tau_3 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_4 = (tau_4 - MODEL_Att3) / SIGMA_Att3\n", "    IndiAtt3 = {\n", "        1: bioNormalCdf(Att3_tau_1),\n", "        2: bioNormalCdf(Att3_tau_2) - bioNormalCdf(Att3_tau_1),\n", "        3: bioNormalCdf(Att3_tau_3) - bioNormalCdf(Att3_tau_2),\n", "        4: bioNormalCdf(Att3_tau_4) - bioNormalCdf(Att3_tau_3),\n", "        5: 1 - bioNormalCdf(Att3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att3 = Elem(IndiAtt3, Attitude3)\n", "\n", "    Att4_tau_1 = (tau_1 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_2 = (tau_2 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_3 = (tau_3 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_4 = (tau_4 - MODEL_Att4) / SIGMA_Att4\n", "    IndiAtt4 = {\n", "        1: bioNormalCdf(Att4_tau_1),\n", "        2: bioNormalCdf(Att4_tau_2) - bioNormalCdf(Att4_tau_1),\n", "        3: bioNormalCdf(Att4_tau_3) - bioNormalCdf(Att4_tau_2),\n", "        4: bioNormalCdf(Att4_tau_4) - bioNormalCdf(Att4_tau_3),\n", "        5: 1 - bioNormalCdf(Att4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att4 = Elem(IndiAtt4, Attitude4)\n", "\n", "    Att5_tau_1 = (tau_1 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_2 = (tau_2 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_3 = (tau_3 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_4 = (tau_4 - MODEL_Att5) / SIGMA_Att5\n", "    IndiAtt5 = {\n", "        1: bioNormalCdf(Att5_tau_1),\n", "        2: bioNormalCdf(Att5_tau_2) - bioNormalCdf(Att5_tau_1),\n", "        3: bioNormalCdf(Att5_tau_3) - bioNormalCdf(Att5_tau_2),\n", "        4: bioNormalCdf(Att5_tau_4) - bioNormalCdf(Att5_tau_3),\n", "        5: 1 - bioNormalCdf(Att5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att5 = Elem(IndiAtt5, Attitude5)\n", "\n", "    Att6_tau_1 = (tau_1 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_2 = (tau_2 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_3 = (tau_3 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_4 = (tau_4 - MODEL_Att6) / SIGMA_Att6\n", "    IndiAtt6 = {\n", "        1: bioNormalCdf(Att6_tau_1),\n", "        2: bioNormalCdf(Att6_tau_2) - bioNormalCdf(Att6_tau_1),\n", "        3: bioNormalCdf(Att6_tau_3) - bioNormalCdf(Att6_tau_2),\n", "        4: bioNormalCdf(Att6_tau_4) - bioNormalCdf(Att6_tau_3),\n", "        5: 1 - bioNormalCdf(Att6_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att6 = Elem(IndiAtt6, Attitude6)\n", "\n", "\n", "    Habit1_tau_1 = (tau_1 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_2 = (tau_2 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_3 = (tau_3 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_4 = (tau_4 - MODEL_Habit1) / SIGMA_Habit1\n", "    IndiHabit1 = {\n", "        1: bioNormalCdf(Habit1_tau_1),\n", "        2: bioNormalCdf(Habit1_tau_2) - bioNormalCdf(Habit1_tau_1),\n", "        3: bioNormalCdf(Habit1_tau_3) - bioNormalCdf(Habit1_tau_2),\n", "        4: bioNormalCdf(Habit1_tau_4) - bioNormalCdf(Habit1_tau_3),\n", "        5: 1 - bioNormalCdf(Habit1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit1 = Elem(IndiHabit1, Habit1)\n", "\n", "    Habit2_tau_1 = (tau_1 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_2 = (tau_2 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_3 = (tau_3 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_4 = (tau_4 - MODEL_Habit2) / SIGMA_Habit2\n", "    IndiHabit2 = {\n", "        1: bioNormalCdf(Habit2_tau_1),\n", "        2: bioNormalCdf(Habit2_tau_2) - bioNormalCdf(Habit2_tau_1),\n", "        3: bioNormalCdf(Habit2_tau_3) - bioNormalCdf(Habit2_tau_2),\n", "        4: bioNormalCdf(Habit2_tau_4) - bioNormalCdf(Habit2_tau_3),\n", "        5: 1 - bioNormalCdf(Habit2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit2 = Elem(IndiHabit2, Habit2)\n", "\n", "    Habit3_tau_1 = (tau_1 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_2 = (tau_2 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_3 = (tau_3 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_4 = (tau_4 - MODEL_Habit3) / SIGMA_Habit3\n", "    IndiHabit3 = {\n", "        1: bioNormalCdf(Habit3_tau_1),\n", "        2: bioNormalCdf(Habit3_tau_2) - bioNormalCdf(Habit3_tau_1),\n", "        3: bioNormalCdf(Habit3_tau_3) - bioNormalCdf(Habit3_tau_2),\n", "        4: bioNormalCdf(Habit3_tau_4) - bioNormalCdf(Habit3_tau_3),\n", "        5: 1 - bioNormalCdf(Habit3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit3 = Elem(IndiHabit3, Habit3)\n", "\n", "    Habit4_tau_1 = (tau_1 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_2 = (tau_2 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_3 = (tau_3 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_4 = (tau_4 - MODEL_Habit4) / SIGMA_Habit4\n", "    IndiHabit4 = {\n", "        1: bioNormalCdf(Habit4_tau_1),\n", "        2: bioNormalCdf(Habit4_tau_2) - bioNormalCdf(Habit4_tau_1),\n", "        3: bioNormalCdf(Habit4_tau_3) - bioNormalCdf(Habit4_tau_2),\n", "        4: bioNormalCdf(Habit4_tau_4) - bioNormalCdf(Habit4_tau_3),\n", "        5: 1 - bioNormalCdf(Habit4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit4 = Elem(IndiHabit4, Habit4)\n", "\n", "    Habit5_tau_1 = (tau_1 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_2 = (tau_2 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_3 = (tau_3 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_4 = (tau_4 - MODEL_Habit5) / SIGMA_Habit5\n", "    IndiHabit5 = {\n", "        1: bioNormalCdf(Habit5_tau_1),\n", "        2: bioNormalCdf(Habit5_tau_2) - bioNormalCdf(Habit5_tau_1),\n", "        3: bioNormalCdf(Habit5_tau_3) - bioNormalCdf(Habit5_tau_2),\n", "        4: bioNormalCdf(Habit5_tau_4) - bioNormalCdf(Habit5_tau_3),\n", "        5: 1 - bioNormalCdf(Habit5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit5 = Elem(IndiHabit5, Habit5)\n", "\n", "    V1 = BETA_intercept + \\\n", "            BETA_deliverytime_short * deliverytime_short + \\\n", "            BETA_deliverymode_full * deliverymode_full + \\\n", "            BETA_deliveryposition_extended * deliveryposition_extended + \\\n", "            BETA_workingdays_standard * workingdays_standard + \\\n", "            BETA_workinghours_brief * workinghours_brief + \\\n", "            BETA_deliveryorders_brief * deliveryorders_brief + \\\n", "            BETA_deliveryrange_prolonged * deliveryrange_prolonged + \\\n", "            BETA_deliveryspeed_extended * deliveryspeed_extended + \\\n", "            BETA_gender_female  * gender_female + \\\n", "            BETA_age_less_24 * age_less_24 + \\\n", "            BETA_edu_less_junior * edu_less_junior + \\\n", "            BETA_marriage * marriage_not + \\\n", "            BETA_children * children + \\\n", "            BETA_family_big * family_big + \\\n", "            BETA_monthincome_less_4000 * monthincome_less_4000 + \\\n", "            BETA_disposableincome_more_10000 * disposableincome_more_10000 + \\\n", "            BETA_Habit * Habit + \\\n", "            BETA_Att * Att\n", "\n", "    V2 = 0\n", "\n", "    # Associate utility functions with the numbering of alternatives\n", "    V = {1: V1,\n", "        2: V2,\n", "        }\n", "    \n", "    # mode probability\n", "    prob_accident = models.logit(V, None, 1) \n", "    prob_safe  = models.logit(V, None, 2)\n", "\n", "    # Calculation of the direct elasticities. We use the ‘Derive’ operator to calculate the derivatives.\n", "    direct_elas_gender = Derive(prob_accident, 'gender_female') * gender_female / prob_accident\n", "    direct_elas_age = Derive(prob_accident, 'age_less_24') * age_less_24 / prob_accident\n", "    direct_elas_edu = Derive(prob_accident, 'edu_less_junior') * edu_less_junior / prob_accident\n", "    \n", "    simulate = {\n", "    'prob_accident': prob_accident,\n", "    'prob_safe': prob_safe,\n", "    'direct_elas_gender': direct_elas_gender,\n", "    'direct_elas_age': direct_elas_age,\n", "    'direct_elas_edu': direct_elas_edu,\n", "    }\n", "\n", "    the_biogeme = bio.BIOGEME(database, simulate)\n", "    the_biogeme.modelName = 'accident_simulation_elas'\n", "    results = res.bioResults(pickleFile='accident_iclv_full.pickle')\n", "\n", "    simulated_values = the_biogeme.simulate(results.getBetaValues())\n", "\n", "    print(simulated_values)\n", "\n", "    # We calculate the aggregate elasticities.\n", "    denominator_accident = simulated_values['prob_accident'].sum()\n", "    denominator_safe = simulated_values['prob_safe'].sum()\n", "\n", "    direct_elas_term_gender = (\n", "        simulated_values['prob_accident']\n", "        * simulated_values['direct_elas_gender']\n", "        / denominator_accident\n", "    ).sum()\n", "\n", "    print(\n", "        f'Aggregate direct point elasticity of accident with gender: '\n", "        f'{direct_elas_term_gender:.3g}'\n", "    )\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# result"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Parameter B_Att3 not present in the model.\n", "Parameter B_Att4 not present in the model.\n", "Parameter B_Att5 not present in the model.\n", "Parameter B_Att6 not present in the model.\n", "Parameter B_Habit2 not present in the model.\n", "Parameter B_Habit3 not present in the model.\n", "Parameter B_Habit4 not present in the model.\n", "Parameter B_Habit5 not present in the model.\n", "Parameter INTER_Att3 not present in the model.\n", "Parameter INTER_Att4 not present in the model.\n", "Parameter INTER_Att5 not present in the model.\n", "Parameter INTER_Att6 not present in the model.\n", "Parameter INTER_Habit_2 not present in the model.\n", "Parameter INTER_Habit_3 not present in the model.\n", "Parameter INTER_Habit_4 not present in the model.\n", "Parameter INTER_Habit_5 not present in the model.\n", "Parameter SIGMA_Att3 not present in the model.\n", "Parameter SIGMA_Att4 not present in the model.\n", "Parameter SIGMA_Att5 not present in the model.\n", "Parameter SIGMA_Att6 not present in the model.\n", "Parameter SIGMA_Habit2 not present in the model.\n", "Parameter SIGMA_Habit3 not present in the model.\n", "Parameter SIGMA_Habit4 not present in the model.\n", "Parameter SIGMA_Habit5 not present in the model.\n", "Parameter delta_1 not present in the model.\n", "Parameter delta_2 not present in the model.\n", "Parameter sigma_s1 not present in the model.\n", "Parameter sigma_s2 not present in the model.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["     prob_accident  prob_safe  direct_elas_gender  direct_elas_age  \\\n", "2         0.423680   0.576320            0.000000        -0.285282   \n", "6         0.070890   0.929110            0.000000        -0.459915   \n", "9         0.309348   0.690652            0.000000        -0.341877   \n", "10        0.043170   0.956830           -0.713313        -0.473637   \n", "11        0.023889   0.976111           -0.727687         0.000000   \n", "..             ...        ...                 ...              ...   \n", "408       0.108556   0.891444           -0.664568         0.000000   \n", "410       0.108556   0.891444           -0.664568         0.000000   \n", "411       0.008915   0.991085           -0.738850         0.000000   \n", "412       0.086772   0.913228           -0.680808         0.000000   \n", "413       0.026408   0.973592           -0.725809         0.000000   \n", "\n", "     direct_elas_edu  \n", "2                0.0  \n", "6                0.0  \n", "9                0.0  \n", "10               0.0  \n", "11               0.0  \n", "..               ...  \n", "408              0.0  \n", "410              0.0  \n", "411              0.0  \n", "412              0.0  \n", "413              0.0  \n", "\n", "[276 rows x 5 columns]\n", "Aggregate direct point elasticity of accident with gender: -0.104\n"]}], "source": ["# 生成0-1对角数组，numpy.eye(N,M=None, k=0, dtype=<type 'float'>)\n", "\n", "dforigin = pd.read_excel('new_412.xlsx')\n", "df = dforigin[dforigin['QeTime'] >= 120]\n", "df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]\n", "\n", "# origin1, origin2 = margin_effect(df)\n", "point_elasticities(df)\n", "# print(origin1, origin2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tevrp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}