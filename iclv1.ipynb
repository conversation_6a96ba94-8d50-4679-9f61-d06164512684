{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ICLV MODEL"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[14:31:33] < General >   Remove 115 unused variables from the database as only 27 are used.\n", "[14:31:36] < General >   Log likelihood (N = 276):  -5526.609 Gradient norm:      7e+03  \n", "[14:31:46] < General >   Log likelihood (N = 276):  -3660.932 Gradient norm:      2e+03  \n", "[14:31:53] < General >   Log likelihood (N = 276):  -3343.153 Gradient norm:      7e+02  \n", "[14:32:03] < General >   Log likelihood (N = 276):  -3249.118 Gradient norm:      3e+02  \n", "[14:32:09] < General >   Log likelihood (N = 276):  -3217.668 Gradient norm:      1e+02  \n", "[14:32:19] < General >   Log likelihood (N = 276):  -3209.115 Gradient norm:      7e+01  \n", "[14:32:25] < General >   Log likelihood (N = 276):    -3201.2 Gradient norm:      8e+01  \n", "[14:32:35] < General >   Log likelihood (N = 276):  -3195.433 Gradient norm:      7e+01  \n", "[14:32:42] < General >   Log likelihood (N = 276):  -3188.633 Gradient norm:      6e+01  \n", "[14:32:48] < General >   Log likelihood (N = 276):  -3182.363 Gradient norm:      8e+01  \n", "[14:32:55] < General >   Log likelihood (N = 276):  -3178.428 Gradient norm:      8e+01  \n", "[14:33:01] < General >   Log likelihood (N = 276):  -3173.771 Gradient norm:      7e+01  \n", "[14:33:08] < General >   Log likelihood (N = 276):  -3170.598 Gradient norm:      6e+01  \n", "[14:33:15] < General >   Log likelihood (N = 276):  -3166.837 Gradient norm:      3e+01  \n", "[14:33:21] < General >   Log likelihood (N = 276):  -3164.346 Gradient norm:      3e+01  \n", "[14:33:28] < General >   Log likelihood (N = 276):  -3162.297 Gradient norm:      3e+01  \n", "[14:33:34] < General >   Log likelihood (N = 276):  -3160.836 Gradient norm:      2e+01  \n", "[14:33:41] < General >   Log likelihood (N = 276):  -3159.847 Gradient norm:      2e+01  \n", "[14:33:47] < General >   Log likelihood (N = 276):  -3159.042 Gradient norm:      2e+01  \n", "[14:33:54] < General >   Log likelihood (N = 276):   -3158.45 Gradient norm:      1e+01  \n", "[14:34:00] < General >   Log likelihood (N = 276):  -3158.035 Gradient norm:      1e+01  \n", "[14:34:06] < General >   Log likelihood (N = 276):  -3157.669 Gradient norm:      2e+01  \n", "[14:34:13] < General >   Log likelihood (N = 276):  -3157.302 Gradient norm:      2e+01  \n", "[14:34:19] < General >   Log likelihood (N = 276):  -3156.959 Gradient norm:          9  \n", "[14:34:26] < General >   Log likelihood (N = 276):  -3156.682 Gradient norm:          8  \n", "[14:34:32] < General >   Log likelihood (N = 276):  -3156.299 Gradient norm:      1e+01  \n", "[14:34:39] < General >   Log likelihood (N = 276):  -3155.913 Gradient norm:      1e+01  \n", "[14:34:45] < General >   Log likelihood (N = 276):  -3155.626 Gradient norm:      1e+01  \n", "[14:34:52] < General >   Log likelihood (N = 276):  -3155.405 Gradient norm:          7  \n", "[14:34:58] < General >   Log likelihood (N = 276):   -3155.18 Gradient norm:          6  \n", "[14:35:04] < General >   Log likelihood (N = 276):  -3155.006 Gradient norm:          6  \n", "[14:35:11] < General >   Log likelihood (N = 276):  -3154.853 Gradient norm:          4  \n", "[14:35:17] < General >   Log likelihood (N = 276):  -3154.748 Gradient norm:          5  \n", "[14:35:24] < General >   Log likelihood (N = 276):  -3154.671 Gradient norm:          4  \n", "[14:35:30] < General >   Log likelihood (N = 276):  -3154.608 Gradient norm:          3  \n", "[14:35:37] < General >   Log likelihood (N = 276):  -3154.566 Gradient norm:          2  \n", "[14:35:43] < General >   Log likelihood (N = 276):  -3154.541 Gradient norm:          3  \n", "[14:35:50] < General >   Log likelihood (N = 276):   -3154.52 Gradient norm:          3  \n", "[14:35:56] < General >   Log likelihood (N = 276):  -3154.501 Gradient norm:          2  \n", "[14:36:03] < General >   Log likelihood (N = 276):  -3154.483 Gradient norm:          2  \n", "[14:36:09] < General >   Log likelihood (N = 276):   -3154.47 Gradient norm:          2  \n", "[14:36:16] < General >   Log likelihood (N = 276):  -3154.458 Gradient norm:          2  \n", "[14:36:22] < General >   Log likelihood (N = 276):  -3154.447 Gradient norm:          2  \n", "[14:36:29] < General >   Log likelihood (N = 276):  -3154.438 Gradient norm:          1  \n", "[14:36:35] < General >   Log likelihood (N = 276):  -3154.431 Gradient norm:          1  \n", "[14:36:42] < General >   Log likelihood (N = 276):  -3154.423 Gradient norm:          1  \n", "[14:36:48] < General >   Log likelihood (N = 276):  -3154.417 Gradient norm:          1  \n", "[14:36:55] < General >   Log likelihood (N = 276):  -3154.411 Gradient norm:          1  \n", "[14:37:01] < General >   Log likelihood (N = 276):  -3154.408 Gradient norm:        0.8  \n", "[14:37:08] < General >   Log likelihood (N = 276):  -3154.405 Gradient norm:        0.6  \n", "[14:37:14] < General >   Log likelihood (N = 276):  -3154.404 Gradient norm:        0.5  \n", "[14:37:21] < General >   Log likelihood (N = 276):  -3154.402 Gradient norm:        0.5  \n", "[14:37:27] < General >   Log likelihood (N = 276):  -3154.401 Gradient norm:        0.5  \n", "[14:37:34] < General >   Log likelihood (N = 276):  -3154.399 Gradient norm:        0.4  \n", "[14:37:40] < General >   Log likelihood (N = 276):  -3154.398 Gradient norm:        0.4  \n", "[14:37:47] < General >   Log likelihood (N = 276):  -3154.398 Gradient norm:        0.5  \n", "[14:37:53] < General >   Log likelihood (N = 276):  -3154.397 Gradient norm:        0.5  \n", "[14:38:00] < General >   Log likelihood (N = 276):  -3154.396 Gradient norm:        0.5  \n", "[14:38:06] < General >   Log likelihood (N = 276):  -3154.395 Gradient norm:        0.4  \n", "[14:38:13] < General >   Log likelihood (N = 276):  -3154.395 Gradient norm:        0.2  \n", "[14:38:19] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.2  \n", "[14:38:26] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.1  \n", "[14:38:32] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.1  \n", "[14:38:39] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.2  \n", "[14:38:45] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.2  \n", "[14:38:52] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.1  \n", "[14:38:59] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.1  \n", "[14:39:05] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.1  \n", "[14:39:12] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.2  \n", "[14:39:18] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:        0.1  \n", "[14:39:25] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:       0.07  \n", "[14:40:47] < General >   Log likelihood (N = 276):  -3154.394 Gradient norm:       0.07 Hessian norm:       6e+03 BHHH norm:       2e+04\n", "[14:40:48] < General >   Results saved in file accident_iclv_Att_Habit2024-07-17_14-31-34.html\n", "[14:40:48] < General >   Results saved in file accident_iclv_Att_Habit2024-07-17_14-31-34.pickle\n", "Estimated betas: 65\n", "Final log likelihood: -3154.394\n", "Output file: accident_iclv_Att_Habit2024-07-17_14-31-34.html\n", "                                 Value   Std err    t-test   p-value  \\\n", "BETA_Att                      0.604866  0.186176  3.248895  0.001159   \n", "BETA_Habit                    0.683512  0.243960  2.801738  0.005083   \n", "BETA_deliveryorders_brief     1.767742  0.637456  2.773119  0.005552   \n", "BETA_deliveryrange_prolonged -2.061261  0.613467 -3.360017  0.000779   \n", "BETA_deliveryspeed_extended   1.091014  0.583759  1.868945  0.061630   \n", "BETA_edu_less_junior          1.178445  0.480582  2.452122  0.014202   \n", "BETA_family_big               0.885233  0.411226  2.152666  0.031345   \n", "BETA_workingdays_standard    -1.263094  0.489732 -2.579154  0.009904   \n", "\n", "                              Rob. Std err  Rob. t-test  Rob. p-value  \n", "BETA_Att                          0.205668     2.940977      0.003272  \n", "BETA_Habit                        0.270627     2.525659      0.011548  \n", "BETA_deliveryorders_brief         0.712791     2.480029      0.013137  \n", "BETA_deliveryrange_prolonged      0.618997    -3.329999      0.000868  \n", "BETA_deliveryspeed_extended       0.556872     1.959182      0.050091  \n", "BETA_edu_less_junior              0.495113     2.380154      0.017305  \n", "BETA_family_big                   0.424542     2.085147      0.037056  \n", "BETA_workingdays_standard         0.563966    -2.239661      0.025113  \n", "满足条件的行的数量为: 8\n"]}], "source": ["from datetime import datetime\n", "import pandas as pd\n", "\n", "import biogeme.database as db\n", "import biogeme.biogeme as bio\n", "import biogeme.models as models\n", "import biogeme.messaging as msg\n", "import os\n", "from biogeme.expressions import (\n", "    Beta,\n", "    Variable,\n", "    DefineVariable,\n", "    log,\n", "    RandomVariable,\n", "    Integrate,\n", "    Elem,\n", "    bioNormalCdf,\n", "    exp,\n", "    bioDraws,\n", "    MonteCarlo,\n", ")\n", "\n", "import shutil\n", "import matplotlib.pyplot as plt\n", "import copy\n", "\n", "dforigin = pd.read_excel('new_412.xlsx')\n", "unreli_index = pd.read_excel('question_id.xlsx', header= None)\n", "\n", "\n", "# 筛选无效问卷 删除 回答时间小于2min且 Attitude1 和 Attitude5回答一致 的问卷\n", "# for num_idx in [1,2,3]:\n", "for num_idx in [1]:\n", "    if num_idx == 1:\n", "        df = dforigin[dforigin['QeTime'] >= 120]\n", "        df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]\n", "    elif num_idx == 2: \n", "        df = copy.deepcopy(dforigin)\n", "    else:\n", "        df = dforigin[dforigin['QeTime'] >= 120]\n", "        df = df[~((df['Attitude1'] == df['Attitude6']))]\n", "\n", "    # df = df[df['QeTime'] >= 120]\n", "    # df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]\n", "    # 同学评估筛选\n", "    # df = df[~df.index.isin(unreli_index[0])]\n", "\n", "    database = db.Database('data', df)\n", "    globals().update(database.variables)\n", "\n", "    #* variable definition\n", "    # 场景属性自变量\n", "    deliVolume = Variable('Volume')\n", "    deliTime = Variable('Time')\n", "    deliWeather = Variable('Weather')\n", "    deliLocation = Variable('Location')\n", "    RemainTime = Variable('RemainTime')\n", "    CauseUnsafe = Variable('CauseUnsafe')\n", "\n", "    # 因变量\n", "    UnsafeAccident = Variable('UnsafeAccident')\n", "\n", "    # 社会经济属性自变量\n", "    gender = Variable('gender')\n", "    age = Variable('age')\n", "    education = Variable('education')\n", "    marriage = Variable('marriage')\n", "    family = Variable('family')\n", "    citytime = Variable('citytime')\n", "    monthincome = Variable('monthincome')\n", "    disposableincome = Variable('disposableincome')\n", "\n", "    # 配送属性自变量\n", "    deliverytime = Variable('deliverytime')\n", "    deliverymode = Variable('deliverymode')\n", "    deliveryposition = Variable('deliveryposition')\n", "    workingdays = Variable('workingdays')\n", "    workinghours = Variable('workinghours')\n", "    deliveryorders = Variable('deliveryorders')\n", "    deliveryrange = Variable('deliveryrange')\n", "    deliveryspeed = Variable('deliveryspeed')\n", "\n", "    # 社会经济属性二分类变量\n", "    gender_female = DefineVariable('gender_female', gender == 2, database)\n", "\n", "    age_less_24 = DefineVariable('age_less_24', age <= 2, database)\n", "    age_between_24_35 = DefineVariable('age_between_24_35', age == 3, database)\n", "    age_more_35 = DefineVariable('age_more_35', age >= 4, database)\n", "\n", "    edu_less_junior = DefineVariable('edu_less_junior', education <= 2, database)\n", "    edu_more_uni = DefineVariable('edu_more_uni', education >= 4, database)\n", "\n", "    marriage_not = DefineVariable('marriage_not', marriage <= 1, database)\n", "    children = DefineVariable('children', ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0), database)\n", "\n", "    family_small = DefineVariable('family_small', family <= 2, database)\n", "    family_middle = DefineVariable('family_middle', (family <= 4) + (family >= 3) > 0, database)\n", "    family_big = DefineVariable('family_big', (family >= 5) &  (family <= 7), database)\n", "\n", "    citytime_less_3 = DefineVariable('citytime_less_3', citytime <= 2, database)\n", "    citytime_more_6 = DefineVariable('citytime_more_6', citytime >= 4, database)\n", "    citytime_local = DefineVariable('citytime_local', citytime == 5, database)\n", "\n", "    monthincome_less_2000 = DefineVariable('monthincome_less_2000', monthincome <= 1, database)\n", "    monthincome_less_4000 = DefineVariable('monthincome_less_4000', monthincome <= 2, database)\n", "    monthincome_less_6000 = DefineVariable('monthincome_less_6000', monthincome <= 3, database)\n", "    monthincome_more_8000 = DefineVariable('monthincome_more_8000', monthincome >= 5, database)\n", "    monthincome_4000_8000 = DefineVariable('monthincome_4000_8000', (monthincome >= 3) & (monthincome <= 4), database)\n", "\n", "    disposableincome_less_5000 = DefineVariable('disposableincome_less_5000', disposableincome <= 1, database)\n", "    disposableincome_more_10000 = DefineVariable('disposableincome_more_10000', disposableincome >= 3, database)\n", "\n", "    # 场景属性二分类变量\n", "    deliVolume_low = DefineVariable('deliVolume_low', deliVolume <= 1, database)\n", "    deliVolume_middle = DefineVariable('deliVolume_middle', deliVolume == 2, database)\n", "    deliVolume_high = DefineVariable('deliVolume_high', deliVolume >= 3, database)\n", "\n", "    deliTime_morning = DefineVariable('deliTime_morning', deliTime == 3, database)\n", "    deliTime_noon = DefineVariable('deliTime_noon', deliTime == 1, database)\n", "    deliTime_evening = DefineVariable('deliTime_evening', deliTime == 2, database)\n", "    deliTime_afternoon = DefineVariable('deliTime_afternoon', deliTime == 4, database)\n", "    deliTime_night = DefineVariable('deliTime_night', deliTime >= 5, database)\n", "\n", "    deliWeather_heavy = DefineVariable('deliWeather_heavy', deliWeather == 1, database)\n", "    deliWeather_good = DefineVariable('deliWeather_good', deliWeather == 2, database)\n", "    deliWeather_spit = DefineVariable('deliWeather_spit', deliWeather == 3, database)\n", "\n", "    deliLocation_inter = DefineVariable('deliWeather_inter', deliLocation == 1, database)\n", "    deliLocation_straight = DefineVariable('deliLocation_straight', deliLocation == 2, database)\n", "    deliLocation_curve = DefineVariable('deliLocation_curve', deliLocation == 3, database)\n", "    deliLocation_pede = DefineVariable('deliLocation_pede', deliLocation == 5, database)\n", "\n", "    RemainTime_short = DefineVariable('RemainTime_short', RemainTime <= 1, database)\n", "\n", "    CauseUnsafe_overspeed = DefineVariable('CauseUnsafe_overspeed', CauseUnsafe == 1, database)\n", "    CauseUnsafe_breakrule = DefineVariable('CauseUnsafe_breakrule', CauseUnsafe == 2, database)\n", "    CauseUnsafe_drowsy = DefineVariable('CauseUnsafe_drowsy', CauseUnsafe == 3, database)\n", "    CauseUnsafe_emergency = DefineVariable('CauseUnsafe_emergency', CauseUnsafe == 4, database)\n", "    CauseUnsafe_phone = DefineVariable('CauseUnsafe_phone', CauseUnsafe == 5, database)\n", "    CauseUnsafe_without = DefineVariable('CauseUnsafe_without', CauseUnsafe == 6, database)\n", "\n", "    # 配送属性二分类变量\n", "    deliverytime_brief = DefineVariable('deliverytime_brief', deliverytime == 1, database)\n", "    deliverytime_short = DefineVariable('deliverytime_short', deliverytime <= 2, database)\n", "    deliverytime_extended = DefineVariable('deliverytime_extended', deliverytime >= 3, database)\n", "    deliverytime_prolonged = DefineVariable('deliverytime_prolonged', deliverytime  == 4, database)\n", "\n", "    deliverymode_full = DefineVariable('deliverymode_full', deliverymode >= 2, database)\n", "\n", "    deliveryposition_brief = DefineVariable('deliveryposition_brief', deliveryposition <= 1, database)\n", "    deliveryposition_short = DefineVariable('deliveryposition_short', deliveryposition <= 2, database)\n", "    deliveryposition_extended = DefineVariable('deliveryposition_extended', deliveryposition >= 3, database)\n", "    deliveryposition_prolonged = DefineVariable('deliveryposition_prolonged', deliveryposition  >= 4, database)\n", "\n", "    workingdays_brief = DefineVariable('workingdays_brief', workingdays <= 1, database)\n", "    workingdays_short = DefineVariable('workingdays_short', workingdays <= 2, database)\n", "    workingdays_standard = DefineVariable('workingdays_standard', workingdays <= 3, database)\n", "    workingdays_extended = DefineVariable('workingdays_extended', workingdays  >= 5, database)\n", "    workingdays_prolonged = DefineVariable('workingdays_prolonged', workingdays  >= 6, database)\n", "\n", "    workinghours_brief = DefineVariable('workinghours_brief', workinghours <= 1, database)\n", "    workinghours_short = DefineVariable('workinghours_short', workinghours <= 2, database)\n", "    workinghours_standard = DefineVariable('workinghours_standard', workinghours >= 3, database)\n", "    workinghours_extended = DefineVariable('workinghours_extended', workinghours >= 4, database)\n", "    workinghours_prolonged = DefineVariable('workinghours_prolonged', workinghours >= 5, database)\n", "\n", "    deliveryorders_brief = DefineVariable('deliveryorders_brief', deliveryorders <= 1, database)\n", "    deliveryorders_short = DefineVariable('deliveryorders_short', deliveryorders <= 2, database)\n", "    deliveryorders_standard = DefineVariable('deliveryorders_standard', deliveryorders <= 3, database)\n", "    deliveryorders_extended = DefineVariable('deliveryorders_extended', deliveryorders >= 4, database)\n", "    deliveryorders_prolonged = DefineVariable('deliveryorders_prolonged', deliveryorders >= 5, database)\n", "\n", "    deliveryrange_brief = DefineVariable('deliveryrange_brief', deliveryrange <= 1, database)\n", "    deliveryrange_short = DefineVariable('deliveryrange_short', deliveryrange <= 2, database)\n", "    deliveryrange_standard = DefineVariable('deliveryrange_standard', deliveryrange <= 3, database)\n", "    deliveryrange_extended = DefineVariable('deliveryrange_extended', deliveryrange >= 4, database)\n", "    deliveryrange_prolonged = DefineVariable('deliveryrange_prolonged', deliveryrange >= 5, database)\n", "\n", "    deliveryspeed_brief = DefineVariable('deliveryspeed_brief', deliveryspeed <= 1, database)\n", "    deliveryspeed_short = DefineVariable('deliveryspeed_short', deliveryspeed <= 2, database)\n", "    deliveryspeed_standard = DefineVariable('deliveryspeed_standard', deliveryspeed >= 3, database)\n", "    #!\n", "    deliveryspeed_extended = DefineVariable('deliveryspeed_extended', deliveryspeed == 4, database)\n", "    deliveryspeed_prolonged = DefineVariable('deliveryspeed_prolonged', deliveryspeed == 5, database)\n", "\n", "    #* structual coefficient\n", "    # Attitude\n", "    coef_intercept_Att = Beta('coef_intercept_Att', 0.0, None, None, 0)\n", "    coef_gender_Att = Beta('coef_gender_Att', 0.0, None, None, 0)\n", "    coef_age_Att = Beta('coef_age_Att', 0.0, None, None, 0)\n", "    coef_edu_Att = Beta('coef_edu_Att', 0.0, None, None, 0)\n", "    coef_marriage_Att = Beta('coef_marriage_Att', 0.0, None, None, 0)\n", "    coef_children_Att = Beta('coef_children_Att', 0.0, None, None, 0)\n", "    coef_family_Att = Beta('coef_family_Att', 0.0, None, None, 0)\n", "    coef_citytime_Att = Beta('coef_citytime_Att', 0.0, None, None, 0)\n", "    coef_monthincome_Att = Beta('coef_monthincome_Att', 0.0, None, None, 0)\n", "    coef_disposableincome_Att = Beta('coef_disposableincome_Att', 0.0, None, None, 0)\n", "    # Unsafe behavior\n", "    coef_intercept_Unsafe = Beta('coef_intercept_Unsafe', 0.0, None, None, 0)\n", "    coef_gender_Unsafe = Beta('coef_gender_Unsafe', 0.0, None, None, 0)\n", "    coef_age_Unsafe = Beta('coef_age_Unsafe', 0.0, None, None, 0)\n", "    coef_edu_Unsafe = Beta('coef_edu_Unsafe', 0.0, None, None, 0)\n", "    coef_marriage_Unsafe = Beta('coef_marriage_Unsafe', 0.0, None, None, 0)\n", "    coef_children_Unsafe = Beta('coef_children_Unsafe', 0.0, None, None, 0)\n", "    coef_family_Unsafe = Beta('coef_family_Unsafe', 0.0, None, None, 0)\n", "    coef_citytime_Unsafe = Beta('coef_citytime_Unsafe', 0.0, None, None, 0)\n", "    coef_monthincome_Unsafe = Beta('coef_monthincome_Unsafe', 0.0, None, None, 0)\n", "    coef_disposableincome_Unsafe = Beta('coef_disposableincome_Unsafe', 0.0, None, None, 0)\n", "    # Environment\n", "    coef_intercept_Envi = Beta('coef_intercept_Envi', 0.0, None, None, 0)\n", "    coef_gender_Envi = Beta('coef_gender_Envi', 0.0, None, None, 0)\n", "    coef_age_Envi = Beta('coef_age_Envi', 0.0, None, None, 0)\n", "    coef_edu_Envi = Beta('coef_edu_Envi', 0.0, None, None, 0)\n", "    coef_marriage_Envi = Beta('coef_marriage_Envi', 0.0, None, None, 0)\n", "    coef_children_Envi = Beta('coef_children_Envi', 0.0, None, None, 0)\n", "    coef_family_Envi = Beta('coef_family_Envi', 0.0, None, None, 0)\n", "    coef_citytime_Envi = Beta('coef_citytime_Envi', 0.0, None, None, 0)\n", "    coef_monthincome_Envi = Beta('coef_monthincome_Envi', 0.0, None, None, 0)\n", "    coef_disposableincome_Envi = Beta('coef_disposableincome_Envi', 0.0, None, None, 0)\n", "    # Pressure\n", "    coef_intercept_Press = Beta('coef_intercept_Press', 0.0, None, None, 0)\n", "    coef_gender_Press = Beta('coef_gender_Press', 0.0, None, None, 0)\n", "    coef_age_Press = Beta('coef_age_Press', 0.0, None, None, 0)\n", "    coef_edu_Press = Beta('coef_edu_Press', 0.0, None, None, 0)\n", "    coef_marriage_Press = Beta('coef_marriage_Press', 0.0, None, None, 0)\n", "    coef_children_Press = Beta('coef_children_Press', 0.0, None, None, 0)\n", "    coef_family_Press = Beta('coef_family_Press', 0.0, None, None, 0)\n", "    coef_citytime_Press = Beta('coef_citytime_Press', 0.0, None, None, 0)\n", "    coef_monthincome_Press = Beta('coef_monthincome_Press', 0.0, None, None, 0)\n", "    coef_disposableincome_Press = Beta('coef_disposableincome_Press', 0.0, None, None, 0)\n", "    # Control\n", "    coef_intercept_Con = Beta('coef_intercept_Con', 0.0, None, None, 0)\n", "    coef_gender_Con = Beta('coef_gender_Con', 0.0, None, None, 0)\n", "    coef_age_Con = Beta('coef_age_Con', 0.0, None, None, 0)\n", "    coef_edu_Con = Beta('coef_edu_Con', 0.0, None, None, 0)\n", "    coef_marriage_Con = Beta('coef_marriage_Con', 0.0, None, None, 0)\n", "    coef_children_Con = Beta('coef_children_Con', 0.0, None, None, 0)\n", "    coef_family_Con = Beta('coef_family_Con', 0.0, None, None, 0)\n", "    coef_citytime_Con = Beta('coef_citytime_Con', 0.0, None, None, 0)\n", "    coef_monthincome_Con = Beta('coef_monthincome_Con', 0.0, None, None, 0)\n", "    coef_disposableincome_Con = Beta('coef_disposableincome_Con', 0.0, None, None, 0)\n", "    # Delivery habit\n", "    coef_intercept_Deli = Beta('coef_intercept_Deli', 0.0, None, None, 0)\n", "    coef_gender_Deli = Beta('coef_gender_Deli', 0.0, None, None, 0)\n", "    coef_age_Deli = Beta('coef_age_Deli', 0.0, None, None, 0)\n", "    coef_edu_Deli = Beta('coef_edu_Deli', 0.0, None, None, 0)\n", "    coef_marriage_Deli = Beta('coef_marriage_Deli', 0.0, None, None, 0)\n", "    coef_children_Deli = Beta('coef_children_Deli', 0.0, None, None, 0)\n", "    coef_family_Deli = Beta('coef_family_Deli', 0.0, None, None, 0)\n", "    coef_citytime_Deli = Beta('coef_citytime_Deli', 0.0, None, None, 0)\n", "    coef_monthincome_Deli = Beta('coef_monthincome_Deli', 0.0, None, None, 0)\n", "    coef_disposableincome_Deli = Beta('coef_disposableincome_Deli', 0.0, None, None, 0)\n", "    # Habit\n", "    coef_intercept_Habit = Beta('coef_intercept_Habit', 0.0, None, None, 0)\n", "    coef_gender_Habit = Beta('coef_gender_Habit', 0.0, None, None, 0)\n", "    coef_age_Habit = Beta('coef_age_Habit', 0.0, None, None, 0)\n", "    coef_edu_Habit = Beta('coef_edu_Habit', 0.0, None, None, 0)\n", "    coef_marriage_Habit = Beta('coef_marriage_Habit', 0.0, None, None, 0)\n", "    coef_children_Habit = Beta('coef_children_Habit', 0.0, None, None, 0)\n", "    coef_family_Habit = Beta('coef_family_Habit', 0.0, None, None, 0)\n", "    coef_citytime_Habit = Beta('coef_citytime_Habit', 0.0, None, None, 0)\n", "    coef_monthincome_Habit = Beta('coef_monthincome_Habit', 0.0, None, None, 0)\n", "    coef_disposableincome_Habit = Beta('coef_disposableincome_Habit', 0.0, None, None, 0)\n", "    # Subjective\n", "    coef_intercept_Sub = Beta('coef_intercept_Sub', 0.0, None, None, 0)\n", "    coef_gender_Sub = Beta('coef_gender_Sub', 0.0, None, None, 0)\n", "    coef_age_Sub = Beta('coef_age_Sub', 0.0, None, None, 0)\n", "    coef_edu_Sub = Beta('coef_edu_Sub', 0.0, None, None, 0)\n", "    coef_marriage_Sub = Beta('coef_marriage_Sub', 0.0, None, None, 0)\n", "    coef_children_Sub = Beta('coef_children_Sub', 0.0, None, None, 0)\n", "    coef_family_Sub = Beta('coef_family_Sub', 0.0, None, None, 0)\n", "    coef_citytime_Sub = Beta('coef_citytime_Sub', 0.0, None, None, 0)\n", "    coef_monthincome_Sub = Beta('coef_monthincome_Sub', 0.0, None, None, 0)\n", "    coef_disposableincome_Sub = Beta('coef_disposableincome_Sub', 0.0, None, None, 0)\n", "\n", "    #* measurement coefficient\n", "    INTER_Att2 = Beta('INTER_Att2', 0, None, None, 1)\n", "    INTER_Att3 = Beta('INTER_Att3', 0, None, None, 0)\n", "    INTER_Att4 = Beta('INTER_Att4', 0, None, None, 0)\n", "    INTER_Att5 = Beta('INTER_Att5', 0, None, None, 0)\n", "    INTER_Att6 = Beta('INTER_Att6', 0, None, None, 0)\n", "\n", "    B_Att2 = Beta('B_Att2', 1, None, None, 1)\n", "    B_Att3 = Beta('B_Att3', 1, None, None, 0)\n", "    B_Att4 = Beta('B_Att4', 1, None, None, 0)\n", "    B_Att5 = Beta('B_Att5', 1, None, None, 0)\n", "    B_Att6 = Beta('B_Att6', 1, None, None, 0)\n", "\n", "    SIGMA_Att2 = Beta('SIGMA_Att2', 1, 1.0e-5, None, 1)\n", "    SIGMA_Att3 = Beta('SIGMA_Att3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att4 = Beta('SIGMA_Att4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att5 = Beta('SIGMA_Att5', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att6 = Beta('SIGMA_Att6', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Unsafe1 = Beta('INTER_Unsafe1', 0, None, None, 1)\n", "    INTER_Unsafe2 = Beta('INTER_Unsafe2', 0, None, None, 0)\n", "    INTER_Unsafe3 = Beta('INTER_Unsafe3', 0, None, None, 0)\n", "    INTER_Unsafe4 = Beta('INTER_Unsafe4', 0, None, None, 0)\n", "    INTER_Unsafe5 = Beta('INTER_Unsafe5', 0, None, None, 0)\n", "\n", "    B_Unsafe1 = Beta('B_Unsafe1', 1, None, None, 1)\n", "    B_Unsafe2 = Beta('B_Unsafe2', 1, None, None, 0)\n", "    B_Unsafe3 = Beta('B_Unsafe3', 1, None, None, 0)\n", "    B_Unsafe4 = Beta('B_Unsafe4', 1, None, None, 0)\n", "    B_Unsafe5 = Beta('B_Unsafe5', 1, None, None, 0)\n", "\n", "    SIGMA_Unsafe1 = Beta('SIGMA_Unsafe1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Unsafe2 = Beta('SIGMA_Unsafe2', 1, 1.0e-5, None, 0)\n", "    SIGMA_Unsafe3 = Beta('SIGMA_Unsafe3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Unsafe4 = Beta('SIGMA_Unsafe4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Unsafe5 = Beta('SIGMA_Unsafe5', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Envi1 = Beta('INTER_Envi1', 0, None, None, 1)\n", "    INTER_Envi2 = Beta('INTER_Envi2', 0, None, None, 0)\n", "    INTER_Envi3 = Beta('INTER_Envi3', 0, None, None, 0)\n", "    INTER_Envi4 = Beta('INTER_Envi4', 0, None, None, 0)\n", "    INTER_Envi5 = Beta('INTER_Envi5', 0, None, None, 0)\n", "\n", "    B_Envi1 = Beta('B_Envi1', 1, None, None, 1)\n", "    B_Envi2 = Beta('B_Envi2', 1, None, None, 0)\n", "    B_Envi3 = Beta('B_Envi3', 1, None, None, 0)\n", "    B_Envi4 = Beta('B_Envi4', 1, None, None, 0)\n", "    B_Envi5 = Beta('B_Envi5', 1, None, None, 0)\n", "\n", "    SIGMA_Envi1 = Beta('SIGMA_Envi1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Envi2 = Beta('SIGMA_Envi2', 1, 1.0e-5, None, 0)\n", "    SIGMA_Envi3 = Beta('SIGMA_Envi3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Envi4 = Beta('SIGMA_Envi4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Envi5 = Beta('SIGMA_Envi5', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Press1 = Beta('INTER_Press1', 0, None, None, 1)\n", "    INTER_Press2 = Beta('INTER_Press2', 0, None, None, 0)\n", "    INTER_Press3 = Beta('INTER_Press3', 0, None, None, 0)\n", "    INTER_Press4 = Beta('INTER_Press4', 0, None, None, 0)\n", "\n", "    B_Press1 = Beta('B_Press1', 1, None, None, 1)\n", "    B_Press2 = Beta('B_Press2', 1, None, None, 0)\n", "    B_Press3 = Beta('B_Press3', 1, None, None, 0)\n", "    B_Press4 = Beta('B_Press4', 1, None, None, 0)\n", "\n", "    SIGMA_Press1 = Beta('SIGMA_Press1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Press2 = Beta('SIGMA_Press2', 1, 1.0e-5, None, 0)\n", "    SIGMA_Press3 = Beta('SIGMA_Press3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Press4 = Beta('SIGMA_Press4', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Con1 = Beta('INTER_Con1', 0, None, None, 1)\n", "    INTER_Con2 = Beta('INTER_Con2', 0, None, None, 0)\n", "    INTER_Con3 = Beta('INTER_Con3', 0, None, None, 0)\n", "    INTER_Con4 = Beta('INTER_Con4', 0, None, None, 0)\n", "\n", "    B_Con1 = Beta('B_Con1', 1, None, None, 1)\n", "    B_Con2 = Beta('B_Con2', 1, None, None, 0)\n", "    B_Con3 = Beta('B_Con3', 1, None, None, 0)\n", "    B_Con4 = Beta('B_Con4', 1, None, None, 0)\n", "\n", "    SIGMA_Con1 = Beta('SIGMA_Con1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Con2 = Beta('SIGMA_Con2', 1, 1.0e-5, None, 0)\n", "    SIGMA_Con3 = Beta('SIGMA_Con3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Con4 = Beta('SIGMA_Con4', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Deli_1 = Beta('INTER_Deli_1', 0, None, None, 1)\n", "    INTER_Deli_4 = Beta('INTER_Deli_4', 0, None, None, 0)\n", "    INTER_Deli_5 = Beta('INTER_Deli_5', 0, None, None, 0)\n", "    INTER_Deli_6 = Beta('INTER_Deli_6', 0, None, None, 0)\n", "\n", "    B_Deli_1 = Beta('B_Deli_1', 1, None, None, 1)\n", "    B_Deli_4 = Beta('B_Deli_4', 1, None, None, 0)\n", "    B_Deli_5 = Beta('B_Deli_5', 1, None, None, 0)\n", "    B_Deli_6 = Beta('B_Deli_6', 1, None, None, 0)\n", "\n", "    SIGMA_Deli1 = Beta('SIGMA_Deli1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Deli4 = Beta('SIGMA_Deli4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Deli5 = Beta('SIGMA_Deli5', 1, 1.0e-5, None, 0)\n", "    SIGMA_Deli6 = Beta('SIGMA_Deli6', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Habit_1 = Beta('INTER_Habit_1', 0, None, None, 1)\n", "    INTER_Habit_2 = Beta('INTER_Habit_2', 0, None, None, 0)\n", "    INTER_Habit_3 = Beta('INTER_Habit_3', 0, None, None, 0)\n", "    INTER_Habit_4 = Beta('INTER_Habit_4', 0, None, None, 0)\n", "    INTER_Habit_5 = Beta('INTER_Habit_5', 0, None, None, 0)\n", "\n", "    B_Habit1 = Beta('B_Habit1', 1, None, None, 1)\n", "    B_Habit2 = Beta('B_Habit2', 1, None, None, 0)\n", "    B_Habit3 = Beta('B_Habit3', 1, None, None, 0)\n", "    B_Habit4 = Beta('B_Habit4', 1, None, None, 0)\n", "    B_Habit5 = Beta('B_Habit5', 1, None, None, 0)\n", "\n", "    SIGMA_Habit1 = Beta('SIGMA_Habit1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Habit2 = Beta('SIGMA_Habit2', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit3 = Beta('SIGMA_Habit3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit4 = Beta('SIGMA_Habit4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit5 = Beta('SIGMA_Habit5', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Sub_1 = Beta('INTER_Sub_1', 0, None, None, 1)\n", "    INTER_Sub_2 = Beta('INTER_Sub_2', 0, None, None, 0)\n", "    INTER_Sub_3 = Beta('INTER_Sub_3', 0, None, None, 0)\n", "    INTER_Sub_4 = Beta('INTER_Sub_4', 0, None, None, 0)\n", "\n", "    B_Sub1 = Beta('B_Sub1', 1, None, None, 1)\n", "    B_Sub2 = Beta('B_Sub2', 1, None, None, 0)\n", "    B_Sub3 = Beta('B_Sub3', 1, None, None, 0)\n", "    B_Sub4 = Beta('B_Sub4', 1, None, None, 0)\n", "\n", "    SIGMA_Sub1 = Beta('SIGMA_Sub1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Sub2 = Beta('SIGMA_Sub2', 1, 1.0e-5, None, 0)\n", "    SIGMA_Sub3 = Beta('SIGMA_Sub3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Sub4 = Beta('SIGMA_Sub4', 1, 1.0e-5, None, 0)\n", "\n", "    #* latent variables\n", "    BETA_Envi = Beta('BETA_Envi', 0, None, None, 0)\n", "    BETA_Press = Beta('BETA_Press', 0, None, None, 0)\n", "    BETA_Habit = Beta('BETA_Habit', 0, None, None, 0)\n", "    BETA_Att = Beta('BETA_Att', 0, None, None, 0)\n", "    BETA_Sub = Beta('BETA_Sub', 0, None, None, 0)\n", "    BETA_Con = Beta('BETA_Con', 0, None, None, 0)\n", "    BETA_Unsafe = Beta('BETA_Unsafe', 0, None, None, 0)\n", "    BETA_Deli = Beta('BETA_Deli', 0, None, None, 0)\n", "\n", "    #* choice model coefficient\n", "    BETA_Volume = Beta('BETA_Volume', 0, None, None, 0)\n", "    BETA_Time= Beta('BETA_Time', 0, None, None, 0)\n", "    BETA_Weather= Beta('BETA_Weather', 0, None, None, 0)\n", "    BETA_Location = Beta('BETA_Location', 0, None, None, 0)\n", "    BETA_RemainTime = Beta('BETA_RemainTime', 0, None, None, 0)\n", "    BETA_CauseUnsafe = Beta('BETA_CauseUnsafe', 0, None, None, 0)\n", "\n", "    BETA_deliVolume_low = Beta('BETA_deliVolume_low', 0, None, None, 0)\n", "    BETA_deliVolume_high = Beta('BETA_deliVolume_high', 0, None, None, 0)\n", "\n", "    BETA_deliTime_morning = Beta('BETA_deliTime_morning', 0, None, None, 0)\n", "    BETA_deliTime_noon = Beta('BETA_deliTime_noon', 0, None, None, 0)\n", "    BETA_deliTime_evening = Beta('BETA_deliTime_evening', 0, None, None, 0)\n", "    BETA_deliTime_afternoon = Beta('BETA_deliTime_afternoon', 0, None, None, 0)\n", "    BETA_deliTime_night = Beta('BETA_deliTime_night', 0, None, None, 0)\n", "\n", "    BETA_deliWeather_heavy = Beta('BETA_deliWeather_heavy', 0, None, None, 0)\n", "    BETA_deliWeather_good = Beta('BETA_deliWeather_good', 0, None, None, 0)\n", "    BETA_deliWeather_spit = Beta('BETA_deliWeather_spit', 0, None, None, 0)\n", "\n", "    BETA_deliLocation_inter = Beta('BETA_deliLocation_inter', 0, None, None, 0)\n", "    BETA_deliLocation_straight = Beta('BETA_deliLocation_straight', 0, None, None, 0)\n", "    BETA_deliLocation_curve = Beta('BETA_deliLocation_curve', 0, None, None, 0)\n", "    BETA_deliLocation_pede = Beta('BETA_deliLocation_pede', 0, None, None, 0)\n", "\n", "    BETA_RemainTime_short = Beta('BETA_RemainTime_short', 0, None, None, 0)\n", "\n", "    BETA_CauseUnsafe_overspeed = Beta('BETA_CauseUnsafe_overspeed', 0, None, None, 0)\n", "    BETA_CauseUnsafe_breakrule = Beta('BETA_CauseUnsafe_breakrule', 0, None, None, 0)\n", "    BETA_CauseUnsafe_drowsy = Beta('BETA_CauseUnsafe_drowsy', 0, None, None, 0)\n", "    BETA_CauseUnsafe_emergency = Beta('BETA_CauseUnsafe_emergency', 0, None, None, 0)\n", "    BETA_CauseUnsafe_phone = Beta('BETA_CauseUnsafe_phone', 0, None, None, 0)\n", "    BETA_CauseUnsafe_without = Beta('BETA_CauseUnsafe_without', 0, None, None, 0)\n", "\n", "    BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)\n", "    BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)\n", "    # BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)\n", "    BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)\n", "    BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)\n", "\n", "    BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)\n", "    BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)\n", "    # BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)\n", "    BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)\n", "    BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)\n", "\n", "    BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)\n", "    BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)\n", "    BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)\n", "    BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)\n", "    BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)\n", "\n", "    BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)\n", "    BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)\n", "    BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)\n", "    BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)\n", "    BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)\n", "    BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)\n", "    BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)\n", "    BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)\n", "    BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)\n", "    BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)\n", "    BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)\n", "    BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)\n", "    BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)\n", "    BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)\n", "    BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)\n", "    BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)\n", "    BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)\n", "\n", "    BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)\n", "\n", "    BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)\n", "    BETA_age_between_24_35 = Beta('BETA_age_between_24_35', 0.0, None, None, 0)\n", "    BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)\n", "\n", "    BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)\n", "    BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)\n", "\n", "    BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)\n", "\n", "    BETA_children = Beta('BETA_children', 0.0, None, None, 0)\n", "\n", "    BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)\n", "    BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)\n", "    BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)\n", "\n", "    BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)\n", "    BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)\n", "    BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)\n", "\n", "    BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)\n", "    BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)\n", "    BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)\n", "    BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)\n", "\n", "    BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)\n", "    BETA_disposableincome_more_10000 = Beta('BETA_disposableincome_more_10000', 0.0, None, None, 0)\n", "\n", "    BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)\n", "\n", "    #* structual equations\n", "    sigma_1 = Beta('sigma_1', 1, None, None, 0)\n", "    omega_1 = bioDraws('omega_1', 'NORMAL_MLHS')\n", "\n", "    # coef_citytime_Att * citytime_less_3 + \\\n", "    # coef_age_Att  * age_less_24 + \\\n", "    Att = coef_intercept_Att + \\\n", "        coef_age_Att  * age_less_24 + \\\n", "        coef_gender_Att  * gender_female + \\\n", "        coef_edu_Att * edu_less_junior + \\\n", "        coef_marriage_Att * marriage_not + \\\n", "        coef_children_Att * children + \\\n", "        coef_family_Att * family_big + \\\n", "        coef_monthincome_Att * monthincome_less_4000 + \\\n", "        coef_disposableincome_Att * disposableincome_more_10000 + \\\n", "        omega_1 * sigma_1\n", "\n", "    MODEL_Att2 = INTER_Att2 + B_Att2 * Att\n", "    MODEL_Att3 = INTER_Att3 + B_Att3 * Att\n", "    MODEL_Att4 = INTER_Att4 + B_Att4 * Att\n", "    MODEL_Att5 = INTER_Att5 + B_Att5 * Att\n", "    MODEL_Att6 = INTER_Att6 + B_Att6 * Att\n", "\n", "    sigma_2 = Beta('sigma_2', 1, None, None, 0)\n", "    omega_2 = bioDraws('omega_2', 'NORMAL_MLHS')\n", "\n", "        # coef_citytime_Unsafe * citytime_less_3 + \\\n", "    Unsafe = coef_intercept_Unsafe + \\\n", "        coef_gender_Unsafe  * gender_female + \\\n", "        coef_age_Unsafe  * age_less_24 + \\\n", "        coef_edu_Unsafe * edu_less_junior + \\\n", "        coef_marriage_Unsafe * marriage_not + \\\n", "        coef_children_Unsafe * children + \\\n", "        coef_family_Unsafe * family_big + \\\n", "        coef_monthincome_Unsafe * monthincome_less_4000 + \\\n", "        coef_disposableincome_Unsafe * disposableincome_more_10000 + \\\n", "        omega_2 * sigma_2\n", "\n", "    MODEL_Unsafe1 = INTER_Unsafe1 + B_Unsafe1 * Unsafe\n", "    MODEL_Unsafe2 = INTER_Unsafe2 + B_Unsafe2 * Unsafe\n", "    MODEL_Unsafe3 = INTER_Unsafe3 + B_Unsafe3 * Unsafe\n", "    MODEL_Unsafe4 = INTER_Unsafe4 + B_Unsafe4 * Unsafe\n", "    MODEL_Unsafe5 = INTER_Unsafe5 + B_Unsafe5 * Unsafe\n", "\n", "    sigma_3 = Beta('sigma_3', 1, None, None, 0)\n", "    omega_3 = bioDraws('omega_3', 'NORMAL_MLHS')\n", "\n", "        # coef_citytime_Envi * citytime_less_3 + \\\n", "    Envi = coef_intercept_Envi + \\\n", "        coef_gender_Envi  * gender_female + \\\n", "        coef_age_Envi  * age_less_24 + \\\n", "        coef_edu_Envi * edu_less_junior + \\\n", "        coef_marriage_Envi * marriage_not + \\\n", "        coef_children_Envi * children + \\\n", "        coef_family_Envi * family_big + \\\n", "        coef_monthincome_Envi * monthincome_less_4000 + \\\n", "        coef_disposableincome_Envi * disposableincome_more_10000 + \\\n", "        omega_3 * sigma_3\n", "\n", "    MODEL_Envi1 = INTER_Envi1 + B_Envi1 * Envi\n", "    MODEL_Envi2 = INTER_Envi2 + B_Envi2 * Envi\n", "    MODEL_Envi3 = INTER_Envi3 + B_Envi3 * Envi\n", "    MODEL_Envi4 = INTER_Envi4 + B_Envi4 * Envi\n", "    MODEL_Envi5 = INTER_Envi5 + B_Envi5 * Envi\n", "\n", "    sigma_4 = Beta('sigma_4', 1, None, None, 0)\n", "    omega_4 = bioDraws('omega_4', 'NORMAL_MLHS')\n", "    \n", "        # coef_citytime_Press * citytime_less_3 + \\\n", "    Press = coef_intercept_Press + \\\n", "        coef_gender_Press  * gender_female + \\\n", "        coef_age_Press  * age_less_24 + \\\n", "        coef_edu_Press * edu_less_junior + \\\n", "        coef_marriage_Press * marriage_not + \\\n", "        coef_children_Press * children + \\\n", "        coef_family_Press * family_big + \\\n", "        coef_monthincome_Press * monthincome_less_4000 + \\\n", "        coef_disposableincome_Press * disposableincome_more_10000 + \\\n", "        omega_4 * sigma_4\n", "\n", "    MODEL_Press1 = INTER_Press1 + B_Press1 * Press\n", "    MODEL_Press2 = INTER_Press2 + B_Press2 * Press\n", "    MODEL_Press3 = INTER_Press3 + B_Press3 * Press\n", "    MODEL_Press4 = INTER_Press4 + B_Press4 * Press\n", "    \n", "    sigma_5 = Beta('sigma_5', 1, None, None, 0)\n", "    omega_5 = bioDraws('omega_5', 'NORMAL_MLHS')\n", "\n", "        # coef_citytime_Con * citytime_less_3 + \\\n", "    Con = coef_intercept_Con + \\\n", "        coef_gender_Con  * gender_female + \\\n", "        coef_age_Con  * age_less_24 + \\\n", "        coef_edu_Con * edu_less_junior + \\\n", "        coef_marriage_Con * marriage_not + \\\n", "        coef_children_Con * children + \\\n", "        coef_family_Con * family_big + \\\n", "        coef_monthincome_Con * monthincome_less_4000 + \\\n", "        coef_disposableincome_Con * disposableincome_more_10000 + \\\n", "        omega_5 * sigma_5\n", "\n", "    MODEL_Con1 = INTER_Con1 + B_Con1 * Con\n", "    MODEL_Con2 = INTER_Con2 + B_Con2 * Con\n", "    MODEL_Con3 = INTER_Con3 + B_Con3 * Con\n", "    MODEL_Con4 = INTER_Con4 + B_Con4 * Con\n", "\n", "    sigma_6 = Beta('sigma_6', 1, None, None, 0)\n", "    omega_6 = bioDraws('omega_6', 'NORMAL_MLHS')\n", "\n", "        # coef_citytime_Deli * citytime_less_3 + \\\n", "    Deli = coef_intercept_Deli + \\\n", "        coef_gender_Deli  * gender_female + \\\n", "        coef_age_Deli  * age_less_24 + \\\n", "        coef_edu_Deli * edu_less_junior + \\\n", "        coef_marriage_Deli * marriage_not + \\\n", "        coef_children_Deli * children + \\\n", "        coef_family_Deli * family_big + \\\n", "        coef_monthincome_Deli * monthincome_less_4000 + \\\n", "        coef_disposableincome_Deli * disposableincome_more_10000 + \\\n", "        omega_6 * sigma_6\n", "\n", "    MODEL_Deli1 = INTER_Deli_1 + B_Deli_1 * Deli\n", "    MODEL_Deli4 = INTER_Deli_4 + B_Deli_4 * Deli\n", "    MODEL_Deli5 = INTER_Deli_5 + B_Deli_5 * Deli\n", "    MODEL_Deli6 = INTER_Deli_6 + B_Deli_6 * Deli\n", "\n", "    sigma_7 = Beta('sigma_7', 1, None, None, 0)\n", "    omega_7 = bioDraws('omega_7', 'NORMAL_MLHS')\n", "\n", "        # coef_citytime_Habit * citytime_less_3 + \\\n", "    Habit = coef_intercept_Habit + \\\n", "        coef_gender_Habit  * gender_female + \\\n", "        coef_age_Habit  * age_less_24 + \\\n", "        coef_edu_Habit * edu_less_junior + \\\n", "        coef_marriage_Habit * marriage_not + \\\n", "        coef_children_Habit * children + \\\n", "        coef_family_Habit * family_big + \\\n", "        coef_monthincome_Habit * monthincome_less_4000 + \\\n", "        coef_disposableincome_Habit * disposableincome_more_10000 + \\\n", "        omega_7 * sigma_7\n", "\n", "    MODEL_Habit1 = INTER_Habit_1 + B_Habit1 * Habit\n", "    MODEL_Habit2 = INTER_Habit_2 + B_Habit2 * Habit\n", "    MODEL_Habit3 = INTER_Habit_3 + B_Habit3 * Habit\n", "    MODEL_Habit4 = INTER_Habit_4 + B_Habit4 * Habit\n", "    MODEL_Habit5 = INTER_Habit_5 + B_Habit5 * Habit\n", "\n", "    sigma_8 = Beta('sigma_8', 1, None, None, 0)\n", "    omega_8 = bioDraws('omega_8', 'NORMAL_MLHS')\n", "    \n", "        # coef_citytime_Sub * citytime_less_3 + \\\n", "    Sub = coef_intercept_Sub + \\\n", "        coef_gender_Sub  * gender_female + \\\n", "        coef_age_Sub  * age_less_24 + \\\n", "        coef_edu_Sub * edu_less_junior + \\\n", "        coef_marriage_Sub * marriage_not + \\\n", "        coef_children_Sub * children + \\\n", "        coef_family_Sub * family_big + \\\n", "        coef_monthincome_Sub * monthincome_less_4000 + \\\n", "        coef_disposableincome_Sub * disposableincome_more_10000 + \\\n", "        omega_8 * sigma_8\n", "\n", "    MODEL_Sub1 = INTER_Sub_1 + B_Sub1 * Sub\n", "    MODEL_Sub2 = INTER_Sub_2 + B_Sub2 * Sub\n", "    MODEL_Sub3 = INTER_Sub_3 + B_Sub3 * Sub\n", "    MODEL_Sub4 = INTER_Sub_4 + B_Sub4 * Sub\n", "\n", "    # As the measurements are using a Likert scale with M = 5 levels, we deﬁne 4 parameters\n", "    delta_1 = Beta('delta_1', 0.1, 1.0e-5, None, 0)\n", "    delta_2 = Beta('delta_2', 0.2, 1.0e-5, None, 0)\n", "    tau_1 = -delta_1 - delta_2\n", "    tau_2 = -delta_1\n", "    tau_3 = delta_1\n", "    tau_4 = delta_1 + delta_2\n", "\n", "    #* measurement equations\n", "    Att2_tau_1 = (tau_1 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_2 = (tau_2 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_3 = (tau_3 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_4 = (tau_4 - MODEL_Att2) / SIGMA_Att2\n", "    IndiAtt2 = {\n", "        1: bioNormalCdf(Att2_tau_1),\n", "        2: bioNormalCdf(Att2_tau_2) - bioNormalCdf(Att2_tau_1),\n", "        3: bioNormalCdf(Att2_tau_3) - bioNormalCdf(Att2_tau_2),\n", "        4: bioNormalCdf(Att2_tau_4) - bioNormalCdf(Att2_tau_3),\n", "        5: 1 - bioNormalCdf(Att2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att2 = Elem(IndiAtt2, Attitude2)\n", "\n", "    Att3_tau_1 = (tau_1 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_2 = (tau_2 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_3 = (tau_3 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_4 = (tau_4 - MODEL_Att3) / SIGMA_Att3\n", "    IndiAtt3 = {\n", "        1: bioNormalCdf(Att3_tau_1),\n", "        2: bioNormalCdf(Att3_tau_2) - bioNormalCdf(Att3_tau_1),\n", "        3: bioNormalCdf(Att3_tau_3) - bioNormalCdf(Att3_tau_2),\n", "        4: bioNormalCdf(Att3_tau_4) - bioNormalCdf(Att3_tau_3),\n", "        5: 1 - bioNormalCdf(Att3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att3 = Elem(IndiAtt3, Attitude3)\n", "\n", "    Att4_tau_1 = (tau_1 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_2 = (tau_2 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_3 = (tau_3 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_4 = (tau_4 - MODEL_Att4) / SIGMA_Att4\n", "    IndiAtt4 = {\n", "        1: bioNormalCdf(Att4_tau_1),\n", "        2: bioNormalCdf(Att4_tau_2) - bioNormalCdf(Att4_tau_1),\n", "        3: bioNormalCdf(Att4_tau_3) - bioNormalCdf(Att4_tau_2),\n", "        4: bioNormalCdf(Att4_tau_4) - bioNormalCdf(Att4_tau_3),\n", "        5: 1 - bioNormalCdf(Att4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att4 = Elem(IndiAtt4, Attitude4)\n", "\n", "    Att5_tau_1 = (tau_1 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_2 = (tau_2 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_3 = (tau_3 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_4 = (tau_4 - MODEL_Att5) / SIGMA_Att5\n", "    IndiAtt5 = {\n", "        1: bioNormalCdf(Att5_tau_1),\n", "        2: bioNormalCdf(Att5_tau_2) - bioNormalCdf(Att5_tau_1),\n", "        3: bioNormalCdf(Att5_tau_3) - bioNormalCdf(Att5_tau_2),\n", "        4: bioNormalCdf(Att5_tau_4) - bioNormalCdf(Att5_tau_3),\n", "        5: 1 - bioNormalCdf(Att5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att5 = Elem(IndiAtt5, Attitude5)\n", "\n", "    Att6_tau_1 = (tau_1 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_2 = (tau_2 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_3 = (tau_3 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_4 = (tau_4 - MODEL_Att6) / SIGMA_Att6\n", "    IndiAtt6 = {\n", "        1: bioNormalCdf(Att6_tau_1),\n", "        2: bioNormalCdf(Att6_tau_2) - bioNormalCdf(Att6_tau_1),\n", "        3: bioNormalCdf(Att6_tau_3) - bioNormalCdf(Att6_tau_2),\n", "        4: bioNormalCdf(Att6_tau_4) - bioNormalCdf(Att6_tau_3),\n", "        5: 1 - bioNormalCdf(Att6_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att6 = Elem(IndiAtt6, Attitude6)\n", "\n", "    Unsafe1_tau_1 = (tau_1 - MODEL_Unsafe1) / SIGMA_Unsafe1\n", "    Unsafe1_tau_2 = (tau_2 - MODEL_Unsafe1) / SIGMA_Unsafe1\n", "    Unsafe1_tau_3 = (tau_3 - MODEL_Unsafe1) / SIGMA_Unsafe1\n", "    Unsafe1_tau_4 = (tau_4 - MODEL_Unsafe1) / SIGMA_Unsafe1\n", "    IndiUnsafe1 = {\n", "        1: bioNormalCdf(Unsafe1_tau_1),\n", "        2: bioNormalCdf(Unsafe1_tau_2) - bioNormalCdf(Unsafe1_tau_1),\n", "        3: bioNormalCdf(Unsafe1_tau_3) - bioNormalCdf(Unsafe1_tau_2),\n", "        4: bioNormalCdf(Unsafe1_tau_4) - bioNormalCdf(Unsafe1_tau_3),\n", "        5: 1 - bioNormalCdf(Unsafe1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Unsafe1 = Elem(IndiUnsafe1, Unsafe1)\n", "\n", "    Unsafe2_tau_1 = (tau_1 - MODEL_Unsafe2) / SIGMA_Unsafe2\n", "    Unsafe2_tau_2 = (tau_2 - MODEL_Unsafe2) / SIGMA_Unsafe2\n", "    Unsafe2_tau_3 = (tau_3 - MODEL_Unsafe2) / SIGMA_Unsafe2\n", "    Unsafe2_tau_4 = (tau_4 - MODEL_Unsafe2) / SIGMA_Unsafe2\n", "    IndiUnsafe2 = {\n", "        1: bioNormalCdf(Unsafe2_tau_1),\n", "        2: bioNormalCdf(Unsafe2_tau_2) - bioNormalCdf(Unsafe2_tau_1),\n", "        3: bioNormalCdf(Unsafe2_tau_3) - bioNormalCdf(Unsafe2_tau_2),\n", "        4: bioNormalCdf(Unsafe2_tau_4) - bioNormalCdf(Unsafe2_tau_3),\n", "        5: 1 - bioNormalCdf(Unsafe2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Unsafe2 = Elem(IndiUnsafe2, Unsafe2)\n", "\n", "    Unsafe3_tau_1 = (tau_1 - MODEL_Unsafe3) / SIGMA_Unsafe3\n", "    Unsafe3_tau_2 = (tau_2 - MODEL_Unsafe3) / SIGMA_Unsafe3\n", "    Unsafe3_tau_3 = (tau_3 - MODEL_Unsafe3) / SIGMA_Unsafe3\n", "    Unsafe3_tau_4 = (tau_4 - MODEL_Unsafe3) / SIGMA_Unsafe3\n", "    IndiUnsafe3 = {\n", "        1: bioNormalCdf(Unsafe3_tau_1),\n", "        2: bioNormalCdf(Unsafe3_tau_2) - bioNormalCdf(Unsafe3_tau_1),\n", "        3: bioNormalCdf(Unsafe3_tau_3) - bioNormalCdf(Unsafe3_tau_2),\n", "        4: bioNormalCdf(Unsafe3_tau_4) - bioNormalCdf(Unsafe3_tau_3),\n", "        5: 1 - bioNormalCdf(Unsafe3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Unsafe3 = Elem(IndiUnsafe3, Unsafe3)\n", "\n", "    Unsafe4_tau_1 = (tau_1 - MODEL_Unsafe4) / SIGMA_Unsafe4\n", "    Unsafe4_tau_2 = (tau_2 - MODEL_Unsafe4) / SIGMA_Unsafe4\n", "    Unsafe4_tau_3 = (tau_3 - MODEL_Unsafe4) / SIGMA_Unsafe4\n", "    Unsafe4_tau_4 = (tau_4 - MODEL_Unsafe4) / SIGMA_Unsafe4\n", "    IndiUnsafe4 = {\n", "        1: bioNormalCdf(Unsafe4_tau_1),\n", "        2: bioNormalCdf(Unsafe4_tau_2) - bioNormalCdf(Unsafe4_tau_1),\n", "        3: bioNormalCdf(Unsafe4_tau_3) - bioNormalCdf(Unsafe4_tau_2),\n", "        4: bioNormalCdf(Unsafe4_tau_4) - bioNormalCdf(Unsafe4_tau_3),\n", "        5: 1 - bioNormalCdf(Unsafe4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Unsafe4 = Elem(IndiUnsafe4, Unsafe4)\n", "\n", "    Unsafe5_tau_1 = (tau_1 - MODEL_Unsafe5) / SIGMA_Unsafe5\n", "    Unsafe5_tau_2 = (tau_2 - MODEL_Unsafe5) / SIGMA_Unsafe5\n", "    Unsafe5_tau_3 = (tau_3 - MODEL_Unsafe5) / SIGMA_Unsafe5\n", "    Unsafe5_tau_4 = (tau_4 - MODEL_Unsafe5) / SIGMA_Unsafe5\n", "    IndiUnsafe5 = {\n", "        1: bioNormalCdf(Unsafe5_tau_1),\n", "        2: bioNormalCdf(Unsafe5_tau_2) - bioNormalCdf(Unsafe5_tau_1),\n", "        3: bioNormalCdf(Unsafe5_tau_3) - bioNormalCdf(Unsafe5_tau_2),\n", "        4: bioNormalCdf(Unsafe5_tau_4) - bioNormalCdf(Unsafe5_tau_3),\n", "        5: 1 - bioNormalCdf(Unsafe5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Unsafe5 = Elem(IndiUnsafe5, Unsafe5)\n", "\n", "    Envi1_tau_1 = (tau_1 - MODEL_Envi1) / SIGMA_Envi1\n", "    Envi1_tau_2 = (tau_2 - MODEL_Envi1) / SIGMA_Envi1\n", "    Envi1_tau_3 = (tau_3 - MODEL_Envi1) / SIGMA_Envi1\n", "    Envi1_tau_4 = (tau_4 - MODEL_Envi1) / SIGMA_Envi1\n", "    IndiEnvi1 = {\n", "        1: bioNormalCdf(Envi1_tau_1),\n", "        2: bioNormalCdf(Envi1_tau_2) - bioNormalCdf(Envi1_tau_1),\n", "        3: bioNormalCdf(Envi1_tau_3) - bioNormalCdf(Envi1_tau_2),\n", "        4: bioNormalCdf(Envi1_tau_4) - bioNormalCdf(Envi1_tau_3),\n", "        5: 1 - bioNormalCdf(Envi1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Envi1 = Elem(IndiEnvi1, Envi1)\n", "\n", "    Envi2_tau_1 = (tau_1 - MODEL_Envi2) / SIGMA_Envi2\n", "    Envi2_tau_2 = (tau_2 - MODEL_Envi2) / SIGMA_Envi2\n", "    Envi2_tau_3 = (tau_3 - MODEL_Envi2) / SIGMA_Envi2\n", "    Envi2_tau_4 = (tau_4 - MODEL_Envi2) / SIGMA_Envi2\n", "    IndiEnvi2 = {\n", "        1: bioNormalCdf(Envi2_tau_1),\n", "        2: bioNormalCdf(Envi2_tau_2) - bioNormalCdf(Envi2_tau_1),\n", "        3: bioNormalCdf(Envi2_tau_3) - bioNormalCdf(Envi2_tau_2),\n", "        4: bioNormalCdf(Envi2_tau_4) - bioNormalCdf(Envi2_tau_3),\n", "        5: 1 - bioNormalCdf(Envi2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Envi2 = Elem(IndiEnvi2, Envi2)\n", "\n", "    Envi3_tau_1 = (tau_1 - MODEL_Envi3) / SIGMA_Envi3\n", "    Envi3_tau_2 = (tau_2 - MODEL_Envi3) / SIGMA_Envi3\n", "    Envi3_tau_3 = (tau_3 - MODEL_Envi3) / SIGMA_Envi3\n", "    Envi3_tau_4 = (tau_4 - MODEL_Envi3) / SIGMA_Envi3\n", "    IndiEnvi3 = {\n", "        1: bioNormalCdf(Envi3_tau_1),\n", "        2: bioNormalCdf(Envi3_tau_2) - bioNormalCdf(Envi3_tau_1),\n", "        3: bioNormalCdf(Envi3_tau_3) - bioNormalCdf(Envi3_tau_2),\n", "        4: bioNormalCdf(Envi3_tau_4) - bioNormalCdf(Envi3_tau_3),\n", "        5: 1 - bioNormalCdf(Envi3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Envi3 = Elem(IndiEnvi3, Envi3)\n", "\n", "    Envi4_tau_1 = (tau_1 - MODEL_Envi4) / SIGMA_Envi4\n", "    Envi4_tau_2 = (tau_2 - MODEL_Envi4) / SIGMA_Envi4\n", "    Envi4_tau_3 = (tau_3 - MODEL_Envi4) / SIGMA_Envi4\n", "    Envi4_tau_4 = (tau_4 - MODEL_Envi4) / SIGMA_Envi4\n", "    IndiEnvi4 = {\n", "        1: bioNormalCdf(Envi4_tau_1),\n", "        2: bioNormalCdf(Envi4_tau_2) - bioNormalCdf(Envi4_tau_1),\n", "        3: bioNormalCdf(Envi4_tau_3) - bioNormalCdf(Envi4_tau_2),\n", "        4: bioNormalCdf(Envi4_tau_4) - bioNormalCdf(Envi4_tau_3),\n", "        5: 1 - bioNormalCdf(Envi4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Envi4 = Elem(IndiEnvi4, Envi4)\n", "\n", "    Envi5_tau_1 = (tau_1 - MODEL_Envi5) / SIGMA_Envi5\n", "    Envi5_tau_2 = (tau_2 - MODEL_Envi5) / SIGMA_Envi5\n", "    Envi5_tau_3 = (tau_3 - MODEL_Envi5) / SIGMA_Envi5\n", "    Envi5_tau_4 = (tau_4 - MODEL_Envi5) / SIGMA_Envi5\n", "    IndiEnvi5 = {\n", "        1: bioNormalCdf(Envi5_tau_1),\n", "        2: bioNormalCdf(Envi5_tau_2) - bioNormalCdf(Envi5_tau_1),\n", "        3: bioNormalCdf(Envi5_tau_3) - bioNormalCdf(Envi5_tau_2),\n", "        4: bioNormalCdf(Envi5_tau_4) - bioNormalCdf(Envi5_tau_3),\n", "        5: 1 - bioNormalCdf(Envi5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Envi5 = Elem(IndiEnvi5, Envi5)\n", "\n", "    Press1_tau_1 = (tau_1 - MODEL_Press1) / SIGMA_Press1\n", "    Press1_tau_2 = (tau_2 - MODEL_Press1) / SIGMA_Press1\n", "    Press1_tau_3 = (tau_3 - MODEL_Press1) / SIGMA_Press1\n", "    Press1_tau_4 = (tau_4 - MODEL_Press1) / SIGMA_Press1\n", "    IndiPress1 = {\n", "        1: bioNormalCdf(Press1_tau_1),\n", "        2: bioNormalCdf(Press1_tau_2) - bioNormalCdf(Press1_tau_1),\n", "        3: bioNormalCdf(Press1_tau_3) - bioNormalCdf(Press1_tau_2),\n", "        4: bioNormalCdf(Press1_tau_4) - bioNormalCdf(Press1_tau_3),\n", "        5: 1 - bioNormalCdf(Press1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Press1 = Elem(IndiPress1, Pressure1)\n", "\n", "    Press2_tau_1 = (tau_1 - MODEL_Press2) / SIGMA_Press2\n", "    Press2_tau_2 = (tau_2 - MODEL_Press2) / SIGMA_Press2\n", "    Press2_tau_3 = (tau_3 - MODEL_Press2) / SIGMA_Press2\n", "    Press2_tau_4 = (tau_4 - MODEL_Press2) / SIGMA_Press2\n", "    IndiPress2 = {\n", "        1: bioNormalCdf(Press2_tau_1),\n", "        2: bioNormalCdf(Press2_tau_2) - bioNormalCdf(Press2_tau_1),\n", "        3: bioNormalCdf(Press2_tau_3) - bioNormalCdf(Press2_tau_2),\n", "        4: bioNormalCdf(Press2_tau_4) - bioNormalCdf(Press2_tau_3),\n", "        5: 1 - bioNormalCdf(Press2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Press2 = Elem(IndiPress2, Pressure2)\n", "\n", "    Press3_tau_1 = (tau_1 - MODEL_Press3) / SIGMA_Press3\n", "    Press3_tau_2 = (tau_2 - MODEL_Press3) / SIGMA_Press3\n", "    Press3_tau_3 = (tau_3 - MODEL_Press3) / SIGMA_Press3\n", "    Press3_tau_4 = (tau_4 - MODEL_Press3) / SIGMA_Press3\n", "    IndiPress3 = {\n", "        1: bioNormalCdf(Press3_tau_1),\n", "        2: bioNormalCdf(Press3_tau_2) - bioNormalCdf(Press3_tau_1),\n", "        3: bioNormalCdf(Press3_tau_3) - bioNormalCdf(Press3_tau_2),\n", "        4: bioNormalCdf(Press3_tau_4) - bioNormalCdf(Press3_tau_3),\n", "        5: 1 - bioNormalCdf(Press3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Press3 = Elem(IndiPress3, Pressure3)\n", "\n", "    Press4_tau_1 = (tau_1 - MODEL_Press4) / SIGMA_Press4\n", "    Press4_tau_2 = (tau_2 - MODEL_Press4) / SIGMA_Press4\n", "    Press4_tau_3 = (tau_3 - MODEL_Press4) / SIGMA_Press4\n", "    Press4_tau_4 = (tau_4 - MODEL_Press4) / SIGMA_Press4\n", "    IndiPress4 = {\n", "        1: bioNormalCdf(Press4_tau_1),\n", "        2: bioNormalCdf(Press4_tau_2) - bioNormalCdf(Press4_tau_1),\n", "        3: bioNormalCdf(Press4_tau_3) - bioNormalCdf(Press4_tau_2),\n", "        4: bioNormalCdf(Press4_tau_4) - bioNormalCdf(Press4_tau_3),\n", "        5: 1 - bioNormalCdf(Press4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Press4 = Elem(IndiPress4, Pressure4)\n", "\n", "    Con1_tau_1 = (tau_1 - MODEL_Con1) / SIGMA_Con1\n", "    Con1_tau_2 = (tau_2 - MODEL_Con1) / SIGMA_Con1\n", "    Con1_tau_3 = (tau_3 - MODEL_Con1) / SIGMA_Con1\n", "    Con1_tau_4 = (tau_4 - MODEL_Con1) / SIGMA_Con1\n", "    IndiCon1 = {\n", "        1: bioNormalCdf(Con1_tau_1),\n", "        2: bioNormalCdf(Con1_tau_2) - bioNormalCdf(Con1_tau_1),\n", "        3: bioNormalCdf(Con1_tau_3) - bioNormalCdf(Con1_tau_2),\n", "        4: bioNormalCdf(Con1_tau_4) - bioNormalCdf(Con1_tau_3),\n", "        5: 1 - bioNormalCdf(Con1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Con1 = Elem(IndiCon1, Control1)\n", "\n", "    Con2_tau_1 = (tau_1 - MODEL_Con2) / SIGMA_Con2\n", "    Con2_tau_2 = (tau_2 - MODEL_Con2) / SIGMA_Con2\n", "    Con2_tau_3 = (tau_3 - MODEL_Con2) / SIGMA_Con2\n", "    Con2_tau_4 = (tau_4 - MODEL_Con2) / SIGMA_Con2\n", "    IndiCon2 = {\n", "        1: bioNormalCdf(Con2_tau_1),\n", "        2: bioNormalCdf(Con2_tau_2) - bioNormalCdf(Con2_tau_1),\n", "        3: bioNormalCdf(Con2_tau_3) - bioNormalCdf(Con2_tau_2),\n", "        4: bioNormalCdf(Con2_tau_4) - bioNormalCdf(Con2_tau_3),\n", "        5: 1 - bioNormalCdf(Con2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Con2 = Elem(IndiCon2, Control2)\n", "\n", "    Con3_tau_1 = (tau_1 - MODEL_Con3) / SIGMA_Con3\n", "    Con3_tau_2 = (tau_2 - MODEL_Con3) / SIGMA_Con3\n", "    Con3_tau_3 = (tau_3 - MODEL_Con3) / SIGMA_Con3\n", "    Con3_tau_4 = (tau_4 - MODEL_Con3) / SIGMA_Con3\n", "    IndiCon3 = {\n", "        1: bioNormalCdf(Con3_tau_1),\n", "        2: bioNormalCdf(Con3_tau_2) - bioNormalCdf(Con3_tau_1),\n", "        3: bioNormalCdf(Con3_tau_3) - bioNormalCdf(Con3_tau_2),\n", "        4: bioNormalCdf(Con3_tau_4) - bioNormalCdf(Con3_tau_3),\n", "        5: 1 - bioNormalCdf(Con3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Con3 = Elem(IndiCon3, Control3)\n", "\n", "    Con4_tau_1 = (tau_1 - MODEL_Con4) / SIGMA_Con4\n", "    Con4_tau_2 = (tau_2 - MODEL_Con4) / SIGMA_Con4\n", "    Con4_tau_3 = (tau_3 - MODEL_Con4) / SIGMA_Con4\n", "    Con4_tau_4 = (tau_4 - MODEL_Con4) / SIGMA_Con4\n", "    IndiCon4 = {\n", "        1: bioNormalCdf(Con4_tau_1),\n", "        2: bioNormalCdf(Con4_tau_2) - bioNormalCdf(Con4_tau_1),\n", "        3: bioNormalCdf(Con4_tau_3) - bioNormalCdf(Con4_tau_2),\n", "        4: bioNormalCdf(Con4_tau_4) - bioNormalCdf(Con4_tau_3),\n", "        5: 1 - bioNormalCdf(Con4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Con4 = Elem(IndiCon4, Control4)\n", "\n", "    Deli1_tau_1 = (tau_1 - MODEL_Deli1) / SIGMA_Deli1\n", "    Deli1_tau_2 = (tau_2 - MODEL_Deli1) / SIGMA_Deli1\n", "    Deli1_tau_3 = (tau_3 - MODEL_Deli1) / SIGMA_Deli1\n", "    Deli1_tau_4 = (tau_4 - MODEL_Deli1) / SIGMA_Deli1\n", "    IndiDeli1 = {\n", "        1: bioNormalCdf(Deli1_tau_1),\n", "        2: bioNormalCdf(Deli1_tau_2) - bioNormalCdf(Deli1_tau_1),\n", "        3: bioNormalCdf(Deli1_tau_3) - bioNormalCdf(Deli1_tau_2),\n", "        4: bioNormalCdf(Deli1_tau_4) - bioNormalCdf(Deli1_tau_3),\n", "        5: 1 - bioNormalCdf(Deli1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Deli1 = Elem(IndiDeli1, Deli1)\n", "\n", "    Deli6_tau_1 = (tau_1 - MODEL_Deli6) / SIGMA_Deli6\n", "    Deli6_tau_2 = (tau_2 - MODEL_Deli6) / SIGMA_Deli6\n", "    Deli6_tau_3 = (tau_3 - MODEL_Deli6) / SIGMA_Deli6\n", "    Deli6_tau_4 = (tau_4 - MODEL_Deli6) / SIGMA_Deli6\n", "    IndiDeli6 = {\n", "        1: bioNormalCdf(Deli6_tau_1),\n", "        2: bioNormalCdf(Deli6_tau_2) - bioNormalCdf(Deli6_tau_1),\n", "        3: bioNormalCdf(Deli6_tau_3) - bioNormalCdf(Deli6_tau_2),\n", "        4: bioNormalCdf(Deli6_tau_4) - bioNormalCdf(Deli6_tau_3),\n", "        5: 1 - bioNormalCdf(Deli6_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Deli6 = Elem(IndiDeli6, Deli6)\n", "\n", "    Deli5_tau_1 = (tau_1 - MODEL_Deli5) / SIGMA_Deli5\n", "    Deli5_tau_2 = (tau_2 - MODEL_Deli5) / SIGMA_Deli5\n", "    Deli5_tau_3 = (tau_3 - MODEL_Deli5) / SIGMA_Deli5\n", "    Deli5_tau_4 = (tau_4 - MODEL_Deli5) / SIGMA_Deli5\n", "    IndiDeli5 = {\n", "        1: bioNormalCdf(Deli5_tau_1),\n", "        2: bioNormalCdf(Deli5_tau_2) - bioNormalCdf(Deli5_tau_1),\n", "        3: bioNormalCdf(Deli5_tau_3) - bioNormalCdf(Deli5_tau_2),\n", "        4: bioNormalCdf(Deli5_tau_4) - bioNormalCdf(Deli5_tau_3),\n", "        5: 1 - bioNormalCdf(Deli5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Deli5 = Elem(IndiDeli5, Deli5)\n", "\n", "    Deli4_tau_1 = (tau_1 - MODEL_Deli4) / SIGMA_Deli4\n", "    Deli4_tau_2 = (tau_2 - MODEL_Deli4) / SIGMA_Deli4\n", "    Deli4_tau_3 = (tau_3 - MODEL_Deli4) / SIGMA_Deli4\n", "    Deli4_tau_4 = (tau_4 - MODEL_Deli4) / SIGMA_Deli4\n", "    IndiDeli4 = {\n", "        1: bioNormalCdf(Deli4_tau_1),\n", "        2: bioNormalCdf(Deli4_tau_2) - bioNormalCdf(Deli4_tau_1),\n", "        3: bioNormalCdf(Deli4_tau_3) - bioNormalCdf(Deli4_tau_2),\n", "        4: bioNormalCdf(Deli4_tau_4) - bioNormalCdf(Deli4_tau_3),\n", "        5: 1 - bioNormalCdf(Deli4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Deli4 = Elem(IndiDeli4, Deli4)\n", "\n", "    Habit1_tau_1 = (tau_1 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_2 = (tau_2 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_3 = (tau_3 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_4 = (tau_4 - MODEL_Habit1) / SIGMA_Habit1\n", "    IndiHabit1 = {\n", "        1: bioNormalCdf(Habit1_tau_1),\n", "        2: bioNormalCdf(Habit1_tau_2) - bioNormalCdf(Habit1_tau_1),\n", "        3: bioNormalCdf(Habit1_tau_3) - bioNormalCdf(Habit1_tau_2),\n", "        4: bioNormalCdf(Habit1_tau_4) - bioNormalCdf(Habit1_tau_3),\n", "        5: 1 - bioNormalCdf(Habit1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit1 = Elem(IndiHabit1, Habit1)\n", "\n", "    Habit2_tau_1 = (tau_1 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_2 = (tau_2 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_3 = (tau_3 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_4 = (tau_4 - MODEL_Habit2) / SIGMA_Habit2\n", "    IndiHabit2 = {\n", "        1: bioNormalCdf(Habit2_tau_1),\n", "        2: bioNormalCdf(Habit2_tau_2) - bioNormalCdf(Habit2_tau_1),\n", "        3: bioNormalCdf(Habit2_tau_3) - bioNormalCdf(Habit2_tau_2),\n", "        4: bioNormalCdf(Habit2_tau_4) - bioNormalCdf(Habit2_tau_3),\n", "        5: 1 - bioNormalCdf(Habit2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit2 = Elem(IndiHabit2, Habit2)\n", "\n", "    Habit3_tau_1 = (tau_1 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_2 = (tau_2 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_3 = (tau_3 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_4 = (tau_4 - MODEL_Habit3) / SIGMA_Habit3\n", "    IndiHabit3 = {\n", "        1: bioNormalCdf(Habit3_tau_1),\n", "        2: bioNormalCdf(Habit3_tau_2) - bioNormalCdf(Habit3_tau_1),\n", "        3: bioNormalCdf(Habit3_tau_3) - bioNormalCdf(Habit3_tau_2),\n", "        4: bioNormalCdf(Habit3_tau_4) - bioNormalCdf(Habit3_tau_3),\n", "        5: 1 - bioNormalCdf(Habit3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit3 = Elem(IndiHabit3, Habit3)\n", "\n", "    Habit4_tau_1 = (tau_1 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_2 = (tau_2 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_3 = (tau_3 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_4 = (tau_4 - MODEL_Habit4) / SIGMA_Habit4\n", "    IndiHabit4 = {\n", "        1: bioNormalCdf(Habit4_tau_1),\n", "        2: bioNormalCdf(Habit4_tau_2) - bioNormalCdf(Habit4_tau_1),\n", "        3: bioNormalCdf(Habit4_tau_3) - bioNormalCdf(Habit4_tau_2),\n", "        4: bioNormalCdf(Habit4_tau_4) - bioNormalCdf(Habit4_tau_3),\n", "        5: 1 - bioNormalCdf(Habit4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit4 = Elem(IndiHabit4, Habit4)\n", "\n", "    Habit5_tau_1 = (tau_1 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_2 = (tau_2 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_3 = (tau_3 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_4 = (tau_4 - MODEL_Habit5) / SIGMA_Habit5\n", "    IndiHabit5 = {\n", "        1: bioNormalCdf(Habit5_tau_1),\n", "        2: bioNormalCdf(Habit5_tau_2) - bioNormalCdf(Habit5_tau_1),\n", "        3: bioNormalCdf(Habit5_tau_3) - bioNormalCdf(Habit5_tau_2),\n", "        4: bioNormalCdf(Habit5_tau_4) - bioNormalCdf(Habit5_tau_3),\n", "        5: 1 - bioNormalCdf(Habit5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit5 = Elem(IndiHabit5, Habit5)\n", "\n", "    Sub1_tau_1 = (tau_1 - MODEL_Sub1) / SIGMA_Sub1\n", "    Sub1_tau_2 = (tau_2 - MODEL_Sub1) / SIGMA_Sub1\n", "    Sub1_tau_3 = (tau_3 - MODEL_Sub1) / SIGMA_Sub1\n", "    Sub1_tau_4 = (tau_4 - MODEL_Sub1) / SIGMA_Sub1\n", "    IndiSub1 = {\n", "        1: bioNormalCdf(Sub1_tau_1),\n", "        2: bioNormalCdf(Sub1_tau_2) - bioNormalCdf(Sub1_tau_1),\n", "        3: bioNormalCdf(Sub1_tau_3) - bioNormalCdf(Sub1_tau_2),\n", "        4: bioNormalCdf(Sub1_tau_4) - bioNormalCdf(Sub1_tau_3),\n", "        5: 1 - bioNormalCdf(Sub1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Sub1 = Elem(IndiSub1, Subjective1)\n", "\n", "    Sub2_tau_1 = (tau_1 - MODEL_Sub2) / SIGMA_Sub2\n", "    Sub2_tau_2 = (tau_2 - MODEL_Sub2) / SIGMA_Sub2\n", "    Sub2_tau_3 = (tau_3 - MODEL_Sub2) / SIGMA_Sub2\n", "    Sub2_tau_4 = (tau_4 - MODEL_Sub2) / SIGMA_Sub2\n", "    IndiSub2 = {\n", "        1: bioNormalCdf(Sub2_tau_1),\n", "        2: bioNormalCdf(Sub2_tau_2) - bioNormalCdf(Sub2_tau_1),\n", "        3: bioNormalCdf(Sub2_tau_3) - bioNormalCdf(Sub2_tau_2),\n", "        4: bioNormalCdf(Sub2_tau_4) - bioNormalCdf(Sub2_tau_3),\n", "        5: 1 - bioNormalCdf(Sub2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Sub2 = Elem(IndiSub2, Subjective2)\n", "\n", "    Sub3_tau_1 = (tau_1 - MODEL_Sub3) / SIGMA_Sub3\n", "    Sub3_tau_2 = (tau_2 - MODEL_Sub3) / SIGMA_Sub3\n", "    Sub3_tau_3 = (tau_3 - MODEL_Sub3) / SIGMA_Sub3\n", "    Sub3_tau_4 = (tau_4 - MODEL_Sub3) / SIGMA_Sub3\n", "    IndiSub3 = {\n", "        1: bioNormalCdf(Sub3_tau_1),\n", "        2: bioNormalCdf(Sub3_tau_2) - bioNormalCdf(Sub3_tau_1),\n", "        3: bioNormalCdf(Sub3_tau_3) - bioNormalCdf(Sub3_tau_2),\n", "        4: bioNormalCdf(Sub3_tau_4) - bioNormalCdf(Sub3_tau_3),\n", "        5: 1 - bioNormalCdf(Sub3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Sub3 = Elem(IndiSub3, Subjective3)\n", "\n", "    Sub4_tau_1 = (tau_1 - MODEL_Sub4) / SIGMA_Sub4\n", "    Sub4_tau_2 = (tau_2 - MODEL_Sub4) / SIGMA_Sub4\n", "    Sub4_tau_3 = (tau_3 - MODEL_Sub4) / SIGMA_Sub4\n", "    Sub4_tau_4 = (tau_4 - MODEL_Sub4) / SIGMA_Sub4\n", "    IndiSub4 = {\n", "        1: bioNormalCdf(Sub4_tau_1),\n", "        2: bioNormalCdf(Sub4_tau_2) - bioNormalCdf(Sub4_tau_1),\n", "        3: bioNormalCdf(Sub4_tau_3) - bioNormalCdf(Sub4_tau_2),\n", "        4: bioNormalCdf(Sub4_tau_4) - bioNormalCdf(Sub4_tau_3),\n", "        5: 1 - bioNormalCdf(Sub4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Sub4 = Elem(IndiSub4, Subjective4)\n", "\n", "\n", "    #* choice model\n", "\n", "    if num_idx == 1:\n", "        latent_att = [\n", "        # [Unsafe, Att, Habit], \n", "        # [Unsafe, Att, Sub], \n", "        # [Unsafe, Att, Press], \n", "        # [Unsafe, <PERSON><PERSON>, <PERSON><PERSON>], \n", "        # [Unsafe, Att, Habit, Sub], \n", "        # [Unsafe, Att, Habit, Press], \n", "        # [Unsafe, <PERSON><PERSON>, <PERSON>bit, <PERSON><PERSON>],\n", "        # [At<PERSON>, <PERSON>], \n", "        # [Att, Sub], \n", "        # [Att, Press], \n", "        # [<PERSON><PERSON>, <PERSON><PERSON>],\n", "        [Att, Ha<PERSON>],\n", "        ]\n", "\n", "        latent_att_beta = [\n", "        # [BETA_Unsafe, BETA_Att, BETA_Habit],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Sub],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Press],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Envi],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Sub],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Press],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Envi],\n", "        # [BETA_Att, BETA_Con],\n", "        # [BETA_Att, BETA_Sub],\n", "        # [BETA_Att, BETA_Press],\n", "        # [BETA_<PERSON>t, BETA_Envi],\n", "        [BETA_Att, BETA_Habit],\n", "        ]\n", "\n", "        latent_att_name = [\n", "        # 'Unsafe_Att_Habit', \n", "        # 'Unsafe_Att_Sub',\n", "        # 'Unsafe_Att_Press',\n", "        # 'Unsafe_Att_Envi',\n", "        # 'Unsafe_Att_Habit_Sub',\n", "        # 'Unsafe_Att_Habit_Press', \n", "        # 'Unsafe_Att_Habit_Envi',\n", "        # 'Att_Con', \n", "        # 'Att_Sub',\n", "        # 'Att_Press',\n", "        # 'Att_Envi',\n", "        'Att_Habit',\n", "        ]\n", "\n", "        latent_P = [\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Sub1,  P_Sub2, P_Sub3, P_Sub4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Press1,  P_Press2, P_Press3, P_Press4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Envi1, P_Envi2,  P_Envi3, P_Envi4, P_Envi5],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Sub1,  P_Sub2, P_Sub3, P_Sub4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Press1,  P_Press2, P_Press3, P_Press4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Envi1, P_Envi2,  P_Envi3, P_Envi4, P_Envi5],\n", "        # [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "        #  P_Con1,  P_Con2, P_Con3, P_Con4],\n", "        # [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "        #  P_Sub1,  P_Sub2, P_Sub3, P_Sub4],\n", "        # [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "        #  P_Press1,  P_Press2, P_Press3, P_Press4],\n", "        # [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "        #  P_Envi1, P_Envi2,  P_Envi3,  P_Envi4,  P_Envi5],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],\n", "        ]\n", "            \n", "    if num_idx == 2:\n", "\n", "        latent_att = [\n", "        # [Unsafe, Att, Habit], \n", "        # [Unsafe, Att, Deli], \n", "        # [Unsafe, Att, Con], \n", "        # [Unsafe, Att, Sub], \n", "        # [Unsafe, Att, Press], \n", "        # [Unsafe, Att, Habit, Deli], \n", "        # [Unsafe, Att, Habit, Con],\n", "        # [Unsafe, Att, Habit, Sub],\n", "        # [Unsafe, Att, Habit, Press]\n", "        [Att, <PERSON>], \n", "        [Att, Sub], \n", "        [Att, Press], \n", "        [At<PERSON>, <PERSON><PERSON>],\n", "        [Att, Ha<PERSON>],\n", "        ]\n", "\n", "        latent_att_beta = [\n", "        # [BETA_Unsafe, BETA_Att, BETA_Habit],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Deli],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Con],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Sub],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Press],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Deli],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Con],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Sub],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Press],\n", "        [BETA_Att, BETA_Con],\n", "        [BETA_Att, BETA_Sub],\n", "        [BETA_Att, BETA_Press],\n", "        [BETA_Att, BETA_Envi],\n", "        [BETA_Att, BETA_Habit],\n", "        ]\n", "\n", "        latent_att_name = [\n", "        # 'Unsafe_Att_Habit', \n", "        # 'Unsafe_Att_Deli',\n", "        # 'Unsafe_Att_Con',\n", "        # 'Unsafe_Att_Sub',\n", "        # 'Unsafe_Att_Press',\n", "        # 'Unsafe_Att_Habit_Deli', \n", "        # 'Unsafe_Att_Habit_Con',\n", "        # 'Unsafe_Att_Habit_Sub',\n", "        # 'Unsafe_Att_Habit_Press',\n", "        'Att_Con', \n", "        'Att_Sub',\n", "        'Att_Press',\n", "        'Att_Envi',\n", "        'Att_Habit',\n", "        ]\n", "\n", "        latent_P = [\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Deli1, P_Deli4, P_Deli5, P_Deli6],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Con1, P_Con2, P_Con3, P_Con4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Sub1, P_Sub2, P_Sub3, P_Sub4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Press1, P_Press2, P_Press3, P_Press4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Deli1,  P_Deli4, P_Deli5, P_Deli6],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Con1,  P_Con2, P_Con3, P_Con4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Sub1, P_Sub2, P_Sub3, P_Sub4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Press1, P_Press2, P_Press3, P_Press4]\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Con1,  P_Con2, P_Con3, P_Con4],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Sub1,  P_Sub2, P_Sub3, P_Sub4],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Press1,  P_Press2, P_Press3, P_Press4],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Envi1, P_Envi2,  P_Envi3,  P_Envi4,  P_Envi5],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],\n", "        ]\n", "\n", "    if num_idx == 3:\n", "\n", "        latent_att = [\n", "        # [Unsafe, Att, Habit], \n", "        # [Unsafe, Att, Deli], \n", "        # [Unsafe, Att, Con],  \n", "        # [Unsafe, Att, Press],\n", "        # [Unsafe, Att, Sub],\n", "        # [Unsafe, Att, Habit, Deli], \n", "        # [Unsafe, Att, Habit, Con],\n", "        # [Unsafe, Att, Habit, Press],\n", "        # [Unsafe, Att, Habit, Sub],\n", "        [Att, <PERSON>], \n", "        [Att, Sub], \n", "        [Att, Press], \n", "        [At<PERSON>, <PERSON><PERSON>],\n", "        [Att, Ha<PERSON>],\n", "        ]\n", "\n", "        latent_att_beta = [\n", "        # [BETA_Unsafe, BETA_Att, BETA_Habit],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Deli],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Con],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Press],\n", "        # [BETA_Unsafe, BETA_Att, BETA_Sub],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Deli],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Con],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Press],\n", "        # [BET<PERSON>_Unsafe, BETA_Att, BETA_Habit, BETA_Sub],\n", "        [BETA_Att, BETA_Con],\n", "        [BETA_Att, BETA_Sub],\n", "        [BETA_Att, BETA_Press],\n", "        [BETA_Att, BETA_Envi],\n", "        [BETA_Att, BETA_Habit],\n", "            \n", "        ]\n", "\n", "        latent_att_name = [\n", "        # 'Unsafe_Att_Habit', \n", "        # 'Unsafe_Att_Deli',\n", "        # 'Unsafe_Att_Con',\n", "        # 'Unsafe_Att_Press',\n", "        # 'Unsafe_Att_Sub',\n", "        # 'Unsafe_Att_Habit_Deli', \n", "        # 'Unsafe_Att_Habit_Con',\n", "        # 'Unsafe_Att_Habit_Press',\n", "        # 'Unsafe_Att_Habit_Sub',\n", "        'Att_Con', \n", "        'Att_Sub',\n", "        'Att_Press',\n", "        'Att_Envi',\n", "        'Att_Habit',\n", "        ]\n", "\n", "        latent_P = [\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Deli1, P_Deli4, P_Deli5, P_Deli6],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Con1, P_Con2, P_Con3, P_Con4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Press1, P_Press2, P_Press3, P_Press4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Sub1, P_Sub2, P_Sub3, P_Sub4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Deli1,  P_Deli4, P_Deli5, P_Deli6],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Con1,  P_Con2, P_Con3, P_Con4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Press1, P_Press2, P_Press3, P_Press4],\n", "        # [P_Unsafe1, P_Unsafe2, P_Unsafe3, P_Unsafe4, P_Unsafe5, P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5, P_Sub1, P_Sub2, P_Sub3, P_Sub4],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Con1,  P_Con2, P_Con3, P_Con4],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Sub1,  P_Sub2, P_Sub3, P_Sub4],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Press1,  P_Press2, P_Press3, P_Press4],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Envi1, P_Envi2,  P_Envi3,  P_Envi4,  P_Envi5],\n", "        [P_Att2, P_Att3, P_Att4, P_Att5, P_Att6, \n", "         P_Habit1, P_Habit2, P_Habit3, P_Habit4, P_Habit5],\n", "        ]\n", "\n", "    for i in range(len(latent_att)):\n", "        func_sum = 0\n", "        for j in range(len(latent_att[i])):\n", "            element_name = latent_att[i][j]\n", "            beta_element_name = latent_att_beta[i][j]\n", "            func_sum += element_name * beta_element_name\n", "\n", "        \n", "\n", "        # V1 = BETA_intercept + \\\n", "        #     BETA_deliverytime_short * deliverytime_short + \\\n", "        #     BETA_deliverymode_full * deliverymode_full + \\\n", "        #     BETA_deliveryposition_short * deliveryposition_short + \\\n", "        #     BETA_workingdays_standard * workingdays_standard + \\\n", "        #     BETA_workinghours_brief * workinghours_brief + \\\n", "        #     BETA_deliveryorders_brief * deliveryorders_brief + \\\n", "        #     BETA_deliveryrange_prolonged * deliveryrange_prolonged + \\\n", "        #     BETA_deliveryspeed_extended * deliveryspeed_extended + \\\n", "        #     BETA_gender_female  * gender_female + \\\n", "        #     BETA_age_less_24 * age_less_24 + \\\n", "        #     BETA_edu_less_junior * edu_less_junior + \\\n", "        #     BETA_marriage * marriage_not + \\\n", "        #     BETA_children * children + \\\n", "        #     BETA_family_big * family_big + \\\n", "        #     BETA_monthincome_less_4000 * monthincome_less_4000 + \\\n", "        #     BETA_disposableincome_more_10000 * disposableincome_more_10000 + \\\n", "        #     func_sum\n", "            \n", "        V1 = BETA_intercept + \\\n", "            BETA_deliverytime_short * deliverytime_short + \\\n", "            BETA_deliverymode_full * deliverymode_full + \\\n", "            BETA_deliveryposition_extended * deliveryposition_extended + \\\n", "            BETA_workingdays_standard * workingdays_standard + \\\n", "            BETA_workinghours_brief * workinghours_brief + \\\n", "            BETA_deliveryorders_brief * deliveryorders_brief + \\\n", "            BETA_deliveryrange_prolonged * deliveryrange_prolonged + \\\n", "            BETA_deliveryspeed_extended * deliveryspeed_extended + \\\n", "            BETA_gender_female  * gender_female + \\\n", "            BETA_age_less_24 * age_less_24 + \\\n", "            BETA_edu_less_junior * edu_less_junior + \\\n", "            BETA_marriage * marriage_not + \\\n", "            BETA_children * children + \\\n", "            BETA_family_big * family_big + \\\n", "            BETA_monthincome_less_4000 * monthincome_less_4000 + \\\n", "            BETA_disposableincome_more_10000 * disposableincome_more_10000 + \\\n", "            func_sum\n", "\n", "        V2 = 0\n", "\n", "        # Associate utility functions with the numbering of alternatives\n", "        V = {1: V1,\n", "            2: V2,\n", "            }\n", "\n", "        condprob = models.logit(V, None, UnsafeAccident)\n", "\n", "        sublist_product = 1  # Initialize the product for the current sublist to 1\n", "        for element in latent_P[i]:\n", "            sublist_product *= element  # Multiply each element in the sublist\n", "        \n", "        condlike = sublist_product * condprob\n", "\n", "        loglike = log(<PERSON><PERSON><PERSON><PERSON>(condlike))\n", "\n", "        # Define level of verbosity\n", "        logger = msg.bioMessage()\n", "        # logger.setSilent()\n", "        # logger.setWarning()\n", "        logger.setGeneral()\n", "        # logger.setDetailed()\n", "\n", "        # Create the Biogeme object\n", "        # biogeme = bio.BIOGEME(database, loglike, numberOfDraws = 500)\n", "        biogeme = bio.BIOGEME(database, loglike, numberOfDraws = 500)\n", "\n", "        # 获取当前日期和时间\n", "        now = datetime.now()\n", "\n", "        # 格式化日期和时间\n", "        date_time = now.strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "\n", "        biogeme.modelName = 'accident_iclv_' + latent_att_name[i] + date_time\n", "        # biogeme.generate_pickle = False\n", "        # biogeme.generatePickle = False\n", "\n", "        # Estimate the parameters\n", "        results = biogeme.estimate()\n", "\n", "        # biogeme.generate_pickle = False\n", "        # biogeme.generatePickle = False\n", "\n", "        print(f'Estimated betas: {len(results.data.betaValues)}')\n", "        print(f'Final log likelihood: {results.data.logLike:.3f}')\n", "        print(f'Output file: {results.data.htmlFileName}')\n", "\n", "        # shutil.move(str(results.data.htmlFileName), str('new_questionaire/iclv_mnl_once3/'+results.data.htmlFileName))\n", "\n", "        # print(results.short_summary())\n", "\n", "        pandas_results = results.getEstimatedParameters()\n", "        # print(pandas_results)\n", "\n", "        beta_rows = pandas_results[pandas_results.index.str.startswith('BETA_')]\n", "\n", "        # 在筛选的结果中，进一步筛选出 p-value 列小于 0.1 的行\n", "        filtered_rows = beta_rows[beta_rows['p-value'] < 0.1]\n", "        print(filtered_rows)\n", "        # 计算满足条件的行的数量\n", "        count_of_filtered_rows = filtered_rows.shape[0]\n", "\n", "        # 打印结果\n", "        print(\"满足条件的行的数量为:\", count_of_filtered_rows)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# MARGIN EFFECTS"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import biogeme.results as res\n", "\n", "def margin_effect(df,i,martix):\n", "    database = db.Database('data'+str(i), df)\n", "    globals().update(database.variables)\n", "\n", "    # 场景属性自变量\n", "    deliVolume = Variable('Volume')\n", "    deliTime = Variable('Time')\n", "    deliWeather = Variable('Weather')\n", "    deliLocation = Variable('Location')\n", "    RemainTime = Variable('RemainTime')\n", "    CauseUnsafe = Variable('CauseUnsafe')\n", "\n", "    # 因变量\n", "    UnsafeAccident = Variable('UnsafeAccident')\n", "\n", "    # 社会经济属性自变量\n", "    gender = Variable('gender')\n", "    age = Variable('age')\n", "    education = Variable('education')\n", "    marriage = Variable('marriage')\n", "    family = Variable('family')\n", "    citytime = Variable('citytime')\n", "    monthincome = Variable('monthincome')\n", "    disposableincome = Variable('disposableincome')\n", "\n", "    # 配送属性自变量\n", "    deliverytime = Variable('deliverytime')\n", "    deliverymode = Variable('deliverymode')\n", "    deliveryposition = Variable('deliveryposition')\n", "    workingdays = Variable('workingdays')\n", "    workinghours = Variable('workinghours')\n", "    deliveryorders = Variable('deliveryorders')\n", "    deliveryrange = Variable('deliveryrange')\n", "    deliveryspeed = Variable('deliveryspeed')\n", "\n", "    # 社会经济属性二分类变量\n", "    gender_female = DefineVariable('gender_female', gender == 2, database) + martix[0][i]\n", "    age_less_24 = DefineVariable('age_less_24', age <= 2, database) + martix[1][i]\n", "    edu_less_junior = DefineVariable('edu_less_junior', education <= 2, database) + martix[2][i]\n", "    marriage_not = DefineVariable('marriage_not', marriage <= 1, database) + martix[3][i]\n", "    children = DefineVariable('children', ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0), database) + martix[4][i]\n", "    family_big = DefineVariable('family_big', (family >= 5) &  (family <= 7), database) + martix[5][i]\n", "    monthincome_less_4000 = DefineVariable('monthincome_less_4000', monthincome <= 2, database) + martix[6][i]\n", "    disposableincome_more_10000 = DefineVariable('disposableincome_more_10000', disposableincome >= 3, database) \n", "\n", "    # 配送属性二分类变量\n", "    deliverytime_short = DefineVariable('deliverytime_short', deliverytime <= 2, database) \n", "    deliverymode_full = DefineVariable('deliverymode_full', deliverymode >= 2, database) \n", "    deliveryposition_extended = DefineVariable('deliveryposition_extended', deliveryposition >= 3, database) \n", "    workinghours_brief = DefineVariable('workinghours_brief', workinghours <= 1, database) + martix[7][i]\n", "    workingdays_standard = DefineVariable('workingdays_standard', workingdays <= 3, database) + martix[8][i]\n", "    deliveryorders_brief = DefineVariable('deliveryorders_brief', deliveryorders <= 1, database) + martix[9][i]\n", "    deliveryrange_prolonged = DefineVariable('deliveryrange_prolonged', deliveryrange >= 5, database) + martix[10][i]\n", "    deliveryspeed_extended = DefineVariable('deliveryspeed_extended', deliveryspeed == 4, database) + martix[11][i]\n", "\n", "    #* structual coefficient\n", "\n", "    # Attitude\n", "    coef_intercept_Att = Beta('coef_intercept_Att', 0.0, None, None, 0)\n", "    coef_gender_Att = Beta('coef_gender_Att', 0.0, None, None, 0)\n", "    coef_age_Att = Beta('coef_age_Att', 0.0, None, None, 0)\n", "    coef_edu_Att = Beta('coef_edu_Att', 0.0, None, None, 0)\n", "    coef_marriage_Att = Beta('coef_marriage_Att', 0.0, None, None, 0)\n", "    coef_children_Att = Beta('coef_children_Att', 0.0, None, None, 0)\n", "    coef_family_Att = Beta('coef_family_Att', 0.0, None, None, 0)\n", "    coef_citytime_Att = Beta('coef_citytime_Att', 0.0, None, None, 0)\n", "    coef_monthincome_Att = Beta('coef_monthincome_Att', 0.0, None, None, 0)\n", "    coef_disposableincome_Att = Beta('coef_disposableincome_Att', 0.0, None, None, 0)\n", "\n", "    # Habit\n", "    coef_intercept_Habit = Beta('coef_intercept_Habit', 0.0, None, None, 0)\n", "    coef_gender_Habit = Beta('coef_gender_Habit', 0.0, None, None, 0)\n", "    coef_age_Habit = Beta('coef_age_Habit', 0.0, None, None, 0)\n", "    coef_edu_Habit = Beta('coef_edu_Habit', 0.0, None, None, 0)\n", "    coef_marriage_Habit = Beta('coef_marriage_Habit', 0.0, None, None, 0)\n", "    coef_children_Habit = Beta('coef_children_Habit', 0.0, None, None, 0)\n", "    coef_family_Habit = Beta('coef_family_Habit', 0.0, None, None, 0)\n", "    coef_citytime_Habit = Beta('coef_citytime_Habit', 0.0, None, None, 0)\n", "    coef_monthincome_Habit = Beta('coef_monthincome_Habit', 0.0, None, None, 0)\n", "    coef_disposableincome_Habit = Beta('coef_disposableincome_Habit', 0.0, None, None, 0)\n", "\n", "    #* measurement coefficient\n", "    INTER_Att2 = Beta('INTER_Att2', 0, None, None, 1)\n", "    INTER_Att3 = Beta('INTER_Att3', 0, None, None, 0)\n", "    INTER_Att4 = Beta('INTER_Att4', 0, None, None, 0)\n", "    INTER_Att5 = Beta('INTER_Att5', 0, None, None, 0)\n", "    INTER_Att6 = Beta('INTER_Att6', 0, None, None, 0)\n", "\n", "    B_Att2 = Beta('B_Att2', 1, None, None, 1)\n", "    B_Att3 = Beta('B_Att3', 1, None, None, 0)\n", "    B_Att4 = Beta('B_Att4', 1, None, None, 0)\n", "    B_Att5 = Beta('B_Att5', 1, None, None, 0)\n", "    B_Att6 = Beta('B_Att6', 1, None, None, 0)\n", "\n", "    SIGMA_Att2 = Beta('SIGMA_Att2', 1, 1.0e-5, None, 1)\n", "    SIGMA_Att3 = Beta('SIGMA_Att3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att4 = Beta('SIGMA_Att4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att5 = Beta('SIGMA_Att5', 1, 1.0e-5, None, 0)\n", "    SIGMA_Att6 = Beta('SIGMA_Att6', 1, 1.0e-5, None, 0)\n", "\n", "    INTER_Habit_1 = Beta('INTER_Habit_1', 0, None, None, 1)\n", "    INTER_Habit_2 = Beta('INTER_Habit_2', 0, None, None, 0)\n", "    INTER_Habit_3 = Beta('INTER_Habit_3', 0, None, None, 0)\n", "    INTER_Habit_4 = Beta('INTER_Habit_4', 0, None, None, 0)\n", "    INTER_Habit_5 = Beta('INTER_Habit_5', 0, None, None, 0)\n", "\n", "    B_Habit1 = Beta('B_Habit1', 1, None, None, 1)\n", "    B_Habit2 = Beta('B_Habit2', 1, None, None, 0)\n", "    B_Habit3 = Beta('B_Habit3', 1, None, None, 0)\n", "    B_Habit4 = Beta('B_Habit4', 1, None, None, 0)\n", "    B_Habit5 = Beta('B_Habit5', 1, None, None, 0)\n", "\n", "    SIGMA_Habit1 = Beta('SIGMA_Habit1', 1, 1.0e-5, None, 1)\n", "    SIGMA_Habit2 = Beta('SIGMA_Habit2', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit3 = Beta('SIGMA_Habit3', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit4 = Beta('SIGMA_Habit4', 1, 1.0e-5, None, 0)\n", "    SIGMA_Habit5 = Beta('SIGMA_Habit5', 1, 1.0e-5, None, 0)\n", "\n", "    #* latent variables\n", "    BETA_Habit = Beta('BETA_Habit', 0, None, None, 0)\n", "    BETA_Att = Beta('BETA_Att', 0, None, None, 0)\n", "\n", "    #* choice model coefficient\n", "    BETA_Volume = Beta('BETA_Volume', 0, None, None, 0)\n", "    BETA_Time= Beta('BETA_Time', 0, None, None, 0)\n", "    BETA_Weather= Beta('BETA_Weather', 0, None, None, 0)\n", "    BETA_Location = Beta('BETA_Location', 0, None, None, 0)\n", "    BETA_RemainTime = Beta('BETA_RemainTime', 0, None, None, 0)\n", "    BETA_CauseUnsafe = Beta('BETA_CauseUnsafe', 0, None, None, 0)\n", "\n", "    BETA_deliVolume_low = Beta('BETA_deliVolume_low', 0, None, None, 0)\n", "    BETA_deliVolume_high = Beta('BETA_deliVolume_high', 0, None, None, 0)\n", "\n", "    BETA_deliTime_morning = Beta('BETA_deliTime_morning', 0, None, None, 0)\n", "    BETA_deliTime_noon = Beta('BETA_deliTime_noon', 0, None, None, 0)\n", "    BETA_deliTime_evening = Beta('BETA_deliTime_evening', 0, None, None, 0)\n", "    BETA_deliTime_afternoon = Beta('BETA_deliTime_afternoon', 0, None, None, 0)\n", "    BETA_deliTime_night = Beta('BETA_deliTime_night', 0, None, None, 0)\n", "\n", "    BETA_deliWeather_heavy = Beta('BETA_deliWeather_heavy', 0, None, None, 0)\n", "    BETA_deliWeather_good = Beta('BETA_deliWeather_good', 0, None, None, 0)\n", "    BETA_deliWeather_spit = Beta('BETA_deliWeather_spit', 0, None, None, 0)\n", "\n", "    BETA_deliLocation_inter = Beta('BETA_deliLocation_inter', 0, None, None, 0)\n", "    BETA_deliLocation_straight = Beta('BETA_deliLocation_straight', 0, None, None, 0)\n", "    BETA_deliLocation_curve = Beta('BETA_deliLocation_curve', 0, None, None, 0)\n", "    BETA_deliLocation_pede = Beta('BETA_deliLocation_pede', 0, None, None, 0)\n", "\n", "    BETA_RemainTime_short = Beta('BETA_RemainTime_short', 0, None, None, 0)\n", "\n", "    BETA_CauseUnsafe_overspeed = Beta('BETA_CauseUnsafe_overspeed', 0, None, None, 0)\n", "    BETA_CauseUnsafe_breakrule = Beta('BETA_CauseUnsafe_breakrule', 0, None, None, 0)\n", "    BETA_CauseUnsafe_drowsy = Beta('BETA_CauseUnsafe_drowsy', 0, None, None, 0)\n", "    BETA_CauseUnsafe_emergency = Beta('BETA_CauseUnsafe_emergency', 0, None, None, 0)\n", "    BETA_CauseUnsafe_phone = Beta('BETA_CauseUnsafe_phone', 0, None, None, 0)\n", "    BETA_CauseUnsafe_without = Beta('BETA_CauseUnsafe_without', 0, None, None, 0)\n", "\n", "    BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)\n", "    BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)\n", "    # BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)\n", "    BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)\n", "    BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)\n", "\n", "    BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)\n", "    BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)\n", "    # BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)\n", "    BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)\n", "    BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)\n", "\n", "    BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)\n", "    BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)\n", "    BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)\n", "    BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)\n", "    BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)\n", "\n", "    BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)\n", "    BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)\n", "    BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)\n", "    BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)\n", "    BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)\n", "    BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)\n", "    BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)\n", "    BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)\n", "    BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)\n", "    BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)\n", "    BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)\n", "    BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)\n", "    BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)\n", "\n", "    BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)\n", "    BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)\n", "    BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)\n", "    BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)\n", "    BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)\n", "\n", "    BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)\n", "\n", "    BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)\n", "    BETA_age_between_24_35 = Beta('BETA_age_between_24_35', 0.0, None, None, 0)\n", "    BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)\n", "\n", "    BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)\n", "    BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)\n", "\n", "    BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)\n", "\n", "    BETA_children = Beta('BETA_children', 0.0, None, None, 0)\n", "\n", "    BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)\n", "    BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)\n", "    BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)\n", "\n", "    BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)\n", "    BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)\n", "    BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)\n", "\n", "    BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)\n", "    BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)\n", "    BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)\n", "    BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)\n", "\n", "    BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)\n", "    BETA_disposableincome_more_10000 = Beta('BETA_disposableincome_more_10000', 0.0, None, None, 0)\n", "\n", "    BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)\n", "\n", "    #* structual equations\n", "    sigma_1 = Beta('sigma_1', 1, None, None, 0)\n", "    omega_1 = bioDraws('omega_1', 'NORMAL_MLHS')\n", "\n", "    # coef_citytime_Att * citytime_less_3 + \\\n", "    # coef_age_Att  * age_less_24 + \\\n", "    Att = coef_intercept_Att + \\\n", "        coef_age_Att  * age_less_24 + \\\n", "        coef_gender_Att  * gender_female + \\\n", "        coef_edu_Att * edu_less_junior + \\\n", "        coef_marriage_Att * marriage_not + \\\n", "        coef_children_Att * children + \\\n", "        coef_family_Att * family_big + \\\n", "        coef_monthincome_Att * monthincome_less_4000 + \\\n", "        coef_disposableincome_Att * disposableincome_more_10000\n", "    # + \\\n", "    #     omega_1 * sigma_1\n", "\n", "    MODEL_Att2 = INTER_Att2 + B_Att2 * Att\n", "    MODEL_Att3 = INTER_Att3 + B_Att3 * Att\n", "    MODEL_Att4 = INTER_Att4 + B_Att4 * Att\n", "    MODEL_Att5 = INTER_Att5 + B_Att5 * Att\n", "    MODEL_Att6 = INTER_Att6 + B_Att6 * Att\n", "    \n", "\n", "    sigma_7 = Beta('sigma_7', 1, None, None, 0)\n", "    omega_7 = bioDraws('omega_7', 'NORMAL_MLHS')\n", "\n", "        # coef_citytime_Habit * citytime_less_3 + \\\n", "    Habit = coef_intercept_Habit + \\\n", "        coef_gender_Habit  * gender_female + \\\n", "        coef_age_Habit  * age_less_24 + \\\n", "        coef_edu_Habit * edu_less_junior + \\\n", "        coef_marriage_Habit * marriage_not + \\\n", "        coef_children_Habit * children + \\\n", "        coef_family_Habit * family_big + \\\n", "        coef_monthincome_Habit * monthincome_less_4000 + \\\n", "        coef_disposableincome_Habit * disposableincome_more_10000\n", "    # + \\\n", "    #     omega_7 * sigma_7\n", "\n", "    MODEL_Habit1 = INTER_Habit_1 + B_Habit1 * Habit\n", "    MODEL_Habit2 = INTER_Habit_2 + B_Habit2 * Habit\n", "    MODEL_Habit3 = INTER_Habit_3 + B_Habit3 * Habit\n", "    MODEL_Habit4 = INTER_Habit_4 + B_Habit4 * Habit\n", "    MODEL_Habit5 = INTER_Habit_5 + B_Habit5 * Habit\n", "\n", "    # As the measurements are using a Likert scale with M = 5 levels, we deﬁne 4 parameters\n", "    delta_1 = Beta('delta_1', 0.1, 1.0e-5, None, 0)\n", "    delta_2 = Beta('delta_2', 0.2, 1.0e-5, None, 0)\n", "    tau_1 = -delta_1 - delta_2\n", "    tau_2 = -delta_1\n", "    tau_3 = delta_1\n", "    tau_4 = delta_1 + delta_2\n", "\n", "    #* measurement equations\n", "    Att2_tau_1 = (tau_1 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_2 = (tau_2 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_3 = (tau_3 - MODEL_Att2) / SIGMA_Att2\n", "    Att2_tau_4 = (tau_4 - MODEL_Att2) / SIGMA_Att2\n", "    IndiAtt2 = {\n", "        1: bioNormalCdf(Att2_tau_1),\n", "        2: bioNormalCdf(Att2_tau_2) - bioNormalCdf(Att2_tau_1),\n", "        3: bioNormalCdf(Att2_tau_3) - bioNormalCdf(Att2_tau_2),\n", "        4: bioNormalCdf(Att2_tau_4) - bioNormalCdf(Att2_tau_3),\n", "        5: 1 - bioNormalCdf(Att2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att2 = Elem(IndiAtt2, Attitude2)\n", "\n", "    Att3_tau_1 = (tau_1 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_2 = (tau_2 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_3 = (tau_3 - MODEL_Att3) / SIGMA_Att3\n", "    Att3_tau_4 = (tau_4 - MODEL_Att3) / SIGMA_Att3\n", "    IndiAtt3 = {\n", "        1: bioNormalCdf(Att3_tau_1),\n", "        2: bioNormalCdf(Att3_tau_2) - bioNormalCdf(Att3_tau_1),\n", "        3: bioNormalCdf(Att3_tau_3) - bioNormalCdf(Att3_tau_2),\n", "        4: bioNormalCdf(Att3_tau_4) - bioNormalCdf(Att3_tau_3),\n", "        5: 1 - bioNormalCdf(Att3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att3 = Elem(IndiAtt3, Attitude3)\n", "\n", "    Att4_tau_1 = (tau_1 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_2 = (tau_2 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_3 = (tau_3 - MODEL_Att4) / SIGMA_Att4\n", "    Att4_tau_4 = (tau_4 - MODEL_Att4) / SIGMA_Att4\n", "    IndiAtt4 = {\n", "        1: bioNormalCdf(Att4_tau_1),\n", "        2: bioNormalCdf(Att4_tau_2) - bioNormalCdf(Att4_tau_1),\n", "        3: bioNormalCdf(Att4_tau_3) - bioNormalCdf(Att4_tau_2),\n", "        4: bioNormalCdf(Att4_tau_4) - bioNormalCdf(Att4_tau_3),\n", "        5: 1 - bioNormalCdf(Att4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att4 = Elem(IndiAtt4, Attitude4)\n", "\n", "    Att5_tau_1 = (tau_1 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_2 = (tau_2 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_3 = (tau_3 - MODEL_Att5) / SIGMA_Att5\n", "    Att5_tau_4 = (tau_4 - MODEL_Att5) / SIGMA_Att5\n", "    IndiAtt5 = {\n", "        1: bioNormalCdf(Att5_tau_1),\n", "        2: bioNormalCdf(Att5_tau_2) - bioNormalCdf(Att5_tau_1),\n", "        3: bioNormalCdf(Att5_tau_3) - bioNormalCdf(Att5_tau_2),\n", "        4: bioNormalCdf(Att5_tau_4) - bioNormalCdf(Att5_tau_3),\n", "        5: 1 - bioNormalCdf(Att5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att5 = Elem(IndiAtt5, Attitude5)\n", "\n", "    Att6_tau_1 = (tau_1 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_2 = (tau_2 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_3 = (tau_3 - MODEL_Att6) / SIGMA_Att6\n", "    Att6_tau_4 = (tau_4 - MODEL_Att6) / SIGMA_Att6\n", "    IndiAtt6 = {\n", "        1: bioNormalCdf(Att6_tau_1),\n", "        2: bioNormalCdf(Att6_tau_2) - bioNormalCdf(Att6_tau_1),\n", "        3: bioNormalCdf(Att6_tau_3) - bioNormalCdf(Att6_tau_2),\n", "        4: bioNormalCdf(Att6_tau_4) - bioNormalCdf(Att6_tau_3),\n", "        5: 1 - bioNormalCdf(Att6_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Att6 = Elem(IndiAtt6, Attitude6)\n", "\n", "\n", "    Habit1_tau_1 = (tau_1 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_2 = (tau_2 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_3 = (tau_3 - MODEL_Habit1) / SIGMA_Habit1\n", "    Habit1_tau_4 = (tau_4 - MODEL_Habit1) / SIGMA_Habit1\n", "    IndiHabit1 = {\n", "        1: bioNormalCdf(Habit1_tau_1),\n", "        2: bioNormalCdf(Habit1_tau_2) - bioNormalCdf(Habit1_tau_1),\n", "        3: bioNormalCdf(Habit1_tau_3) - bioNormalCdf(Habit1_tau_2),\n", "        4: bioNormalCdf(Habit1_tau_4) - bioNormalCdf(Habit1_tau_3),\n", "        5: 1 - bioNormalCdf(Habit1_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit1 = Elem(IndiHabit1, Habit1)\n", "\n", "    Habit2_tau_1 = (tau_1 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_2 = (tau_2 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_3 = (tau_3 - MODEL_Habit2) / SIGMA_Habit2\n", "    Habit2_tau_4 = (tau_4 - MODEL_Habit2) / SIGMA_Habit2\n", "    IndiHabit2 = {\n", "        1: bioNormalCdf(Habit2_tau_1),\n", "        2: bioNormalCdf(Habit2_tau_2) - bioNormalCdf(Habit2_tau_1),\n", "        3: bioNormalCdf(Habit2_tau_3) - bioNormalCdf(Habit2_tau_2),\n", "        4: bioNormalCdf(Habit2_tau_4) - bioNormalCdf(Habit2_tau_3),\n", "        5: 1 - bioNormalCdf(Habit2_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit2 = Elem(IndiHabit2, Habit2)\n", "\n", "    Habit3_tau_1 = (tau_1 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_2 = (tau_2 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_3 = (tau_3 - MODEL_Habit3) / SIGMA_Habit3\n", "    Habit3_tau_4 = (tau_4 - MODEL_Habit3) / SIGMA_Habit3\n", "    IndiHabit3 = {\n", "        1: bioNormalCdf(Habit3_tau_1),\n", "        2: bioNormalCdf(Habit3_tau_2) - bioNormalCdf(Habit3_tau_1),\n", "        3: bioNormalCdf(Habit3_tau_3) - bioNormalCdf(Habit3_tau_2),\n", "        4: bioNormalCdf(Habit3_tau_4) - bioNormalCdf(Habit3_tau_3),\n", "        5: 1 - bioNormalCdf(Habit3_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit3 = Elem(IndiHabit3, Habit3)\n", "\n", "    Habit4_tau_1 = (tau_1 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_2 = (tau_2 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_3 = (tau_3 - MODEL_Habit4) / SIGMA_Habit4\n", "    Habit4_tau_4 = (tau_4 - MODEL_Habit4) / SIGMA_Habit4\n", "    IndiHabit4 = {\n", "        1: bioNormalCdf(Habit4_tau_1),\n", "        2: bioNormalCdf(Habit4_tau_2) - bioNormalCdf(Habit4_tau_1),\n", "        3: bioNormalCdf(Habit4_tau_3) - bioNormalCdf(Habit4_tau_2),\n", "        4: bioNormalCdf(Habit4_tau_4) - bioNormalCdf(Habit4_tau_3),\n", "        5: 1 - bioNormalCdf(Habit4_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit4 = Elem(IndiHabit4, Habit4)\n", "\n", "    Habit5_tau_1 = (tau_1 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_2 = (tau_2 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_3 = (tau_3 - MODEL_Habit5) / SIGMA_Habit5\n", "    Habit5_tau_4 = (tau_4 - MODEL_Habit5) / SIGMA_Habit5\n", "    IndiHabit5 = {\n", "        1: bioNormalCdf(Habit5_tau_1),\n", "        2: bioNormalCdf(Habit5_tau_2) - bioNormalCdf(Habit5_tau_1),\n", "        3: bioNormalCdf(Habit5_tau_3) - bioNormalCdf(Habit5_tau_2),\n", "        4: bioNormalCdf(Habit5_tau_4) - bioNormalCdf(Habit5_tau_3),\n", "        5: 1 - bioNormalCdf(Habit5_tau_4),\n", "        6: 1.0,\n", "        -1: 1.0,\n", "        -2: 1.0}\n", "\n", "    P_Habit5 = Elem(IndiHabit5, Habit5)\n", "\n", "    V1 = BETA_intercept + \\\n", "            BETA_deliverytime_short * deliverytime_short + \\\n", "            BETA_deliverymode_full * deliverymode_full + \\\n", "            BETA_deliveryposition_extended * deliveryposition_extended + \\\n", "            BETA_workingdays_standard * workingdays_standard + \\\n", "            BETA_workinghours_brief * workinghours_brief + \\\n", "            BETA_deliveryorders_brief * deliveryorders_brief + \\\n", "            BETA_deliveryrange_prolonged * deliveryrange_prolonged + \\\n", "            BETA_deliveryspeed_extended * deliveryspeed_extended + \\\n", "            BETA_gender_female  * gender_female + \\\n", "            BETA_age_less_24 * age_less_24 + \\\n", "            BETA_edu_less_junior * edu_less_junior + \\\n", "            BETA_marriage * marriage_not + \\\n", "            BETA_children * children + \\\n", "            BETA_family_big * family_big + \\\n", "            BETA_monthincome_less_4000 * monthincome_less_4000 + \\\n", "            BETA_disposableincome_more_10000 * disposableincome_more_10000 + \\\n", "            BETA_Habit * Habit + \\\n", "            BETA_Att * Att\n", "\n", "    V2 = 0\n", "\n", "    # Associate utility functions with the numbering of alternatives\n", "    V = {1: V1,\n", "        2: V2,\n", "        }\n", "    \n", "    # mode probability\n", "    prob_accident = models.logit(V, None, 1) \n", "    prob_safe  = models.logit(V, None, 2)\n", "    \n", "    simulate = {\n", "    'prob_accident': prob_accident,\n", "    'prob_safe': prob_safe,\n", "    }\n", "\n", "    biogeme = bio.BIOGEME(database, simulate)\n", "    biogeme.modelName = 'accident_simulation'\n", "    results = res.bioResults(pickleFile='accident_iclv_Att_Habit2024-07-17_14-31-34.pickle')\n", "\n", "    simulatedValues = biogeme.simulate(results.getBetaValues())\n", "\n", "    # Calculate confidence intervals\n", "    betas = biogeme.freeBetaNames\n", "    b_bootstrap = results.getBetasForSensitivityAnalysis(betas)\n", "    b_normal = results.getBetasForSensitivityAnalysis(\n", "        betas, size=100, useBootstrap=False\n", "    )\n", "\n", "    # Returns data frame containing, for each simulated value, the left\n", "    # and right bounds of the confidence interval calculated by\n", "    # simulation.\n", "    left_bootstrap, right_bootstrap = biogeme.confidenceIntervals(b_bootstrap, 0.9)\n", "    left_normal, right_normal = biogeme.confidenceIntervals(b_normal, 0.9)\n", "\n", "    # We calculate now the market shares and their confidence intervals\n", "\n", "    marketShare_accident = simulatedValues['prob_accident'].mean()\n", "\n", "    marketShare_accident_left_bootstrap = left_bootstrap['prob_accident'].mean()\n", "    marketShare_accident_right_bootstrap = right_bootstrap['prob_accident'].mean()\n", "    marketShare_accident_left_normal = left_normal['prob_accident'].mean()\n", "    marketShare_accident_right_normal = right_normal['prob_accident'].mean()\n", "    \n", "\n", "    marketShare_safe = simulatedValues['prob_safe'].mean()\n", "\n", "    marketShare_safe_left_bootstrap = left_bootstrap['prob_safe'].mean()\n", "    marketShare_safe_right_bootstrap = right_bootstrap['prob_safe'].mean()\n", "    marketShare_safe_left_normal = left_normal['prob_safe'].mean()\n", "    marketShare_safe_right_normal = right_normal['prob_safe'].mean()\n", "\n", "    accident_list = [marketShare_accident, marketShare_accident_left_bootstrap, marketShare_accident_right_bootstrap, marketShare_accident_left_normal, marketShare_accident_right_normal]\n", "    safe_list = [marketShare_safe, marketShare_safe_left_bootstrap, marketShare_safe_right_bootstrap, marketShare_safe_left_normal, marketShare_safe_right_normal]\n", "\n", "    return accident_list, safe_list\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 计算"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[16:43:01] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:02] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:03] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:04] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:05] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:07] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:08] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:09] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:10] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:11] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:12] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:14] < General >   Remove 65 unused variables from the database as only 16 are used.\n", "[16:43:15] < General >   Remove 65 unused variables from the database as only 16 are used.\n"]}], "source": ["# 生成0-1对角数组，numpy.eye(N,M=None, k=0, dtype=<type 'float'>)\n", "import numpy as np\n", "variable=['original',\n", "          'Gender (female)',\n", "          'Age (less than 24)',\n", "          \"Education level (less than junior school)\",\n", "          \"Marital status (single)\",\n", "          'Parental Status (with children)',\n", "          'Family size (above 4 people)',\n", "          'Income (less than 4,000)',\n", "          'Working hours (less than 6 hours)',\n", "          'Working days (less than 5 days)',\n", "          'Daily delivery orders (less than 20)',\n", "          'Daily delivery mileage (above 80 km)',\n", "          'Driving speed (above 40km/h)']\n", "\n", "list0 = [0]*(len(variable)-1)\n", "array_0 = np.array(list0)\n", "list1 = [1]*(len(variable)-1)\n", "diagonal = np.diag(list1)\n", "\n", "# 生成最终数组\n", "martix2 = np.vstack((array_0,diagonal))\n", "martix1 = pd.DataFrame(data = martix2)\n", "martix = martix1.to_dict()\n", "margin = pd.DataFrame(data = np.zeros((len(variable),4)), \n", "                      index = variable, \n", "                      columns = ['Marginal effect','Marginal_effect_%','marketShare_Accident','marketShare_Accident_%'])\n", "\n", "\n", "df = dforigin[dforigin['QeTime'] >= 120]\n", "df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]\n", "\n", "for i in range(len(variable)):\n", "    # 先计算初始化概率\n", "    df1 = df.copy()\n", "    origin1, origin2 = margin_effect(df1,i,martix)\n", "    margin.loc[variable[i],'marketShare_Accident']= origin1[0]\n", "    margin.loc[variable[i],'marketShare_Accident_%']=round(100*origin1[0],2)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["margin['Marginal effect'] = margin['marketShare_Accident']-margin.loc['original','marketShare_Accident']\n", "margin['Marginal_effect_%'] = margin['marketShare_Accident_%']-margin.loc['original','marketShare_Accident_%']\n", "margin.to_excel('margin_effect_simulate_final1.xlsx',sheet_name='Sheet1')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: {0: 0,\n", "  1: 1,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 1: {0: 0,\n", "  1: 0,\n", "  2: 1,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 2: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 1,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 3: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 1,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 4: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 1,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 5: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 1,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 6: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 1,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 7: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 1,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 8: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 1,\n", "  10: 0,\n", "  11: 0,\n", "  12: 0},\n", " 9: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 1,\n", "  11: 0,\n", "  12: 0},\n", " 10: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 1,\n", "  12: 0},\n", " 11: {0: 0,\n", "  1: 0,\n", "  2: 0,\n", "  3: 0,\n", "  4: 0,\n", "  5: 0,\n", "  6: 0,\n", "  7: 0,\n", "  8: 0,\n", "  9: 0,\n", "  10: 0,\n", "  11: 0,\n", "  12: 1}}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["martix"]}], "metadata": {"kernelspec": {"display_name": "behavior", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.16"}}, "nbformat": 4, "nbformat_minor": 2}