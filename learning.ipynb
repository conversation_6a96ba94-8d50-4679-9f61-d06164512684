import pandas as pd
import os
import shutil
import matplotlib.pyplot as plt

dforigin = pd.read_excel('new_412.xlsx')
df = dforigin[dforigin['QeTime'] >= 120]
df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]

df.columns

from factor_analyzer import FactorAnalyzer

# 1) 假设我们想提取2个因子（对应“不良生活习惯”和“遵守交通规则态度”)
fa = FactorAnalyzer(n_factors=2, rotation='varimax')
# 2) 拟合数据
fa.fit(df[['Attitude2', 'Attitude3', 'Attitude4', 'Attitude5', 'Attitude6', 
           'Habit1', 'Habit2', 'Habit3', 'Habit4', 'Habit5']])

# 3) 获取各题目的因子载荷
loadings = fa.loadings_
print("因子载荷矩阵（Loadings）：\n", loadings)

# 4) 获取因子的方差解释情况
ev, v = fa.get_eigenvalues()
print("特征值（Eigenvalues）：\n", ev)

# 提取因子得分

factor_scores = fa.transform(df[['Attitude2', 'Attitude3', 'Attitude4', 'Attitude5', 'Attitude6', 
           'Habit1', 'Habit2', 'Habit3', 'Habit4', 'Habit5']])
print(factor_scores.shape)


df_factor_scores = pd.DataFrame(factor_scores, columns=['Rule-breaking propensity', 'Unhealthy lifestyles'])


df

df_factor_scores

df_final = pd.concat([df.reset_index(drop=True), df_factor_scores.reset_index(drop=True)], axis=1)
df_final

# 定义分箱区间和标签
bins = [-float('inf'), -1, -0.5, 0, 0.5, 1, 1.5, 2, float('inf')]
labels = ['<-1', '-1~-0.5', '-0.5~0', '0~0.5', '0.5~1', '1~1.5', '1.5~2', '>2']

# 对Rule-breaking propensity进行分箱
df_final['Rule-breaking propensity group'] = pd.cut(df_final['Rule-breaking propensity'], bins=bins, labels=labels, right=True, include_lowest=True)

# 统计每一类中的碰撞事故概率（UnsafeAccident=1的占比）
# Map UnsafeAccident to 0 and 1 for probability calculation
df_final['UnsafeAccident_mapped'] = df_final['UnsafeAccident'].apply(lambda x: 1 if x == 1 else 0)
accident_prob_by_group = df_final.groupby('Rule-breaking propensity group', observed=False)['UnsafeAccident_mapped'].mean() # Pandas 1.5.0+ needs observed=False for old behavior
print("每一类中的碰撞事故概率（UnsafeAccident=1的占比）:")
print(accident_prob_by_group)

# 统计所有碰撞事故中事故严重程度的概率（Severity = 1,2,3的占比）
# 先筛选出所有发生碰撞事故的样本
accident_df = df_final[df_final['UnsafeAccident'] == 1]

# 计算Severity为1,2,3的占比
severity_counts = accident_df['Severity'].value_counts(normalize=True).sort_index()
print("\n所有碰撞事故中事故严重程度的概率（Severity = 1,2,3的占比）:")
for sev in [1, 2, 3]:
    prob = severity_counts.get(sev, 0)
    print(f"Severity={sev}: {prob:.2%}")

# 统计不同Rule-breaking propensity group中，碰撞事故（UnsafeAccident=1）下的Severity概率分布
print("\n不同Rule-breaking propensity group中，碰撞事故中的事故严重程度概率分布：")
severity_dist_by_group = {}

for group in labels:
    # 选出该group下发生碰撞事故的样本
    group_accidents = df_final[(df_final['Rule-breaking propensity group'] == group) & (df_final['UnsafeAccident'] == 1)]
    total = len(group_accidents)
    if total == 0:
        print(f"Group {group}: 无碰撞事故样本")
        severity_dist_by_group[group] = {1: 0, 2: 0, 3: 0}
        continue
    # 计算Severity为1,2,3的占比
    sev_counts = group_accidents['Severity'].value_counts(normalize=True).sort_index()
    print(f"Group {group}:")
    for sev in [1, 2, 3]:
        prob = sev_counts.get(sev, 0)
        print(f"  Severity={sev}: {prob:.2%}")
    severity_dist_by_group[group] = {sev: sev_counts.get(sev, 0) for sev in [1, 2, 3]}


# 定义分箱区间和标签
bins = [-float('inf'), 0, 0.5, 1, float('inf')]
labels = ['<0', '0~0.5', '0.5~1', '>1']

# 对Rule-breaking propensity进行分箱
df_final['Rule-breaking propensity group'] = pd.cut(df_final['Rule-breaking propensity'], bins=bins, labels=labels, right=True, include_lowest=True)

# 统计每一类中的碰撞事故概率（UnsafeAccident=1的占比）
# Map UnsafeAccident to 0 and 1 for probability calculation
df_final['UnsafeAccident_mapped'] = df_final['UnsafeAccident'].apply(lambda x: 1 if x == 1 else 0)
accident_prob_by_group = df_final.groupby('Rule-breaking propensity group', observed=False)['UnsafeAccident_mapped'].mean() # Pandas 1.5.0+ needs observed=False for old behavior
print("每一类中的碰撞事故概率（UnsafeAccident=1的占比）:")
print(accident_prob_by_group)

# 统计所有碰撞事故中事故严重程度的概率（Severity = 1,2,3的占比）
# 先筛选出所有发生碰撞事故的样本
accident_df = df_final[df_final['UnsafeAccident'] == 1]

# 计算Severity为1,2,3的占比
severity_counts = accident_df['Severity'].value_counts(normalize=True).sort_index()
print("\n所有碰撞事故中事故严重程度的概率（Severity = 1,2,3的占比）:")
for sev in [1, 2, 3]:
    prob = severity_counts.get(sev, 0)
    print(f"Severity={sev}: {prob:.2%}")

# 统计不同Rule-breaking propensity group中，碰撞事故（UnsafeAccident=1）下的Severity概率分布
print("\n不同Rule-breaking propensity group中，碰撞事故中的事故严重程度概率分布：")
severity_dist_by_group = {}

for group in labels:
    # 选出该group下发生碰撞事故的样本
    group_accidents = df_final[(df_final['Rule-breaking propensity group'] == group) & (df_final['UnsafeAccident'] == 1)]
    total = len(group_accidents)
    if total == 0:
        print(f"Group {group}: 无碰撞事故样本")
        severity_dist_by_group[group] = {1: 0, 2: 0, 3: 0}
        continue
    # 计算Severity为1,2,3的占比
    sev_counts = group_accidents['Severity'].value_counts(normalize=True).sort_index()
    print(f"Group {group}:")
    for sev in [1, 2, 3]:
        prob = sev_counts.get(sev, 0)
        print(f"  Severity={sev}: {prob:.2%}")
    severity_dist_by_group[group] = {sev: sev_counts.get(sev, 0) for sev in [1, 2, 3]}



import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Set font to Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman'] + plt.rcParams['font.serif']


# Data provided by the user
# 每一类中的碰撞事故概率（UnsafeAccident=1的占比）:
# Rule-breaking propensity group
groups = [
    "($\infty$, 0)",      # Index 0
    "[0, 0.5)",    # Index 3
    "[0.5, 1)",    # Index 4
    "[1, $\infty$)"        # Index 7
]
probabilities = [
    0.175676,
    0.214286,
    0.461538, # Sharp increase from previous group
    0.446809,
]

# Create a Pandas DataFrame
df = pd.DataFrame({
    'Rule-breaking propensity group': groups,
    'Probability of Unsafe Accident': probabilities
})

# Define bar properties and spacing
bar_width = 0.8  # Width of each bar
reduced_inter_bar_space = 0.05  # Reduced space between specified pairs of bars
normal_inter_bar_space = 0.5    # Normal space between other bars (i.e., between pairs)

# Calculate custom x-coordinates for the centers of the bars
x_centers = []
current_center = 0.0

# Bar 0 (index 0)
x_centers.append(current_center)

# Bar 1 (index 1) - closer to Bar 0
current_center += bar_width + reduced_inter_bar_space
x_centers.append(current_center)

# Bar 2 (index 2) - normal spacing from Bar 1
current_center += bar_width + normal_inter_bar_space
x_centers.append(current_center)

# Bar 3 (index 3) - closer to Bar 2
current_center += bar_width + reduced_inter_bar_space
x_centers.append(current_center)

# # Bar 4 (index 4) - normal spacing from Bar 3
# current_center += bar_width + normal_inter_bar_space
# x_centers.append(current_center)

# # Bar 5 (index 5) - closer to Bar 4
# current_center += bar_width + reduced_inter_bar_space
# x_centers.append(current_center)

# # Bar 6 (index 6) - normal spacing from Bar 5
# current_center += bar_width + normal_inter_bar_space
# x_centers.append(current_center)

# # Bar 7 (index 7) - closer to Bar 6
# current_center += bar_width + reduced_inter_bar_space
# x_centers.append(current_center)


# Create the bar chart
plt.figure(figsize=(9, 6), dpi=600) # Adjusted figsize slightly for potentially wider plot
bars = plt.bar(x_centers, df['Probability of Unsafe Accident'], 
               width=bar_width, color='#99DAEE', edgecolor='black')

# Add titles and labels
plt.xlabel("Rule-breaking propensity group", fontsize=22)
plt.ylabel("Probability of crash", fontsize=22)
# plt.title("Crash Probability by Rule-breaking Propensity Group", fontsize=15, fontweight='bold')

# Set x-axis ticks to the calculated centers and use group names as labels
plt.xticks(x_centers, df['Rule-breaking propensity group'], rotation=0, ha="right", fontsize=22)
plt.yticks(fontsize=22)
plt.ylim(0, max(probabilities) * 1.12) # Slightly increased ylim for text label space
plt.gca().yaxis.set_major_formatter(plt.matplotlib.ticker.PercentFormatter(xmax=1.0))


# Add a horizontal grid for easier value reading
plt.grid(axis='y', linestyle='--', alpha=0.7)

# Add data labels on top of each bar
for bar in bars:
    yval = bar.get_height()
    # bar.get_x() gives the left edge of the bar. Center is bar.get_x() + bar.get_width()/2.0
    plt.text(bar.get_x() + bar.get_width()/2.0, yval + 0.01, f'{yval*100:.1f}%', 
             ha='center', va='bottom', fontsize=22, fontweight='medium')


plt.tight_layout() # Adjust layout to prevent labels from overlapping
# plt.show()
plt.savefig('break_propensity.pdf')


"""
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Set font to Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman'] + plt.rcParams['font.serif']


# Data provided by the user
# 每一类中的碰撞事故概率（UnsafeAccident=1的占比）:
# Rule-breaking propensity group
groups = [
    "($\infty$, -1)",      # Index 0
    "[-1, -0.5)",  # Index 1
    "[-0.5, 0)",   # Index 2
    "[0, 0.5)",    # Index 3
    "[0.5, 1)",    # Index 4
    "[1, 1.5)",    # Index 5
    "[1.5, 2)",    # Index 6
    "[2, $\infty$)"        # Index 7
]
probabilities = [
    0.100000,
    0.088235,
    0.400000, # Sharp increase from previous group
    0.214286,
    0.461538,
    0.458333,
    0.333333,
    0.800000  # Sharp increase from previous group
]

# Create a Pandas DataFrame
df = pd.DataFrame({
    'Rule-breaking propensity group': groups,
    'Probability of Unsafe Accident': probabilities
})

# Define bar properties and spacing
bar_width = 0.8  # Width of each bar
reduced_inter_bar_space = 0.05  # Reduced space between specified pairs of bars
normal_inter_bar_space = 0.5    # Normal space between other bars (i.e., between pairs)

# Calculate custom x-coordinates for the centers of the bars
x_centers = []
current_center = 0.0

# Bar 0 (index 0)
x_centers.append(current_center)

# Bar 1 (index 1) - closer to Bar 0
current_center += bar_width + reduced_inter_bar_space
x_centers.append(current_center)

# Bar 2 (index 2) - normal spacing from Bar 1
current_center += bar_width + normal_inter_bar_space
x_centers.append(current_center)

# Bar 3 (index 3) - closer to Bar 2
current_center += bar_width + reduced_inter_bar_space
x_centers.append(current_center)

# Bar 4 (index 4) - normal spacing from Bar 3
current_center += bar_width + normal_inter_bar_space
x_centers.append(current_center)

# Bar 5 (index 5) - closer to Bar 4
current_center += bar_width + reduced_inter_bar_space
x_centers.append(current_center)

# Bar 6 (index 6) - normal spacing from Bar 5
current_center += bar_width + normal_inter_bar_space
x_centers.append(current_center)

# Bar 7 (index 7) - closer to Bar 6
current_center += bar_width + reduced_inter_bar_space
x_centers.append(current_center)


# Create the bar chart
plt.figure(figsize=(9, 6), dpi=600) # Adjusted figsize slightly for potentially wider plot
bars = plt.bar(x_centers, df['Probability of Unsafe Accident'], 
               width=bar_width, color='#99DAEE', edgecolor='black')

# Add titles and labels
plt.xlabel("Rule-breaking propensity group", fontsize=16)
plt.ylabel("Probability of crash", fontsize=16)
# plt.title("Crash Probability by Rule-breaking Propensity Group", fontsize=15, fontweight='bold')

# Set x-axis ticks to the calculated centers and use group names as labels
plt.xticks(x_centers, df['Rule-breaking propensity group'], rotation=45, ha="right", fontsize=16)
plt.yticks(fontsize=16)
plt.ylim(0, max(probabilities) * 1.12) # Slightly increased ylim for text label space
plt.gca().yaxis.set_major_formatter(plt.matplotlib.ticker.PercentFormatter(xmax=1.0))


# Add a horizontal grid for easier value reading
plt.grid(axis='y', linestyle='--', alpha=0.7)

# Add data labels on top of each bar
for bar in bars:
    yval = bar.get_height()
    # bar.get_x() gives the left edge of the bar. Center is bar.get_x() + bar.get_width()/2.0
    plt.text(bar.get_x() + bar.get_width()/2.0, yval + 0.01, f'{yval*100:.1f}%', 
             ha='center', va='bottom', fontsize=16, fontweight='medium')


plt.tight_layout() # Adjust layout to prevent labels from overlapping
# plt.show()
plt.savefig('break_propensity.pdf')
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Set font to Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman'] + plt.rcParams['font.serif']

# Group names (reused from previous chart for consistency)
groups = [
    "< 0",      # Index 0
    "[0, 0.5)",    # Index 3
    "[0.5, 1)",    # Index 4
    "> 1"        # Index 7
]
# Severity distribution data
# Data format: {group_name: [Prob_Sev1, Prob_Sev2, Prob_Sev3]}
# Probabilities should sum to 1.0 (or 100%) for each group.
severity_data_raw = {
    "< 0":     [0.0385, 0.0385, 0.9231],
    "[0, 0.5)":   [0.1111, 0.1111, 0.7778],
    "[0.5, 1)":   [0.0556, 0.1667, 0.7778],
    "> 1"  :      [0.2857, 0.1905, 0.5238],
}

# Extract probabilities for each severity level, ordered by the 'groups' list
sev1_probs = np.array([severity_data_raw[g][0] for g in groups])
sev2_probs = np.array([severity_data_raw[g][1] for g in groups])
sev3_probs = np.array([severity_data_raw[g][2] for g in groups])


# Define bar properties and spacing (reused from previous chart)
bar_width = 0.8
reduced_inter_bar_space = 0.05
normal_inter_bar_space = 0.5

# Calculate custom x-coordinates for the centers of the bars (reused)
x_centers = []
current_center = 0.0
for i in range(len(groups)):
    if i == 0:
        x_centers.append(current_center)
    elif i % 2 == 1: # Second bar in a pair (closer to previous)
        current_center += bar_width + reduced_inter_bar_space
        x_centers.append(current_center)
    else: # First bar of a new pair (normal spacing from previous pair's second bar)
        current_center += bar_width + normal_inter_bar_space
        x_centers.append(current_center)

# Colors for severity levels
color_sev1 = '#FFB6C1' # Light Blue
color_sev2 = '#ADD8E6' # Light Pink / Salmon
color_sev3 = '#90EE90' # Light Green

# Create the stacked bar chart
plt.figure(figsize=(9, 8), dpi=600) # Adjusted figsize slightly

# Plot Severity 1
bars1 = plt.bar(x_centers, sev1_probs, width=bar_width, label='Severe injuries', color=color_sev1, edgecolor='black')
# Plot Severity 2 (stacked on top of Severity 1)
bars2 = plt.bar(x_centers, sev2_probs, width=bar_width, bottom=sev1_probs, label='Minor injuries', color=color_sev2, edgecolor='black')
# Plot Severity 3 (stacked on top of Severity 1 + Severity 2)
bars3 = plt.bar(x_centers, sev3_probs, width=bar_width, bottom=sev1_probs + sev2_probs, label='Property damage only', color=color_sev3, edgecolor='black')

# Add titles and labels
plt.xlabel("Rule-breaking propensity group", fontsize=22)
plt.ylabel("Proportion of severity", fontsize=22)
# plt.title("Crash Severity Distribution by Rule-breaking Propensity Group\n(按违规倾向分组的碰撞事故严重程度分布)", fontsize=18, fontweight='bold')

# Set x-axis ticks to the calculated centers and use group names as labels
plt.xticks(x_centers, groups, rotation=0, ha="right", fontsize=22)
plt.yticks(fontsize=22)
plt.ylim(0, 1.05) # Y-axis from 0% to 100% (plus a bit of space)
plt.gca().yaxis.set_major_formatter(plt.matplotlib.ticker.PercentFormatter(xmax=1.0))

# Add a horizontal grid for easier value reading
plt.grid(axis='y', linestyle='--', alpha=0.7)

# Add data labels to each segment of the stacked bar
# Only add label if segment height is greater than a threshold (e.g., 3%)
label_threshold = 0.03

for i in range(len(x_centers)):
    # Severity 1 labels
    if sev1_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] / 2, f'{sev1_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=22, color='black', fontweight='medium')
    # Severity 2 labels
    if sev2_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] + sev2_probs[i] / 2, f'{sev2_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=22, color='black', fontweight='medium')
    # Severity 3 labels
    if sev3_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] + sev2_probs[i] + sev3_probs[i] / 2, f'{sev3_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=22, color='black', fontweight='medium')

# Add legend
legend = plt.legend(
    # title="Severity level", 
                    fontsize=20, 
                    title_fontsize=20, 
                    # loc='center right', 
                    loc='upper center', 
                    # bbox_to_anchor=(1.3, 0.5), 
                    bbox_to_anchor=(0.48, 1.2), 
                    ncol=3,
                        labelspacing=0.3,         # 减小条目间的垂直间距
    handletextpad=0.5,        # 减小句柄和文本间的水平间距
    borderpad=0.4,            # 减小图例框的内边距
    handlelength=1.5,         # 缩短图例句柄的长度
    columnspacing=1.0         # 调整列间距（如果ncol > 1）
                    )
legend.get_frame().set_edgecolor('black')
legend.get_frame().set_boxstyle('Square')


plt.tight_layout(rect=[0, 0.05, 1, 0.95]) # Adjust layout to prevent labels/legend from overlapping, rect leaves space at bottom for legend
# plt.show()
plt.savefig('break_propensity_severity.pdf', bbox_inches='tight')


"""
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Set font to Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman'] + plt.rcParams['font.serif']

# Group names (reused from previous chart for consistency)
groups = [
    "($\infty$, -1)",      # Index 0
    "[-1, -0.5)",  # Index 1
    "[-0.5, 0)",   # Index 2
    "[0, 0.5)",    # Index 3
    "[0.5, 1)",    # Index 4
    "[1, 1.5)",    # Index 5
    "[1.5, 2)",    # Index 6
    "[2, $\infty$)" ,        # Index 7
]
# Severity distribution data
# Data format: {group_name: [Prob_Sev1, Prob_Sev2, Prob_Sev3]}
# Probabilities should sum to 1.0 (or 100%) for each group.
severity_data_raw = {
    "($\infty$, -1)":     [0.2500, 0.2500, 0.5000],
    "[-1, -0.5)": [0.0000, 0.0000, 1.0000],
    "[-0.5, 0)":  [0.0000, 0.0000, 1.0000],
    "[0, 0.5)":   [0.1111, 0.1111, 0.7778],
    "[0.5, 1)":   [0.0556, 0.1667, 0.7778],
    "[1, 1.5)":   [0.3636, 0.0000, 0.6364],
    "[1.5, 2)":   [0.1667, 0.6667, 0.1667],
    "[2, $\infty$)"  :      [0.2500, 0.0000, 0.7500],
}

# Extract probabilities for each severity level, ordered by the 'groups' list
sev1_probs = np.array([severity_data_raw[g][0] for g in groups])
sev2_probs = np.array([severity_data_raw[g][1] for g in groups])
sev3_probs = np.array([severity_data_raw[g][2] for g in groups])


# Define bar properties and spacing (reused from previous chart)
bar_width = 0.8
reduced_inter_bar_space = 0.05
normal_inter_bar_space = 0.5

# Calculate custom x-coordinates for the centers of the bars (reused)
x_centers = []
current_center = 0.0
for i in range(len(groups)):
    if i == 0:
        x_centers.append(current_center)
    elif i % 2 == 1: # Second bar in a pair (closer to previous)
        current_center += bar_width + reduced_inter_bar_space
        x_centers.append(current_center)
    else: # First bar of a new pair (normal spacing from previous pair's second bar)
        current_center += bar_width + normal_inter_bar_space
        x_centers.append(current_center)

# Colors for severity levels
color_sev1 = '#ADD8E6' # Light Blue
color_sev2 = '#FFB6C1' # Light Pink / Salmon
color_sev3 = '#90EE90' # Light Green

# Create the stacked bar chart
plt.figure(figsize=(9, 6), dpi=600) # Adjusted figsize slightly

# Plot Severity 1
bars1 = plt.bar(x_centers, sev1_probs, width=bar_width, label='Fatalities', color=color_sev1, edgecolor='black')
# Plot Severity 2 (stacked on top of Severity 1)
bars2 = plt.bar(x_centers, sev2_probs, width=bar_width, bottom=sev1_probs, label='Injuries', color=color_sev2, edgecolor='black')
# Plot Severity 3 (stacked on top of Severity 1 + Severity 2)
bars3 = plt.bar(x_centers, sev3_probs, width=bar_width, bottom=sev1_probs + sev2_probs, label='Property damage only', color=color_sev3, edgecolor='black')

# Add titles and labels
plt.xlabel("Rule-breaking propensity group", fontsize=16)
plt.ylabel("Proportion of crashes by severity", fontsize=16)
# plt.title("Crash Severity Distribution by Rule-breaking Propensity Group\n(按违规倾向分组的碰撞事故严重程度分布)", fontsize=18, fontweight='bold')

# Set x-axis ticks to the calculated centers and use group names as labels
plt.xticks(x_centers, groups, rotation=45, ha="right", fontsize=16)
plt.yticks(fontsize=16)
plt.ylim(0, 1.05) # Y-axis from 0% to 100% (plus a bit of space)
plt.gca().yaxis.set_major_formatter(plt.matplotlib.ticker.PercentFormatter(xmax=1.0))

# Add a horizontal grid for easier value reading
plt.grid(axis='y', linestyle='--', alpha=0.7)

# Add data labels to each segment of the stacked bar
# Only add label if segment height is greater than a threshold (e.g., 3%)
label_threshold = 0.03

for i in range(len(x_centers)):
    # Severity 1 labels
    if sev1_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] / 2, f'{sev1_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=14, color='black', fontweight='medium')
    # Severity 2 labels
    if sev2_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] + sev2_probs[i] / 2, f'{sev2_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=14, color='black', fontweight='medium')
    # Severity 3 labels
    if sev3_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] + sev2_probs[i] + sev3_probs[i] / 2, f'{sev3_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=14, color='black', fontweight='medium')

# Add legend
legend = plt.legend(title="Severity level", 
                    fontsize=14, 
                    title_fontsize=14, 
                    # loc='center right', 
                    loc='upper center', 
                    # bbox_to_anchor=(1.3, 0.5), 
                    bbox_to_anchor=(0.5, 1.3), 
                    ncol=3,)
legend.get_frame().set_edgecolor('black')
legend.get_frame().set_boxstyle('Square')


plt.tight_layout(rect=[0, 0.05, 1, 0.95]) # Adjust layout to prevent labels/legend from overlapping, rect leaves space at bottom for legend
# plt.show()
plt.savefig('break_propensity_severity.pdf')
"""


'''
# 定义分箱区间和标签
bins = [-float('inf'), -1, -0.5, 0, 0.5, 1, 1.5, 2, float('inf')]
labels = ['<-1', '-1~-0.5', '-0.5~0', '0~0.5', '0.5~1', '1~1.5', '1.5~2', '>2']

# 对Unhealthy lifestyles进行分箱
df_final['Unhealthy lifestyles group'] = pd.cut(df_final['Unhealthy lifestyles'], bins=bins, labels=labels, right=True, include_lowest=True)

# 统计每一类中的碰撞事故概率（UnsafeAccident=1的占比）
# Map UnsafeAccident to 0 and 1 for probability calculation
df_final['UnsafeAccident_mapped'] = df_final['UnsafeAccident'].apply(lambda x: 1 if x == 1 else 0)
accident_prob_by_group = df_final.groupby('Unhealthy lifestyles group', observed=False)['UnsafeAccident_mapped'].mean() # Pandas 1.5.0+ needs observed=False for old behavior
print("每一类中的碰撞事故概率（UnsafeAccident=1的占比）:")
print(accident_prob_by_group)

# 统计所有碰撞事故中事故严重程度的概率（Severity = 1,2,3的占比）
# 先筛选出所有发生碰撞事故的样本
accident_df = df_final[df_final['UnsafeAccident'] == 1]

# 计算Severity为1,2,3的占比
severity_counts = accident_df['Severity'].value_counts(normalize=True).sort_index()
print("\n所有碰撞事故中事故严重程度的概率（Severity = 1,2,3的占比）:")
for sev in [1, 2, 3]:
    prob = severity_counts.get(sev, 0)
    print(f"Severity={sev}: {prob:.2%}")

# 统计不同Unhealthy lifestyles group中，碰撞事故（UnsafeAccident=1）下的Severity概率分布
print("\n不同Unhealthy lifestyles group中，碰撞事故中的事故严重程度概率分布：")
severity_dist_by_group = {}

for group in labels:
    # 选出该group下发生碰撞事故的样本
    group_accidents = df_final[(df_final['Unhealthy lifestyles group'] == group) & (df_final['UnsafeAccident'] == 1)]
    total = len(group_accidents)
    if total == 0:
        print(f"Group {group}: 无碰撞事故样本")
        severity_dist_by_group[group] = {1: 0, 2: 0, 3: 0}
        continue
    # 计算Severity为1,2,3的占比
    sev_counts = group_accidents['Severity'].value_counts(normalize=True).sort_index()
    print(f"Group {group}:")
    for sev in [1, 2, 3]:
        prob = sev_counts.get(sev, 0)
        print(f"  Severity={sev}: {prob:.2%}")
    severity_dist_by_group[group] = {sev: sev_counts.get(sev, 0) for sev in [1, 2, 3]}
'''

# 定义分箱区间和标签
bins = [-float('inf'), 0, 0.5, 1, float('inf')]
labels = ['<0', '0~0.5', '0.5~1', '>1']

# 对Unhealthy lifestyles进行分箱
df_final['Unhealthy lifestyles group'] = pd.cut(df_final['Unhealthy lifestyles'], bins=bins, labels=labels, right=True, include_lowest=True)

# 统计每一类中的碰撞事故概率（UnsafeAccident=1的占比）
# Map UnsafeAccident to 0 and 1 for probability calculation
df_final['UnsafeAccident_mapped'] = df_final['UnsafeAccident'].apply(lambda x: 1 if x == 1 else 0)
accident_prob_by_group = df_final.groupby('Unhealthy lifestyles group', observed=False)['UnsafeAccident_mapped'].mean() # Pandas 1.5.0+ needs observed=False for old behavior
print("每一类中的碰撞事故概率（UnsafeAccident=1的占比）:")
print(accident_prob_by_group)

# 统计所有碰撞事故中事故严重程度的概率（Severity = 1,2,3的占比）
# 先筛选出所有发生碰撞事故的样本
accident_df = df_final[df_final['UnsafeAccident'] == 1]

# 计算Severity为1,2,3的占比
severity_counts = accident_df['Severity'].value_counts(normalize=True).sort_index()
print("\n所有碰撞事故中事故严重程度的概率（Severity = 1,2,3的占比）:")
for sev in [1, 2, 3]:
    prob = severity_counts.get(sev, 0)
    print(f"Severity={sev}: {prob:.2%}")

# 统计不同Unhealthy lifestyles group中，碰撞事故（UnsafeAccident=1）下的Severity概率分布
print("\n不同Unhealthy lifestyles group中，碰撞事故中的事故严重程度概率分布：")
severity_dist_by_group = {}

for group in labels:
    # 选出该group下发生碰撞事故的样本
    group_accidents = df_final[(df_final['Unhealthy lifestyles group'] == group) & (df_final['UnsafeAccident'] == 1)]
    total = len(group_accidents)
    if total == 0:
        print(f"Group {group}: 无碰撞事故样本")
        severity_dist_by_group[group] = {1: 0, 2: 0, 3: 0}
        continue
    # 计算Severity为1,2,3的占比
    sev_counts = group_accidents['Severity'].value_counts(normalize=True).sort_index()
    print(f"Group {group}:")
    for sev in [1, 2, 3]:
        prob = sev_counts.get(sev, 0)
        print(f"  Severity={sev}: {prob:.2%}")
    severity_dist_by_group[group] = {sev: sev_counts.get(sev, 0) for sev in [1, 2, 3]}


import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Set font to Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman'] + plt.rcParams['font.serif']


# Data provided by the user
# 每一类中的碰撞事故概率（UnsafeAccident=1的占比）:
# Rule-breaking propensity group
groups = [
    "(-$\infty$, 0)",   # Index 2
    "[0, 0.5)",    # Index 3
    "[0.5,1)",    # Index 4
    "[1, +$\infty$)",    # Index 5
]

probabilities = [
    0.136000,
    0.354839,
    0.428571,
    0.350000,
]

# Create a Pandas DataFrame
df = pd.DataFrame({
    'Unhealthy lifestyles group': groups,
    'Probability of unsafe accident': probabilities
})

# Define bar properties and spacing
bar_width = 0.8  # Width of each bar
reduced_inter_bar_space = 0.05  # Reduced space between specified pairs of bars
normal_inter_bar_space = 0.5    # Normal space between other bars (i.e., between pairs)

# Calculate custom x-coordinates for the centers of the bars
x_centers = []
current_center = 0.0

# Bar 0 (index 0)
x_centers.append(current_center)

# Bar 1 (index 1) - closer to Bar 0
current_center += bar_width + reduced_inter_bar_space
x_centers.append(current_center)

# Bar 2 (index 2) - normal spacing from Bar 1
current_center += bar_width + normal_inter_bar_space
x_centers.append(current_center)

# Bar 3 (index 3) - closer to Bar 2
current_center += bar_width + reduced_inter_bar_space
x_centers.append(current_center)

# # Bar 4 (index 4) - normal spacing from Bar 3
# current_center += bar_width + normal_inter_bar_space
# x_centers.append(current_center)

# # Bar 5 (index 5) - closer to Bar 4
# current_center += bar_width + reduced_inter_bar_space
# x_centers.append(current_center)

# # Bar 6 (index 6) - normal spacing from Bar 5
# current_center += bar_width + normal_inter_bar_space
# x_centers.append(current_center)

# # Bar 7 (index 7) - closer to Bar 6
# current_center += bar_width + reduced_inter_bar_space
# x_centers.append(current_center)


# Create the bar chart
plt.figure(figsize=(9, 6), dpi=600) # Adjusted figsize slightly for potentially wider plot
bars = plt.bar(x_centers, df['Probability of unsafe accident'], 
               width=bar_width, color='#99DAEE', edgecolor='black')

# Add titles and labels
plt.xlabel("Unhealthy lifestyles group", fontsize=22)
plt.ylabel("Probability of crash", fontsize=22)
# plt.title("Crash Probability by Rule-breaking Propensity Group", fontsize=15, fontweight='bold')

# Set x-axis ticks to the calculated centers and use group names as labels
plt.xticks(x_centers, df['Unhealthy lifestyles group'], rotation=0, ha="right", fontsize=22)
plt.yticks(fontsize=22)
plt.ylim(0, max(probabilities) * 1.12) # Slightly increased ylim for text label space
plt.gca().yaxis.set_major_formatter(plt.matplotlib.ticker.PercentFormatter(xmax=1.0))


# Add a horizontal grid for easier value reading
plt.grid(axis='y', linestyle='--', alpha=0.7)

# Add data labels on top of each bar
for bar in bars:
    yval = bar.get_height()
    # bar.get_x() gives the left edge of the bar. Center is bar.get_x() + bar.get_width()/2.0
    plt.text(bar.get_x() + bar.get_width()/2.0, yval + 0.01, f'{yval*100:.1f}%', 
             ha='center', va='bottom', fontsize=22, fontweight='medium')


plt.tight_layout() # Adjust layout to prevent labels from overlapping
# plt.show()
plt.savefig('unhealthy_lifestyles.pdf')


import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# Set font to Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman'] + plt.rcParams['font.serif']

# Group names (reused from previous chart for consistency)
groups = [
    "< 0",   # Index 2
    "[0, 0.5)",    # Index 3
    "[0.5,1)",    # Index 4
    "> 1",    # Index 5
]

# Severity distribution data
# Data format: {group_name: [Prob_Sev1, Prob_Sev2, Prob_Sev3]}
# Probabilities should sum to 1.0 (or 100%) for each group.
severity_data_raw = {
    "< 0":     [0.0588, 0.1176, 0.8235],
    "[0, 0.5)": [0.1818, 0.0455, 0.7727],
    "[0.5,1)":  [0.1429, 0.0476, 0.8095],
    "> 1":   [0.0714, 0.3571, 0.5714],
}

# Extract probabilities for each severity level, ordered by the 'groups' list
sev1_probs = np.array([severity_data_raw[g][0] for g in groups])
sev2_probs = np.array([severity_data_raw[g][1] for g in groups])
sev3_probs = np.array([severity_data_raw[g][2] for g in groups])


# Define bar properties and spacing (reused from previous chart)
bar_width = 0.8
reduced_inter_bar_space = 0.05
normal_inter_bar_space = 0.5

# Calculate custom x-coordinates for the centers of the bars (reused)
x_centers = []
current_center = 0.0
for i in range(len(groups)):
    if i == 0:
        x_centers.append(current_center)
    elif i % 2 == 1: # Second bar in a pair (closer to previous)
        current_center += bar_width + reduced_inter_bar_space
        x_centers.append(current_center)
    else: # First bar of a new pair (normal spacing from previous pair's second bar)
        current_center += bar_width + normal_inter_bar_space
        x_centers.append(current_center)

# Colors for severity levels
color_sev1 = '#FFB6C1' # Light Blue
color_sev2 = '#ADD8E6' # Light Pink / Salmon
color_sev3 = '#90EE90' # Light Green

# Create the stacked bar chart
plt.figure(figsize=(9, 8), dpi=600) # Adjusted figsize slightly

# Plot Severity 1
bars1 = plt.bar(x_centers, sev1_probs, width=bar_width, label='Severe injuries', color=color_sev1, edgecolor='black')
# Plot Severity 2 (stacked on top of Severity 1)
bars2 = plt.bar(x_centers, sev2_probs, width=bar_width, bottom=sev1_probs, label='Minor injuries', color=color_sev2, edgecolor='black')
# Plot Severity 3 (stacked on top of Severity 1 + Severity 2)
bars3 = plt.bar(x_centers, sev3_probs, width=bar_width, bottom=sev1_probs + sev2_probs, label='Property damage only', color=color_sev3, edgecolor='black')

# Add titles and labels
plt.xlabel("Unhealthy lifestyles group", fontsize=22)
plt.ylabel("Proportion of severity", fontsize=22)
# plt.title("Crash Severity Distribution by Rule-breaking Propensity Group\n(按违规倾向分组的碰撞事故严重程度分布)", fontsize=18, fontweight='bold')

# Set x-axis ticks to the calculated centers and use group names as labels
plt.xticks(x_centers, groups, rotation=0, ha="right", fontsize=22)
plt.yticks(fontsize=22)
plt.ylim(0, 1.05) # Y-axis from 0% to 100% (plus a bit of space)
plt.gca().yaxis.set_major_formatter(plt.matplotlib.ticker.PercentFormatter(xmax=1.0))

# Add a horizontal grid for easier value reading
plt.grid(axis='y', linestyle='--', alpha=0.7)

# Add data labels to each segment of the stacked bar
# Only add label if segment height is greater than a threshold (e.g., 3%)
label_threshold = 0.03

for i in range(len(x_centers)):
    # Severity 1 labels
    if sev1_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] / 2, f'{sev1_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=22, color='black', fontweight='medium')
    # Severity 2 labels
    if sev2_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] + sev2_probs[i] / 2, f'{sev2_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=22, color='black', fontweight='medium')
    # Severity 3 labels
    if sev3_probs[i] > label_threshold:
        plt.text(x_centers[i], sev1_probs[i] + sev2_probs[i] + sev3_probs[i] / 2, f'{sev3_probs[i]*100:.1f}%',
                 ha='center', va='center', fontsize=22, color='black', fontweight='medium')

# Add legend
legend = plt.legend(
    # title="Severity level", 
                    fontsize=20, 
                    title_fontsize=20, 
                    # loc='center right', 
                    loc='upper center', 
                    # bbox_to_anchor=(1.3, 0.5), 
                    bbox_to_anchor=(0.48, 1.2), 
                    ncol=3,
                    labelspacing=0.3,         # 减小条目间的垂直间距
                    handletextpad=0.5,        # 减小句柄和文本间的水平间距
                    borderpad=0.4,            # 减小图例框的内边距
                    handlelength=1.5,         # 缩短图例句柄的长度
                    columnspacing=1.0         # 调整列间距（如果ncol > 1）
                    )
legend.get_frame().set_edgecolor('black')
legend.get_frame().set_boxstyle('Square')


plt.tight_layout(rect=[0, 0.05, 1, 0.95]) # Adjust layout to prevent labels/legend from overlapping, rect leaves space at bottom for legend
# plt.show()
plt.savefig('unhealthy_lifestyles_severity.pdf', bbox_inches='tight')

# 创建新的性别列
df_final['Gender'] = df_final['gender'] == 2

# 创建新的年龄列
df_final['Age'] = df_final['age'] <= 2

# 创建新的教育程度列
df_final['Education level'] = df_final['education'] <= 2

# 创建新的婚姻状况列
df_final['Marital status'] = df_final['marriage'] <= 1

# 创建新的子女状况列
df_final['Parental status'] = ((df_final['marriage'] == 3) | (df_final['marriage'] == 4))

# 创建新的家庭规模列
df_final['Family size'] = (df_final['family'] >= 5) & (df_final['family'] <= 7)

# 创建新的工作时间列
df_final['Working days'] = df_final['workingdays'] <= 4

# 创建新的配送订单列
df_final['Daily delivery orders'] = df_final['deliveryorders'] <= 1

# 创建新的配送范围列
df_final['Daily delivery mileage'] = df_final['deliveryrange'] >= 5

# 创建新的配送速度列
df_final['Driving speed'] = df_final['deliveryspeed'] >= 4

# 创新因变量列
df_final['Crash'] = df_final['UnsafeAccident'] == 1

# 将布尔列转换为整数类型
df_final['Gender'] = df_final['Gender'].astype(int)
df_final['Age'] = df_final['Age'].astype(int)
df_final['Education level'] = df_final['Education level'].astype(int)
df_final['Marital status'] = df_final['Marital status'].astype(int)
df_final['Parental status'] = df_final['Parental status'].astype(int)
df_final['Family size'] = df_final['Family size'].astype(int)
df_final['Working days'] = df_final['Working days'].astype(int)
df_final['Daily delivery orders'] = df_final['Daily delivery orders'].astype(int)
df_final['Daily delivery mileage'] = df_final['Daily delivery mileage'].astype(int)
df_final['Driving speed'] = df_final['Driving speed'].astype(int)
df_final['Crash'] = df_final['Crash'].astype(int)
df_final['Crash_new'] = df_final['Crash'].copy()
df_final['Crash_continous'] = df_final['Crash'].copy()
df_final

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from Learning_Based import train_catboost_classification, explain_shap_catboost_new, plot_feature_importance_donut
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'Times New Roman'


indepent_vars = ['Rule-breaking propensity', 
                 'Unhealthy lifestyles', 
                 'Education level', 
                 'Marital status',
                 'Parental status', 
                 'Family size', 
                 'Working days', 
                 'Daily delivery orders', 
                 'Daily delivery mileage', 
                 'Driving speed']

# 提取特征和目标变量
X = df_final[indepent_vars].copy()
y = df_final['Crash']

# 分割训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=40)
# Example: Rename columns 
# X_train = X_train.rename(columns=lambda x: f"new_{x}")
# X_test = X_test.rename(columns=lambda x: f"new_{x}")

# 标准化特征（对于某些模型，如神经网络，标准化有助于提升性能）
# scaler = StandardScaler() # Z-score 标准化
# scaler = MinMaxScaler(feature_range=(0, 1)) # 0-1 标准化
# X_train_scaled = scaler.fit_transform(X_train)
# X_test_scaled = scaler.transform(X_test)

feature_names = X_train.columns.tolist()

# 训练 CatBoost 模型
catboost_model, catboost_pred = train_catboost_classification(X_train, y_train, X_test, y_test, n_trials=1000)
save_path = './paper_plot/'
shap_catboost = explain_shap_catboost_new(catboost_model, X_train, X_test, feature_names, save_path=save_path, depvar='Crash', num_var=5)

import importlib
from Learning_Based import train_catboost_classification, explain_shap_catboost_new, plot_feature_importance_donut

# 修改Learning_Based.py后，重新加载模块
import Learning_Based
importlib.reload(Learning_Based)

from Learning_Based import train_catboost_classification, explain_shap_catboost_new, plot_feature_importance_donut
# 训练 CatBoost 模型
catboost_model, catboost_pred = train_catboost_classification(X_train, y_train, X_test, y_test, n_trials=2000)
save_path = './paper_plot/'
shap_catboost = explain_shap_catboost_new(catboost_model, X_train, X_test, feature_names, save_path=save_path, depvar='Crash', num_var=5)

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from Learning_Based import train_catboost_classification, explain_shap_catboost_new
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'Times New Roman'


indepent_vars = ['Rule-breaking propensity', 
                 'Unhealthy lifestyles', 
                 'Gender',
                 'Age',
                 'Education level', 
                 'Marital status',
                 'Parental status', 
                 'Family size', 
                 'Working days', 
                 'Daily delivery orders', 
                 'Daily delivery mileage', 
                 'Driving speed']
# 提取特征和目标变量
X = df_final[indepent_vars].copy()
y = df_final['Crash_new']

# 分割训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=41)
# Example: Rename columns 
# X_train = X_train.rename(columns=lambda x: f"new_{x}")
# X_test = X_test.rename(columns=lambda x: f"new_{x}")

# 标准化特征（对于某些模型，如神经网络，标准化有助于提升性能）
# scaler = StandardScaler() # Z-score 标准化
# scaler = MinMaxScaler(feature_range=(0, 1)) # 0-1 标准化
# X_train_scaled = scaler.fit_transform(X_train)
# X_test_scaled = scaler.transform(X_test)

feature_names = X_train.columns.tolist()

# 训练 CatBoost 模型
catboost_model, catboost_pred = train_catboost_classification(X_train, y_train, X_test, y_test, n_trials=1000)
save_path = './paper_plot/'
shap_catboost = explain_shap_catboost_new(catboost_model, X_train, X_test, feature_names, save_path=save_path, depvar='Crash_new', num_var=10)

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from Learning_Based import train_catboost_classification, explain_shap_catboost_new
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 18


indepent_vars = ['Rule-breaking propensity', 
                 'Unhealthy lifestyles', 
                 'Gender',
                 'age',
                 'education', 
                 'Marital status',
                 'Parental status', 
                 'family', 
                 'workingdays', 
                 'deliveryorders', 
                 'deliveryrange', 
                 'deliveryspeed']

# 提取特征和目标变量
X = df_final[indepent_vars].copy()
y = df_final['Crash_continous']

# 分割训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# Example: Rename columns 
# X_train = X_train.rename(columns=lambda x: f"new_{x}")
# X_test = X_test.rename(columns=lambda x: f"new_{x}")

# 标准化特征（对于某些模型，如神经网络，标准化有助于提升性能）
# scaler = StandardScaler() # Z-score 标准化
# scaler = MinMaxScaler(feature_range=(0, 1)) # 0-1 标准化
# X_train_scaled = scaler.fit_transform(X_train)
# X_test_scaled = scaler.transform(X_test)

feature_names = X_train.columns.tolist()

# 训练 CatBoost 模型
catboost_model, catboost_pred = train_catboost_classification(X_train, y_train, X_test, y_test, n_trials=1000)
save_path = './paper_plot/'
shap_catboost = explain_shap_catboost_new(catboost_model, X_train, X_test, feature_names, save_path=save_path, depvar='Crash_continous', num_var=10)

