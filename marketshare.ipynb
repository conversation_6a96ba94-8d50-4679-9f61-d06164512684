import sys
from biogeme import models
import biogeme.biogeme as bio
import biogeme.exceptions as excep
import biogeme.results as res
from datetime import datetime
import biogeme.messaging as msg
import pandas as pd
import biogeme.database as db
import biogeme.biogeme_logging as blog
from biogeme.models import piecewiseFormula
from biogeme.results import bioResults
from biogeme import models
import biogeme.biogeme as bio
from biogeme.expressions import Beta, log, Elem, bioNormalCdf, Variable, bioDraws, MonteCarlo
import shutil
import matplotlib.pyplot as plt
import biogeme.exceptions as excep


import biogeme.results as res

def margin_effect(df, index, var):
    
    database = db.Database('data'+var, df)
    globals().update(database.variables)

    # 场景属性自变量
    deliVolume = Variable('Volume')
    deliTime = Variable('Time')
    deliWeather = Variable('Weather')
    deliLocation = Variable('Location')
    RemainTime = Variable('RemainTime')
    CauseUnsafe = Variable('CauseUnsafe')

    # 因变量
    UnsafeAccident = Variable('UnsafeAccident')

    # 社会经济属性自变量
    gender = Variable('gender')
    age = Variable('age')
    education = Variable('education')
    marriage = Variable('marriage')
    family = Variable('family')
    citytime = Variable('citytime')
    monthincome = Variable('monthincome')
    disposableincome = Variable('disposableincome')

    # 配送属性自变量
    deliverytime = Variable('deliverytime')
    deliverymode = Variable('deliverymode')
    deliveryposition = Variable('deliveryposition')
    workingdays = Variable('workingdays')
    workinghours = Variable('workinghours')
    deliveryorders = Variable('deliveryorders')
    deliveryrange = Variable('deliveryrange')
    deliveryspeed = Variable('deliveryspeed')

    # 社会经济属性二分类变量
    if index == 1:
        gender_female = 1
    else:
        gender_female = database.DefineVariable('gender_female'+var, gender == 2)

    if index == 2: 
        age_less_24 = 1
    else:
        age_less_24 = database.DefineVariable('age_less_24'+var, age <= 2) 
        
    if index == 3: 
        edu_less_junior = 1
    else:
        edu_less_junior = database.DefineVariable('edu_less_junior'+var, education <= 2)
    if index == 4: 
        marriage_not = 1
    else:
        marriage_not = database.DefineVariable('marriage_not'+var, marriage <= 1) 
    if index == 5: 
        children = 1
    else:
        children = database.DefineVariable('children'+var, ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0)) 
    if index == 6:
        family_big = 1
    else:
        family_big = database.DefineVariable('family_big'+var, (family >= 5) &  (family <= 7)) 

    monthincome_less_4000 = database.DefineVariable('monthincome_less_4000'+var, monthincome <= 2) 
    disposableincome_more_10000 = database.DefineVariable('disposableincome_more_10000'+var, disposableincome >= 3) 

    # 配送属性二分类变量
    deliverytime_short = database.DefineVariable('deliverytime_short'+var, deliverytime <= 2) 
    deliverymode_full = database.DefineVariable('deliverymode_full'+var, deliverymode >= 2) 
    deliveryposition_extended = database.DefineVariable('deliveryposition_extended'+var, deliveryposition >= 3) 
    workinghours_brief = database.DefineVariable('workinghours_brief'+var, workinghours <= 1) 
    if index == 7:
        workingdays_standard = 1
    else:
        workingdays_standard = database.DefineVariable('workingdays_standard'+var, workingdays <= 3) 
    if index == 8:  
        deliveryorders_brief = 1
    else:
        deliveryorders_brief = database.DefineVariable('deliveryorders_brief'+var, deliveryorders <= 1)
    if index == 9: 
        deliveryrange_prolonged = 1
    else:
        deliveryrange_prolonged = database.DefineVariable('deliveryrange_prolonged'+var, deliveryrange >= 5)
    if index == 10:
        deliveryspeed_extended = 1
    else:
        deliveryspeed_extended = database.DefineVariable('deliveryspeed_extended'+var, deliveryspeed == 4) 

    #* structual coefficient

    # Attitude
    coef_intercept_Att = Beta('coef_intercept_Att', 0.0, None, None, 0)
    coef_gender_Att = Beta('coef_gender_Att', 0.0, None, None, 0)
    coef_age_Att = Beta('coef_age_Att', 0.0, None, None, 0)
    coef_edu_Att = Beta('coef_edu_Att', 0.0, None, None, 0)
    coef_marriage_Att = Beta('coef_marriage_Att', 0.0, None, None, 0)
    coef_children_Att = Beta('coef_children_Att', 0.0, None, None, 0)
    coef_family_Att = Beta('coef_family_Att', 0.0, None, None, 0)
    coef_citytime_Att = Beta('coef_citytime_Att', 0.0, None, None, 0)
    coef_monthincome_Att = Beta('coef_monthincome_Att', 0.0, None, None, 0)
    coef_disposableincome_Att = Beta('coef_disposableincome_Att', 0.0, None, None, 0)

    # Habit
    coef_intercept_Habit = Beta('coef_intercept_Habit', 0.0, None, None, 0)
    coef_gender_Habit = Beta('coef_gender_Habit', 0.0, None, None, 0)
    coef_age_Habit = Beta('coef_age_Habit', 0.0, None, None, 0)
    coef_edu_Habit = Beta('coef_edu_Habit', 0.0, None, None, 0)
    coef_marriage_Habit = Beta('coef_marriage_Habit', 0.0, None, None, 0)
    coef_children_Habit = Beta('coef_children_Habit', 0.0, None, None, 0)
    coef_family_Habit = Beta('coef_family_Habit', 0.0, None, None, 0)
    coef_citytime_Habit = Beta('coef_citytime_Habit', 0.0, None, None, 0)
    coef_monthincome_Habit = Beta('coef_monthincome_Habit', 0.0, None, None, 0)
    coef_disposableincome_Habit = Beta('coef_disposableincome_Habit', 0.0, None, None, 0)

    #* measurement coefficient
    INTER_Att2 = Beta('INTER_Att2', 0, None, None, 1)
    INTER_Att3 = Beta('INTER_Att3', 0, None, None, 0)
    INTER_Att4 = Beta('INTER_Att4', 0, None, None, 0)
    INTER_Att5 = Beta('INTER_Att5', 0, None, None, 0)
    INTER_Att6 = Beta('INTER_Att6', 0, None, None, 0)

    B_Att2 = Beta('B_Att2', 1, None, None, 1)
    B_Att3 = Beta('B_Att3', 1, None, None, 0)
    B_Att4 = Beta('B_Att4', 1, None, None, 0)
    B_Att5 = Beta('B_Att5', 1, None, None, 0)
    B_Att6 = Beta('B_Att6', 1, None, None, 0)

    SIGMA_Att2 = Beta('SIGMA_Att2', 1, 1.0e-5, None, 1)
    SIGMA_Att3 = Beta('SIGMA_Att3', 1, 1.0e-5, None, 0)
    SIGMA_Att4 = Beta('SIGMA_Att4', 1, 1.0e-5, None, 0)
    SIGMA_Att5 = Beta('SIGMA_Att5', 1, 1.0e-5, None, 0)
    SIGMA_Att6 = Beta('SIGMA_Att6', 1, 1.0e-5, None, 0)

    INTER_Habit_1 = Beta('INTER_Habit_1', 0, None, None, 1)
    INTER_Habit_2 = Beta('INTER_Habit_2', 0, None, None, 0)
    INTER_Habit_3 = Beta('INTER_Habit_3', 0, None, None, 0)
    INTER_Habit_4 = Beta('INTER_Habit_4', 0, None, None, 0)
    INTER_Habit_5 = Beta('INTER_Habit_5', 0, None, None, 0)

    B_Habit1 = Beta('B_Habit1', 1, None, None, 1)
    B_Habit2 = Beta('B_Habit2', 1, None, None, 0)
    B_Habit3 = Beta('B_Habit3', 1, None, None, 0)
    B_Habit4 = Beta('B_Habit4', 1, None, None, 0)
    B_Habit5 = Beta('B_Habit5', 1, None, None, 0)

    SIGMA_Habit1 = Beta('SIGMA_Habit1', 1, 1.0e-5, None, 1)
    SIGMA_Habit2 = Beta('SIGMA_Habit2', 1, 1.0e-5, None, 0)
    SIGMA_Habit3 = Beta('SIGMA_Habit3', 1, 1.0e-5, None, 0)
    SIGMA_Habit4 = Beta('SIGMA_Habit4', 1, 1.0e-5, None, 0)
    SIGMA_Habit5 = Beta('SIGMA_Habit5', 1, 1.0e-5, None, 0)

    #* latent variables
    BETA_Habit = Beta('BETA_Habit', 0, None, None, 0)
    BETA_Att = Beta('BETA_Att', 0, None, None, 0)

    #* choice model coefficient
    BETA_Volume = Beta('BETA_Volume', 0, None, None, 0)
    BETA_Time= Beta('BETA_Time', 0, None, None, 0)
    BETA_Weather= Beta('BETA_Weather', 0, None, None, 0)
    BETA_Location = Beta('BETA_Location', 0, None, None, 0)
    BETA_RemainTime = Beta('BETA_RemainTime', 0, None, None, 0)
    BETA_CauseUnsafe = Beta('BETA_CauseUnsafe', 0, None, None, 0)

    BETA_deliVolume_low = Beta('BETA_deliVolume_low', 0, None, None, 0)
    BETA_deliVolume_high = Beta('BETA_deliVolume_high', 0, None, None, 0)

    BETA_deliTime_morning = Beta('BETA_deliTime_morning', 0, None, None, 0)
    BETA_deliTime_noon = Beta('BETA_deliTime_noon', 0, None, None, 0)
    BETA_deliTime_evening = Beta('BETA_deliTime_evening', 0, None, None, 0)
    BETA_deliTime_afternoon = Beta('BETA_deliTime_afternoon', 0, None, None, 0)
    BETA_deliTime_night = Beta('BETA_deliTime_night', 0, None, None, 0)

    BETA_deliWeather_heavy = Beta('BETA_deliWeather_heavy', 0, None, None, 0)
    BETA_deliWeather_good = Beta('BETA_deliWeather_good', 0, None, None, 0)
    BETA_deliWeather_spit = Beta('BETA_deliWeather_spit', 0, None, None, 0)

    BETA_deliLocation_inter = Beta('BETA_deliLocation_inter', 0, None, None, 0)
    BETA_deliLocation_straight = Beta('BETA_deliLocation_straight', 0, None, None, 0)
    BETA_deliLocation_curve = Beta('BETA_deliLocation_curve', 0, None, None, 0)
    BETA_deliLocation_pede = Beta('BETA_deliLocation_pede', 0, None, None, 0)

    BETA_RemainTime_short = Beta('BETA_RemainTime_short', 0, None, None, 0)

    BETA_CauseUnsafe_overspeed = Beta('BETA_CauseUnsafe_overspeed', 0, None, None, 0)
    BETA_CauseUnsafe_breakrule = Beta('BETA_CauseUnsafe_breakrule', 0, None, None, 0)
    BETA_CauseUnsafe_drowsy = Beta('BETA_CauseUnsafe_drowsy', 0, None, None, 0)
    BETA_CauseUnsafe_emergency = Beta('BETA_CauseUnsafe_emergency', 0, None, None, 0)
    BETA_CauseUnsafe_phone = Beta('BETA_CauseUnsafe_phone', 0, None, None, 0)
    BETA_CauseUnsafe_without = Beta('BETA_CauseUnsafe_without', 0, None, None, 0)

    BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)
    BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)
    # BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)
    BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)
    BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)

    BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)

    BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)
    BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)
    # BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)
    BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)
    BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)

    BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)
    BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)
    BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)
    BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)
    BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)

    BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)
    BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)
    BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)
    BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)
    BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)

    BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)
    BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)
    BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)
    BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)
    BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)

    BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)
    BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)
    BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)
    BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)
    BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)

    BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)
    BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)
    BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)
    BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)
    BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)

    BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)

    BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)
    BETA_age_between_24_35 = Beta('BETA_age_between_24_35', 0.0, None, None, 0)
    BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)

    BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)
    BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)

    BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)

    BETA_children = Beta('BETA_children', 0.0, None, None, 0)

    BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)
    BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)
    BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)

    BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)
    BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)
    BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)

    BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)
    BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)
    BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)
    BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)

    BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)
    BETA_disposableincome_more_10000 = Beta('BETA_disposableincome_more_10000', 0.0, None, None, 0)

    BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)

    #* structual equations
    sigma_1 = Beta('sigma_1', 1, None, None, 0)
    omega_1 = bioDraws('omega_1', 'NORMAL_MLHS')

    """ 
    Att = coef_intercept_Att + \
        coef_age_Att  * age_less_24 + \
        coef_gender_Att  * gender_female + \
        coef_edu_Att * edu_less_junior + \
        coef_marriage_Att * marriage_not + \
        coef_children_Att * children + \
        coef_family_Att * family_big + \
        coef_monthincome_Att * monthincome_less_4000 + \
        coef_disposableincome_Att * disposableincome_more_10000 
    """
    
    Att = coef_intercept_Att + \
        coef_age_Att  * age_less_24 + \
        coef_gender_Att  * gender_female + \
        coef_marriage_Att * marriage_not + \
        coef_children_Att * children + \
        coef_family_Att * family_big

    MODEL_Att2 = INTER_Att2 + B_Att2 * Att
    MODEL_Att3 = INTER_Att3 + B_Att3 * Att
    MODEL_Att4 = INTER_Att4 + B_Att4 * Att
    MODEL_Att5 = INTER_Att5 + B_Att5 * Att
    MODEL_Att6 = INTER_Att6 + B_Att6 * Att
    

    sigma_7 = Beta('sigma_7', 1, None, None, 0)
    omega_7 = bioDraws('omega_7', 'NORMAL_MLHS')


    """ 
    Habit = coef_intercept_Habit + \
        coef_gender_Habit  * gender_female + \
        coef_age_Habit  * age_less_24 + \
        coef_edu_Habit * edu_less_junior + \
        coef_marriage_Habit * marriage_not + \
        coef_children_Habit * children + \
        coef_family_Habit * family_big + \
        coef_monthincome_Habit * monthincome_less_4000 + \
        coef_disposableincome_Habit * disposableincome_more_10000 
    """
    Habit = coef_intercept_Habit + \
        coef_gender_Habit  * gender_female + \
        coef_marriage_Habit * marriage_not + \
        coef_children_Habit * children

    MODEL_Habit1 = INTER_Habit_1 + B_Habit1 * Habit
    MODEL_Habit2 = INTER_Habit_2 + B_Habit2 * Habit
    MODEL_Habit3 = INTER_Habit_3 + B_Habit3 * Habit
    MODEL_Habit4 = INTER_Habit_4 + B_Habit4 * Habit
    MODEL_Habit5 = INTER_Habit_5 + B_Habit5 * Habit

    # As the measurements are using a Likert scale with M = 5 levels, we deﬁne 4 parameters
    delta_1 = Beta('delta_1', 0.1, 1.0e-5, None, 0)
    delta_2 = Beta('delta_2', 0.2, 1.0e-5, None, 0)
    tau_1 = -delta_1 - delta_2
    tau_2 = -delta_1
    tau_3 = delta_1
    tau_4 = delta_1 + delta_2

    #* measurement equations
    Att2_tau_1 = (tau_1 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_2 = (tau_2 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_3 = (tau_3 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_4 = (tau_4 - MODEL_Att2) / SIGMA_Att2
    IndiAtt2 = {
        1: bioNormalCdf(Att2_tau_1),
        2: bioNormalCdf(Att2_tau_2) - bioNormalCdf(Att2_tau_1),
        3: bioNormalCdf(Att2_tau_3) - bioNormalCdf(Att2_tau_2),
        4: bioNormalCdf(Att2_tau_4) - bioNormalCdf(Att2_tau_3),
        5: 1 - bioNormalCdf(Att2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att2 = Elem(IndiAtt2, Attitude2)

    Att3_tau_1 = (tau_1 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_2 = (tau_2 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_3 = (tau_3 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_4 = (tau_4 - MODEL_Att3) / SIGMA_Att3
    IndiAtt3 = {
        1: bioNormalCdf(Att3_tau_1),
        2: bioNormalCdf(Att3_tau_2) - bioNormalCdf(Att3_tau_1),
        3: bioNormalCdf(Att3_tau_3) - bioNormalCdf(Att3_tau_2),
        4: bioNormalCdf(Att3_tau_4) - bioNormalCdf(Att3_tau_3),
        5: 1 - bioNormalCdf(Att3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att3 = Elem(IndiAtt3, Attitude3)

    Att4_tau_1 = (tau_1 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_2 = (tau_2 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_3 = (tau_3 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_4 = (tau_4 - MODEL_Att4) / SIGMA_Att4
    IndiAtt4 = {
        1: bioNormalCdf(Att4_tau_1),
        2: bioNormalCdf(Att4_tau_2) - bioNormalCdf(Att4_tau_1),
        3: bioNormalCdf(Att4_tau_3) - bioNormalCdf(Att4_tau_2),
        4: bioNormalCdf(Att4_tau_4) - bioNormalCdf(Att4_tau_3),
        5: 1 - bioNormalCdf(Att4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att4 = Elem(IndiAtt4, Attitude4)

    Att5_tau_1 = (tau_1 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_2 = (tau_2 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_3 = (tau_3 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_4 = (tau_4 - MODEL_Att5) / SIGMA_Att5
    IndiAtt5 = {
        1: bioNormalCdf(Att5_tau_1),
        2: bioNormalCdf(Att5_tau_2) - bioNormalCdf(Att5_tau_1),
        3: bioNormalCdf(Att5_tau_3) - bioNormalCdf(Att5_tau_2),
        4: bioNormalCdf(Att5_tau_4) - bioNormalCdf(Att5_tau_3),
        5: 1 - bioNormalCdf(Att5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att5 = Elem(IndiAtt5, Attitude5)

    Att6_tau_1 = (tau_1 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_2 = (tau_2 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_3 = (tau_3 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_4 = (tau_4 - MODEL_Att6) / SIGMA_Att6
    IndiAtt6 = {
        1: bioNormalCdf(Att6_tau_1),
        2: bioNormalCdf(Att6_tau_2) - bioNormalCdf(Att6_tau_1),
        3: bioNormalCdf(Att6_tau_3) - bioNormalCdf(Att6_tau_2),
        4: bioNormalCdf(Att6_tau_4) - bioNormalCdf(Att6_tau_3),
        5: 1 - bioNormalCdf(Att6_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att6 = Elem(IndiAtt6, Attitude6)


    Habit1_tau_1 = (tau_1 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_2 = (tau_2 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_3 = (tau_3 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_4 = (tau_4 - MODEL_Habit1) / SIGMA_Habit1
    IndiHabit1 = {
        1: bioNormalCdf(Habit1_tau_1),
        2: bioNormalCdf(Habit1_tau_2) - bioNormalCdf(Habit1_tau_1),
        3: bioNormalCdf(Habit1_tau_3) - bioNormalCdf(Habit1_tau_2),
        4: bioNormalCdf(Habit1_tau_4) - bioNormalCdf(Habit1_tau_3),
        5: 1 - bioNormalCdf(Habit1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit1 = Elem(IndiHabit1, Habit1)

    Habit2_tau_1 = (tau_1 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_2 = (tau_2 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_3 = (tau_3 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_4 = (tau_4 - MODEL_Habit2) / SIGMA_Habit2
    IndiHabit2 = {
        1: bioNormalCdf(Habit2_tau_1),
        2: bioNormalCdf(Habit2_tau_2) - bioNormalCdf(Habit2_tau_1),
        3: bioNormalCdf(Habit2_tau_3) - bioNormalCdf(Habit2_tau_2),
        4: bioNormalCdf(Habit2_tau_4) - bioNormalCdf(Habit2_tau_3),
        5: 1 - bioNormalCdf(Habit2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit2 = Elem(IndiHabit2, Habit2)

    Habit3_tau_1 = (tau_1 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_2 = (tau_2 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_3 = (tau_3 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_4 = (tau_4 - MODEL_Habit3) / SIGMA_Habit3
    IndiHabit3 = {
        1: bioNormalCdf(Habit3_tau_1),
        2: bioNormalCdf(Habit3_tau_2) - bioNormalCdf(Habit3_tau_1),
        3: bioNormalCdf(Habit3_tau_3) - bioNormalCdf(Habit3_tau_2),
        4: bioNormalCdf(Habit3_tau_4) - bioNormalCdf(Habit3_tau_3),
        5: 1 - bioNormalCdf(Habit3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit3 = Elem(IndiHabit3, Habit3)

    Habit4_tau_1 = (tau_1 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_2 = (tau_2 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_3 = (tau_3 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_4 = (tau_4 - MODEL_Habit4) / SIGMA_Habit4
    IndiHabit4 = {
        1: bioNormalCdf(Habit4_tau_1),
        2: bioNormalCdf(Habit4_tau_2) - bioNormalCdf(Habit4_tau_1),
        3: bioNormalCdf(Habit4_tau_3) - bioNormalCdf(Habit4_tau_2),
        4: bioNormalCdf(Habit4_tau_4) - bioNormalCdf(Habit4_tau_3),
        5: 1 - bioNormalCdf(Habit4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit4 = Elem(IndiHabit4, Habit4)

    Habit5_tau_1 = (tau_1 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_2 = (tau_2 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_3 = (tau_3 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_4 = (tau_4 - MODEL_Habit5) / SIGMA_Habit5
    IndiHabit5 = {
        1: bioNormalCdf(Habit5_tau_1),
        2: bioNormalCdf(Habit5_tau_2) - bioNormalCdf(Habit5_tau_1),
        3: bioNormalCdf(Habit5_tau_3) - bioNormalCdf(Habit5_tau_2),
        4: bioNormalCdf(Habit5_tau_4) - bioNormalCdf(Habit5_tau_3),
        5: 1 - bioNormalCdf(Habit5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit5 = Elem(IndiHabit5, Habit5)

    """ 
    V1 = BETA_intercept + \
            BETA_deliverytime_short * deliverytime_short + \
            BETA_deliverymode_full * deliverymode_full + \
            BETA_deliveryposition_extended * deliveryposition_extended + \
            BETA_workingdays_standard * workingdays_standard + \
            BETA_workinghours_brief * workinghours_brief + \
            BETA_deliveryorders_brief * deliveryorders_brief + \
            BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
            BETA_deliveryspeed_extended * deliveryspeed_extended + \
            BETA_gender_female  * gender_female + \
            BETA_age_less_24 * age_less_24 + \
            BETA_edu_less_junior * edu_less_junior + \
            BETA_marriage * marriage_not + \
            BETA_children * children + \
            BETA_family_big * family_big + \
            BETA_monthincome_less_4000 * monthincome_less_4000 + \
            BETA_disposableincome_more_10000 * disposableincome_more_10000 + \
            BETA_Habit * Habit + \
            BETA_Att * Att 
    """
    V1 = BETA_intercept + \
            BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
            BETA_workingdays_standard * workingdays_standard + \
            BETA_deliveryorders_brief * deliveryorders_brief + \
            BETA_deliveryspeed_extended * deliveryspeed_extended + \
            BETA_edu_less_junior * edu_less_junior + \
            BETA_marriage * marriage_not + \
            BETA_children * children + \
            BETA_family_big * family_big + \
            BETA_Habit * Habit + \
            BETA_Att * Att 

    V2 = 0

    # Associate utility functions with the numbering of alternatives
    V = {1: V1,
        2: V2,
        }
    
    # mode probability
    prob_accident = models.logit(V, None, 1) 
    prob_safe  = models.logit(V, None, 2)
    
    simulate = {
    'prob_accident': prob_accident,
    'prob_safe': prob_safe,
    }

    the_biogeme = bio.BIOGEME(database, simulate)
    the_biogeme.modelName = 'accident_simulation'
    results = res.bioResults(pickleFile='accident_iclv_full.pickle')

    simulated_values = the_biogeme.simulate(results.getBetaValues())

    print(simulated_values)

    betas = the_biogeme.free_beta_names()
    b = results.getBetasForSensitivityAnalysis(betas, useBootstrap=False)
    left, right = the_biogeme.confidenceIntervals(b, 0.9)

    marketShare_acc = simulated_values['prob_accident'].mean()
    marketShare_acc_left = left['prob_accident'].mean()
    marketShare_acc_right = right['prob_accident'].mean()

    marketShare_safe = simulated_values['prob_safe'].mean()
    marketShare_safe_left = left['prob_safe'].mean()
    marketShare_safe_right = right['prob_safe'].mean()

    print(
    f'For {var}:'
    f'Market share for accident: {100*marketShare_acc:.1f}% '
    f'[{100*marketShare_acc_left:.1f}%, '
    f'{100*marketShare_acc_right:.1f}%]'
    )

    print(
    f'Market share for safe: {100*marketShare_safe:.1f}% '
    f'[{100*marketShare_safe_left:.1f}%, '
    f'{100*marketShare_safe_right:.1f}%]'
    )

    return [marketShare_acc, marketShare_acc_left, marketShare_acc_right], [marketShare_safe,marketShare_safe_left,marketShare_safe_right ]
    

import biogeme.results as res

def margin_effect_0(df, index, var):
    
    database = db.Database('data'+var, df)
    globals().update(database.variables)

    # 场景属性自变量
    deliVolume = Variable('Volume')
    deliTime = Variable('Time')
    deliWeather = Variable('Weather')
    deliLocation = Variable('Location')
    RemainTime = Variable('RemainTime')
    CauseUnsafe = Variable('CauseUnsafe')

    # 因变量
    UnsafeAccident = Variable('UnsafeAccident')

    # 社会经济属性自变量
    gender = Variable('gender')
    age = Variable('age')
    education = Variable('education')
    marriage = Variable('marriage')
    family = Variable('family')
    citytime = Variable('citytime')
    monthincome = Variable('monthincome')
    disposableincome = Variable('disposableincome')

    # 配送属性自变量
    deliverytime = Variable('deliverytime')
    deliverymode = Variable('deliverymode')
    deliveryposition = Variable('deliveryposition')
    workingdays = Variable('workingdays')
    workinghours = Variable('workinghours')
    deliveryorders = Variable('deliveryorders')
    deliveryrange = Variable('deliveryrange')
    deliveryspeed = Variable('deliveryspeed')

    # 社会经济属性二分类变量
    if index == 1:
        gender_female = 0
    else:
        gender_female = database.DefineVariable('gender_female'+var, gender == 2)

    if index == 2: 
        age_less_24 = 0
    else:
        age_less_24 = database.DefineVariable('age_less_24'+var, age <= 2) 
        
    if index == 3: 
        edu_less_junior = 0
    else:
        edu_less_junior = database.DefineVariable('edu_less_junior'+var, education <= 2)
    if index == 4: 
        marriage_not = 0
    else:
        marriage_not = database.DefineVariable('marriage_not'+var, marriage <= 1) 
    if index == 5: 
        children = 0
    else:
        children = database.DefineVariable('children'+var, ((marriage == 3) + (marriage == 4) + (marriage == 6) > 0)) 
    if index == 6:
        family_big = 0
    else:
        family_big = database.DefineVariable('family_big'+var, (family >= 5) &  (family <= 7)) 

    monthincome_less_4000 = database.DefineVariable('monthincome_less_4000'+var, monthincome <= 2) 
    disposableincome_more_10000 = database.DefineVariable('disposableincome_more_10000'+var, disposableincome >= 3) 

    # 配送属性二分类变量
    deliverytime_short = database.DefineVariable('deliverytime_short'+var, deliverytime <= 2) 
    deliverymode_full = database.DefineVariable('deliverymode_full'+var, deliverymode >= 2) 
    deliveryposition_extended = database.DefineVariable('deliveryposition_extended'+var, deliveryposition >= 3) 
    workinghours_brief = database.DefineVariable('workinghours_brief'+var, workinghours <= 1) 
    if index == 7:
        workingdays_standard = 0
    else:
        workingdays_standard = database.DefineVariable('workingdays_standard'+var, workingdays <= 3) 
    if index == 8:  
        deliveryorders_brief = 0
    else:
        deliveryorders_brief = database.DefineVariable('deliveryorders_brief'+var, deliveryorders <= 1)
    if index == 9: 
        deliveryrange_prolonged = 0
    else:
        deliveryrange_prolonged = database.DefineVariable('deliveryrange_prolonged'+var, deliveryrange >= 5)
    if index == 10:
        deliveryspeed_extended = 0
    else:
        deliveryspeed_extended = database.DefineVariable('deliveryspeed_extended'+var, deliveryspeed == 4) 

    #* structual coefficient

    # Attitude
    coef_intercept_Att = Beta('coef_intercept_Att', 0.0, None, None, 0)
    coef_gender_Att = Beta('coef_gender_Att', 0.0, None, None, 0)
    coef_age_Att = Beta('coef_age_Att', 0.0, None, None, 0)
    coef_edu_Att = Beta('coef_edu_Att', 0.0, None, None, 0)
    coef_marriage_Att = Beta('coef_marriage_Att', 0.0, None, None, 0)
    coef_children_Att = Beta('coef_children_Att', 0.0, None, None, 0)
    coef_family_Att = Beta('coef_family_Att', 0.0, None, None, 0)
    coef_citytime_Att = Beta('coef_citytime_Att', 0.0, None, None, 0)
    coef_monthincome_Att = Beta('coef_monthincome_Att', 0.0, None, None, 0)
    coef_disposableincome_Att = Beta('coef_disposableincome_Att', 0.0, None, None, 0)

    # Habit
    coef_intercept_Habit = Beta('coef_intercept_Habit', 0.0, None, None, 0)
    coef_gender_Habit = Beta('coef_gender_Habit', 0.0, None, None, 0)
    coef_age_Habit = Beta('coef_age_Habit', 0.0, None, None, 0)
    coef_edu_Habit = Beta('coef_edu_Habit', 0.0, None, None, 0)
    coef_marriage_Habit = Beta('coef_marriage_Habit', 0.0, None, None, 0)
    coef_children_Habit = Beta('coef_children_Habit', 0.0, None, None, 0)
    coef_family_Habit = Beta('coef_family_Habit', 0.0, None, None, 0)
    coef_citytime_Habit = Beta('coef_citytime_Habit', 0.0, None, None, 0)
    coef_monthincome_Habit = Beta('coef_monthincome_Habit', 0.0, None, None, 0)
    coef_disposableincome_Habit = Beta('coef_disposableincome_Habit', 0.0, None, None, 0)

    #* measurement coefficient
    INTER_Att2 = Beta('INTER_Att2', 0, None, None, 1)
    INTER_Att3 = Beta('INTER_Att3', 0, None, None, 0)
    INTER_Att4 = Beta('INTER_Att4', 0, None, None, 0)
    INTER_Att5 = Beta('INTER_Att5', 0, None, None, 0)
    INTER_Att6 = Beta('INTER_Att6', 0, None, None, 0)

    B_Att2 = Beta('B_Att2', 1, None, None, 1)
    B_Att3 = Beta('B_Att3', 1, None, None, 0)
    B_Att4 = Beta('B_Att4', 1, None, None, 0)
    B_Att5 = Beta('B_Att5', 1, None, None, 0)
    B_Att6 = Beta('B_Att6', 1, None, None, 0)

    SIGMA_Att2 = Beta('SIGMA_Att2', 1, 1.0e-5, None, 1)
    SIGMA_Att3 = Beta('SIGMA_Att3', 1, 1.0e-5, None, 0)
    SIGMA_Att4 = Beta('SIGMA_Att4', 1, 1.0e-5, None, 0)
    SIGMA_Att5 = Beta('SIGMA_Att5', 1, 1.0e-5, None, 0)
    SIGMA_Att6 = Beta('SIGMA_Att6', 1, 1.0e-5, None, 0)

    INTER_Habit_1 = Beta('INTER_Habit_1', 0, None, None, 1)
    INTER_Habit_2 = Beta('INTER_Habit_2', 0, None, None, 0)
    INTER_Habit_3 = Beta('INTER_Habit_3', 0, None, None, 0)
    INTER_Habit_4 = Beta('INTER_Habit_4', 0, None, None, 0)
    INTER_Habit_5 = Beta('INTER_Habit_5', 0, None, None, 0)

    B_Habit1 = Beta('B_Habit1', 1, None, None, 1)
    B_Habit2 = Beta('B_Habit2', 1, None, None, 0)
    B_Habit3 = Beta('B_Habit3', 1, None, None, 0)
    B_Habit4 = Beta('B_Habit4', 1, None, None, 0)
    B_Habit5 = Beta('B_Habit5', 1, None, None, 0)

    SIGMA_Habit1 = Beta('SIGMA_Habit1', 1, 1.0e-5, None, 1)
    SIGMA_Habit2 = Beta('SIGMA_Habit2', 1, 1.0e-5, None, 0)
    SIGMA_Habit3 = Beta('SIGMA_Habit3', 1, 1.0e-5, None, 0)
    SIGMA_Habit4 = Beta('SIGMA_Habit4', 1, 1.0e-5, None, 0)
    SIGMA_Habit5 = Beta('SIGMA_Habit5', 1, 1.0e-5, None, 0)

    #* latent variables
    BETA_Habit = Beta('BETA_Habit', 0, None, None, 0)
    BETA_Att = Beta('BETA_Att', 0, None, None, 0)

    #* choice model coefficient
    BETA_Volume = Beta('BETA_Volume', 0, None, None, 0)
    BETA_Time= Beta('BETA_Time', 0, None, None, 0)
    BETA_Weather= Beta('BETA_Weather', 0, None, None, 0)
    BETA_Location = Beta('BETA_Location', 0, None, None, 0)
    BETA_RemainTime = Beta('BETA_RemainTime', 0, None, None, 0)
    BETA_CauseUnsafe = Beta('BETA_CauseUnsafe', 0, None, None, 0)

    BETA_deliVolume_low = Beta('BETA_deliVolume_low', 0, None, None, 0)
    BETA_deliVolume_high = Beta('BETA_deliVolume_high', 0, None, None, 0)

    BETA_deliTime_morning = Beta('BETA_deliTime_morning', 0, None, None, 0)
    BETA_deliTime_noon = Beta('BETA_deliTime_noon', 0, None, None, 0)
    BETA_deliTime_evening = Beta('BETA_deliTime_evening', 0, None, None, 0)
    BETA_deliTime_afternoon = Beta('BETA_deliTime_afternoon', 0, None, None, 0)
    BETA_deliTime_night = Beta('BETA_deliTime_night', 0, None, None, 0)

    BETA_deliWeather_heavy = Beta('BETA_deliWeather_heavy', 0, None, None, 0)
    BETA_deliWeather_good = Beta('BETA_deliWeather_good', 0, None, None, 0)
    BETA_deliWeather_spit = Beta('BETA_deliWeather_spit', 0, None, None, 0)

    BETA_deliLocation_inter = Beta('BETA_deliLocation_inter', 0, None, None, 0)
    BETA_deliLocation_straight = Beta('BETA_deliLocation_straight', 0, None, None, 0)
    BETA_deliLocation_curve = Beta('BETA_deliLocation_curve', 0, None, None, 0)
    BETA_deliLocation_pede = Beta('BETA_deliLocation_pede', 0, None, None, 0)

    BETA_RemainTime_short = Beta('BETA_RemainTime_short', 0, None, None, 0)

    BETA_CauseUnsafe_overspeed = Beta('BETA_CauseUnsafe_overspeed', 0, None, None, 0)
    BETA_CauseUnsafe_breakrule = Beta('BETA_CauseUnsafe_breakrule', 0, None, None, 0)
    BETA_CauseUnsafe_drowsy = Beta('BETA_CauseUnsafe_drowsy', 0, None, None, 0)
    BETA_CauseUnsafe_emergency = Beta('BETA_CauseUnsafe_emergency', 0, None, None, 0)
    BETA_CauseUnsafe_phone = Beta('BETA_CauseUnsafe_phone', 0, None, None, 0)
    BETA_CauseUnsafe_without = Beta('BETA_CauseUnsafe_without', 0, None, None, 0)

    BETA_deliverytime_brief = Beta('BETA_deliverytime_brief', 0, None, None, 0)
    BETA_deliverytime_short = Beta('BETA_deliverytime_short', 0, None, None, 0)
    # BETA_deliverytime_standard = Beta('BETA_deliverytime_standard', 0, None, None, 0)
    BETA_deliverytime_extended = Beta('BETA_deliverytime_extended', 0, None, None, 0)
    BETA_deliverytime_prolonged = Beta('BETA_deliverytime_prolonged', 0, None, None, 0)

    BETA_deliverymode_full = Beta('BETA_deliverymode_full', 0, None, None, 0)

    BETA_deliveryposition_brief = Beta('BETA_deliveryposition_brief', 0, None, None, 0)
    BETA_deliveryposition_short = Beta('BETA_deliveryposition_short', 0, None, None, 0)
    # BETA_deliveryposition_standard = Beta('BETA_deliveryposition_standard', 0, None, None, 0)
    BETA_deliveryposition_extended = Beta('BETA_deliveryposition_extended', 0, None, None, 0)
    BETA_deliveryposition_prolonged = Beta('BETA_deliveryposition_prolonged', 0, None, None, 0)

    BETA_workingdays_brief = Beta('BETA_workingdays_brief', 0, None, None, 0)
    BETA_workingdays_short = Beta('BETA_workingdays_short', 0, None, None, 0)
    BETA_workingdays_standard = Beta('BETA_workingdays_standard', 0, None, None, 0)
    BETA_workingdays_extended = Beta('BETA_workingdays_extended', 0, None, None, 0)
    BETA_workingdays_prolonged = Beta('BETA_workingdays_prolonged', 0, None, None, 0)

    BETA_workinghours_brief = Beta('BETA_workinghours_brief', 0, None, None, 0)
    BETA_workinghours_short = Beta('BETA_workinghours_short', 0, None, None, 0)
    BETA_workinghours_standard = Beta('BETA_workinghours_standard', 0, None, None, 0)
    BETA_workinghours_extended = Beta('BETA_workinghours_extended', 0, None, None, 0)
    BETA_workinghours_prolonged = Beta('BETA_workinghours_prolonged', 0, None, None, 0)

    BETA_deliveryorders_brief = Beta('BETA_deliveryorders_brief', 0, None, None, 0)
    BETA_deliveryorders_short = Beta('BETA_deliveryorders_short', 0, None, None, 0)
    BETA_deliveryorders_standard = Beta('BETA_deliveryorders_standard', 0, None, None, 0)
    BETA_deliveryorders_extended = Beta('BETA_deliveryorders_extended', 0, None, None, 0)
    BETA_deliveryorders_prolonged = Beta('BETA_deliveryorders_prolonged', 0, None, None, 0)

    BETA_deliveryrange_brief = Beta('BETA_deliveryrange_brief', 0, None, None, 0)
    BETA_deliveryrange_short = Beta('BETA_deliveryrange_short', 0, None, None, 0)
    BETA_deliveryrange_standard = Beta('BETA_deliveryrange_standard', 0, None, None, 0)
    BETA_deliveryrange_extended = Beta('BETA_deliveryrange_extended', 0, None, None, 0)
    BETA_deliveryrange_prolonged = Beta('BETA_deliveryrange_prolonged', 0, None, None, 0)

    BETA_deliveryspeed_brief = Beta('BETA_deliveryspeed_brief', 0, None, None, 0)
    BETA_deliveryspeed_short = Beta('BETA_deliveryspeed_short', 0, None, None, 0)
    BETA_deliveryspeed_standard = Beta('BETA_deliveryspeed_standard', 0, None, None, 0)
    BETA_deliveryspeed_extended = Beta('BETA_deliveryspeed_extended', 0, None, None, 0)
    BETA_deliveryspeed_prolonged = Beta('BETA_deliveryspeed_prolonged', 0, None, None, 0)

    BETA_gender_female = Beta('BETA_gender_female', 0.0, None, None, 0)

    BETA_age_less_24 = Beta('BETA_age_less_24', 0.0, None, None, 0)
    BETA_age_between_24_35 = Beta('BETA_age_between_24_35', 0.0, None, None, 0)
    BETA_age_more_35 = Beta('BETA_age_more_35', 0.0, None, None, 0)

    BETA_edu_less_junior = Beta('BETA_edu_less_junior', 0.0, None, None, 0)
    BETA_edu_more_uni = Beta('BETA_edu_more_uni', 0.0, None, None, 0)

    BETA_marriage = Beta('BETA_marriage', 0.0, None, None, 0)

    BETA_children = Beta('BETA_children', 0.0, None, None, 0)

    BETA_family_small = Beta('BETA_family_small', 0.0, None, None, 0)
    BETA_family_middle = Beta('BETA_family_middle', 0.0, None, None, 0)
    BETA_family_big = Beta('BETA_family_big', 0.0, None, None, 0)

    BETA_citytime_less_3 = Beta('BETA_citytime_less_3', 0.0, None, None, 0)
    BETA_citytime_more_6 = Beta('BETA_citytime_more_6', 0.0, None, None, 0)
    BETA_citytime_local = Beta('BETA_citytime_local', 0.0, None, None, 0)

    BETA_monthincome_less_2000 = Beta('BETA_monthincome_less_2000', 0.0, None, None, 0)
    BETA_monthincome_less_4000 = Beta('BETA_monthincome_less_4000', 0.0, None, None, 0)
    BETA_monthincome_less_6000 = Beta('BETA_monthincome_less_6000', 0.0, None, None, 0)
    BETA_monthincome_more_8000 = Beta('BETA_monthincome_more_8000', 0.0, None, None, 0)

    BETA_disposableincome_less_5000 = Beta('BETA_disposableincome_less_5000', 0.0, None, None, 0)
    BETA_disposableincome_more_10000 = Beta('BETA_disposableincome_more_10000', 0.0, None, None, 0)

    BETA_intercept= Beta('BETA_intercept', 0.0, None, None, 0)

    #* structual equations
    sigma_1 = Beta('sigma_1', 1, None, None, 0)
    omega_1 = bioDraws('omega_1', 'NORMAL_MLHS')

    """ 
    Att = coef_intercept_Att + \
        coef_age_Att  * age_less_24 + \
        coef_gender_Att  * gender_female + \
        coef_edu_Att * edu_less_junior + \
        coef_marriage_Att * marriage_not + \
        coef_children_Att * children + \
        coef_family_Att * family_big + \
        coef_monthincome_Att * monthincome_less_4000 + \
        coef_disposableincome_Att * disposableincome_more_10000 
    """
    
    Att = coef_intercept_Att + \
        coef_age_Att  * age_less_24 + \
        coef_gender_Att  * gender_female + \
        coef_marriage_Att * marriage_not + \
        coef_children_Att * children + \
        coef_family_Att * family_big

    MODEL_Att2 = INTER_Att2 + B_Att2 * Att
    MODEL_Att3 = INTER_Att3 + B_Att3 * Att
    MODEL_Att4 = INTER_Att4 + B_Att4 * Att
    MODEL_Att5 = INTER_Att5 + B_Att5 * Att
    MODEL_Att6 = INTER_Att6 + B_Att6 * Att
    

    sigma_7 = Beta('sigma_7', 1, None, None, 0)
    omega_7 = bioDraws('omega_7', 'NORMAL_MLHS')

    """ 
    Habit = coef_intercept_Habit + \
        coef_gender_Habit  * gender_female + \
        coef_age_Habit  * age_less_24 + \
        coef_edu_Habit * edu_less_junior + \
        coef_marriage_Habit * marriage_not + \
        coef_children_Habit * children + \
        coef_family_Habit * family_big + \
        coef_monthincome_Habit * monthincome_less_4000 + \
        coef_disposableincome_Habit * disposableincome_more_10000 
    """
    Habit = coef_intercept_Habit + \
        coef_gender_Habit  * gender_female + \
        coef_marriage_Habit * marriage_not + \
        coef_children_Habit * children

    MODEL_Habit1 = INTER_Habit_1 + B_Habit1 * Habit
    MODEL_Habit2 = INTER_Habit_2 + B_Habit2 * Habit
    MODEL_Habit3 = INTER_Habit_3 + B_Habit3 * Habit
    MODEL_Habit4 = INTER_Habit_4 + B_Habit4 * Habit
    MODEL_Habit5 = INTER_Habit_5 + B_Habit5 * Habit

    # As the measurements are using a Likert scale with M = 5 levels, we deﬁne 4 parameters
    delta_1 = Beta('delta_1', 0.1, 1.0e-5, None, 0)
    delta_2 = Beta('delta_2', 0.2, 1.0e-5, None, 0)
    tau_1 = -delta_1 - delta_2
    tau_2 = -delta_1
    tau_3 = delta_1
    tau_4 = delta_1 + delta_2

    #* measurement equations
    Att2_tau_1 = (tau_1 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_2 = (tau_2 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_3 = (tau_3 - MODEL_Att2) / SIGMA_Att2
    Att2_tau_4 = (tau_4 - MODEL_Att2) / SIGMA_Att2
    IndiAtt2 = {
        1: bioNormalCdf(Att2_tau_1),
        2: bioNormalCdf(Att2_tau_2) - bioNormalCdf(Att2_tau_1),
        3: bioNormalCdf(Att2_tau_3) - bioNormalCdf(Att2_tau_2),
        4: bioNormalCdf(Att2_tau_4) - bioNormalCdf(Att2_tau_3),
        5: 1 - bioNormalCdf(Att2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att2 = Elem(IndiAtt2, Attitude2)

    Att3_tau_1 = (tau_1 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_2 = (tau_2 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_3 = (tau_3 - MODEL_Att3) / SIGMA_Att3
    Att3_tau_4 = (tau_4 - MODEL_Att3) / SIGMA_Att3
    IndiAtt3 = {
        1: bioNormalCdf(Att3_tau_1),
        2: bioNormalCdf(Att3_tau_2) - bioNormalCdf(Att3_tau_1),
        3: bioNormalCdf(Att3_tau_3) - bioNormalCdf(Att3_tau_2),
        4: bioNormalCdf(Att3_tau_4) - bioNormalCdf(Att3_tau_3),
        5: 1 - bioNormalCdf(Att3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att3 = Elem(IndiAtt3, Attitude3)

    Att4_tau_1 = (tau_1 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_2 = (tau_2 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_3 = (tau_3 - MODEL_Att4) / SIGMA_Att4
    Att4_tau_4 = (tau_4 - MODEL_Att4) / SIGMA_Att4
    IndiAtt4 = {
        1: bioNormalCdf(Att4_tau_1),
        2: bioNormalCdf(Att4_tau_2) - bioNormalCdf(Att4_tau_1),
        3: bioNormalCdf(Att4_tau_3) - bioNormalCdf(Att4_tau_2),
        4: bioNormalCdf(Att4_tau_4) - bioNormalCdf(Att4_tau_3),
        5: 1 - bioNormalCdf(Att4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att4 = Elem(IndiAtt4, Attitude4)

    Att5_tau_1 = (tau_1 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_2 = (tau_2 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_3 = (tau_3 - MODEL_Att5) / SIGMA_Att5
    Att5_tau_4 = (tau_4 - MODEL_Att5) / SIGMA_Att5
    IndiAtt5 = {
        1: bioNormalCdf(Att5_tau_1),
        2: bioNormalCdf(Att5_tau_2) - bioNormalCdf(Att5_tau_1),
        3: bioNormalCdf(Att5_tau_3) - bioNormalCdf(Att5_tau_2),
        4: bioNormalCdf(Att5_tau_4) - bioNormalCdf(Att5_tau_3),
        5: 1 - bioNormalCdf(Att5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att5 = Elem(IndiAtt5, Attitude5)

    Att6_tau_1 = (tau_1 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_2 = (tau_2 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_3 = (tau_3 - MODEL_Att6) / SIGMA_Att6
    Att6_tau_4 = (tau_4 - MODEL_Att6) / SIGMA_Att6
    IndiAtt6 = {
        1: bioNormalCdf(Att6_tau_1),
        2: bioNormalCdf(Att6_tau_2) - bioNormalCdf(Att6_tau_1),
        3: bioNormalCdf(Att6_tau_3) - bioNormalCdf(Att6_tau_2),
        4: bioNormalCdf(Att6_tau_4) - bioNormalCdf(Att6_tau_3),
        5: 1 - bioNormalCdf(Att6_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Att6 = Elem(IndiAtt6, Attitude6)


    Habit1_tau_1 = (tau_1 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_2 = (tau_2 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_3 = (tau_3 - MODEL_Habit1) / SIGMA_Habit1
    Habit1_tau_4 = (tau_4 - MODEL_Habit1) / SIGMA_Habit1
    IndiHabit1 = {
        1: bioNormalCdf(Habit1_tau_1),
        2: bioNormalCdf(Habit1_tau_2) - bioNormalCdf(Habit1_tau_1),
        3: bioNormalCdf(Habit1_tau_3) - bioNormalCdf(Habit1_tau_2),
        4: bioNormalCdf(Habit1_tau_4) - bioNormalCdf(Habit1_tau_3),
        5: 1 - bioNormalCdf(Habit1_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit1 = Elem(IndiHabit1, Habit1)

    Habit2_tau_1 = (tau_1 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_2 = (tau_2 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_3 = (tau_3 - MODEL_Habit2) / SIGMA_Habit2
    Habit2_tau_4 = (tau_4 - MODEL_Habit2) / SIGMA_Habit2
    IndiHabit2 = {
        1: bioNormalCdf(Habit2_tau_1),
        2: bioNormalCdf(Habit2_tau_2) - bioNormalCdf(Habit2_tau_1),
        3: bioNormalCdf(Habit2_tau_3) - bioNormalCdf(Habit2_tau_2),
        4: bioNormalCdf(Habit2_tau_4) - bioNormalCdf(Habit2_tau_3),
        5: 1 - bioNormalCdf(Habit2_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit2 = Elem(IndiHabit2, Habit2)

    Habit3_tau_1 = (tau_1 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_2 = (tau_2 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_3 = (tau_3 - MODEL_Habit3) / SIGMA_Habit3
    Habit3_tau_4 = (tau_4 - MODEL_Habit3) / SIGMA_Habit3
    IndiHabit3 = {
        1: bioNormalCdf(Habit3_tau_1),
        2: bioNormalCdf(Habit3_tau_2) - bioNormalCdf(Habit3_tau_1),
        3: bioNormalCdf(Habit3_tau_3) - bioNormalCdf(Habit3_tau_2),
        4: bioNormalCdf(Habit3_tau_4) - bioNormalCdf(Habit3_tau_3),
        5: 1 - bioNormalCdf(Habit3_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit3 = Elem(IndiHabit3, Habit3)

    Habit4_tau_1 = (tau_1 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_2 = (tau_2 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_3 = (tau_3 - MODEL_Habit4) / SIGMA_Habit4
    Habit4_tau_4 = (tau_4 - MODEL_Habit4) / SIGMA_Habit4
    IndiHabit4 = {
        1: bioNormalCdf(Habit4_tau_1),
        2: bioNormalCdf(Habit4_tau_2) - bioNormalCdf(Habit4_tau_1),
        3: bioNormalCdf(Habit4_tau_3) - bioNormalCdf(Habit4_tau_2),
        4: bioNormalCdf(Habit4_tau_4) - bioNormalCdf(Habit4_tau_3),
        5: 1 - bioNormalCdf(Habit4_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit4 = Elem(IndiHabit4, Habit4)

    Habit5_tau_1 = (tau_1 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_2 = (tau_2 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_3 = (tau_3 - MODEL_Habit5) / SIGMA_Habit5
    Habit5_tau_4 = (tau_4 - MODEL_Habit5) / SIGMA_Habit5
    IndiHabit5 = {
        1: bioNormalCdf(Habit5_tau_1),
        2: bioNormalCdf(Habit5_tau_2) - bioNormalCdf(Habit5_tau_1),
        3: bioNormalCdf(Habit5_tau_3) - bioNormalCdf(Habit5_tau_2),
        4: bioNormalCdf(Habit5_tau_4) - bioNormalCdf(Habit5_tau_3),
        5: 1 - bioNormalCdf(Habit5_tau_4),
        6: 1.0,
        -1: 1.0,
        -2: 1.0}

    P_Habit5 = Elem(IndiHabit5, Habit5)

    """ 
    V1 = BETA_intercept + \
            BETA_deliverytime_short * deliverytime_short + \
            BETA_deliverymode_full * deliverymode_full + \
            BETA_deliveryposition_extended * deliveryposition_extended + \
            BETA_workingdays_standard * workingdays_standard + \
            BETA_workinghours_brief * workinghours_brief + \
            BETA_deliveryorders_brief * deliveryorders_brief + \
            BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
            BETA_deliveryspeed_extended * deliveryspeed_extended + \
            BETA_gender_female  * gender_female + \
            BETA_age_less_24 * age_less_24 + \
            BETA_edu_less_junior * edu_less_junior + \
            BETA_marriage * marriage_not + \
            BETA_children * children + \
            BETA_family_big * family_big + \
            BETA_monthincome_less_4000 * monthincome_less_4000 + \
            BETA_disposableincome_more_10000 * disposableincome_more_10000 + \
            BETA_Habit * Habit + \
            BETA_Att * Att 
    """
    V1 = BETA_intercept + \
            BETA_deliveryrange_prolonged * deliveryrange_prolonged + \
            BETA_workingdays_standard * workingdays_standard + \
            BETA_deliveryorders_brief * deliveryorders_brief + \
            BETA_deliveryspeed_extended * deliveryspeed_extended + \
            BETA_edu_less_junior * edu_less_junior + \
            BETA_marriage * marriage_not + \
            BETA_children * children + \
            BETA_family_big * family_big + \
            BETA_Habit * Habit + \
            BETA_Att * Att 

    V2 = 0

    # Associate utility functions with the numbering of alternatives
    V = {1: V1,
        2: V2,
        }
    
    # mode probability
    prob_accident = models.logit(V, None, 1) 
    prob_safe  = models.logit(V, None, 2)
    
    simulate = {
    'prob_accident': prob_accident,
    'prob_safe': prob_safe,
    }

    the_biogeme = bio.BIOGEME(database, simulate)
    the_biogeme.modelName = 'accident_simulation'
    results = res.bioResults(pickleFile='accident_iclv_full.pickle')

    simulated_values = the_biogeme.simulate(results.getBetaValues())

    print(simulated_values)

    betas = the_biogeme.free_beta_names()
    b = results.getBetasForSensitivityAnalysis(betas, useBootstrap=False)
    left, right = the_biogeme.confidenceIntervals(b, 0.9)

    marketShare_acc = simulated_values['prob_accident'].mean()
    marketShare_acc_left = left['prob_accident'].mean()
    marketShare_acc_right = right['prob_accident'].mean()

    marketShare_safe = simulated_values['prob_safe'].mean()
    marketShare_safe_left = left['prob_safe'].mean()
    marketShare_safe_right = right['prob_safe'].mean()

    print(
    f'For {var}:'
    f'Market share for accident: {100*marketShare_acc:.1f}% '
    f'[{100*marketShare_acc_left:.1f}%, '
    f'{100*marketShare_acc_right:.1f}%]'
    )

    print(
    f'Market share for safe: {100*marketShare_safe:.1f}% '
    f'[{100*marketShare_safe_left:.1f}%, '
    f'{100*marketShare_safe_right:.1f}%]'
    )

    return [marketShare_acc, marketShare_acc_left, marketShare_acc_right], [marketShare_safe,marketShare_safe_left,marketShare_safe_right ]
    


# 创建一个空的 DataFrame
columns = ['var', 'marketShare_acc', 'marketShare_acc_left', 'marketShare_acc_right', 'marketShare_safe', 'marketShare_safe_left', 'marketShare_safe_right']
result_df = pd.DataFrame(columns=columns)

dforigin = pd.read_excel('new_412.xlsx')
df = dforigin[dforigin['QeTime'] >= 120]
df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]

all_vars = ['ori', 'gender', 'age', 'edu', 'marital', 'parental', 'family', 'days', 'orders', 'mileage', 'speed']

for index, var in enumerate(all_vars):
    origin1, origin2 = margin_effect(df, index, var)
    # print(origin1, origin2)

    # 创建一个新的 DataFrame 行
    new_row = pd.DataFrame([[var] + origin1 + origin2], columns=columns)
    
    # 将新行添加到结果 DataFrame 中
    result_df = pd.concat([result_df, new_row], ignore_index=True)


# 输出结果
import copy
result_df_p1 = copy.deepcopy(result_df)

import matplotlib.pyplot as plt
from scipy import stats
from matplotlib import ticker


plt.rcParams["font.family"] = "Times New Roman"
plt.rcParams["axes.labelsize"] = 14
plt.rcParams["xtick.labelsize"] = 14
plt.rcParams["ytick.labelsize"] = 14
plt.rcParams["axes.titlesize"] = 14
plt.rcParams["figure.titlesize"] = 14
plt.rcParams["axes.titlepad"] = 14

# 只考虑社会因素
result_df = result_df.head(-4)

# 计算误差棒
result_df['error_lower'] = result_df['marketShare_acc'] - result_df['marketShare_acc_left']
result_df['error_upper'] = result_df['marketShare_acc_right'] - result_df['marketShare_acc']

# 更换x轴标签并指定颜色
colors = ['#BFBCDA' if var == 'ori' else '#99DAEE' for var in result_df['var']]

# 绘制柱状图
plt.figure(figsize=(9, 6), dpi = 300)
plt.bar(result_df['var'], result_df['marketShare_acc'], yerr=[result_df['error_lower'], result_df['error_upper']], capsize=5, color=colors, alpha=0.9)
# plt.xlabel('Variable')
plt.ylabel('Accident probability')
# plt.title('Market Share Acc by Variable with Error Bars')
# custom_labels = ['Base', 
#                      'Gender',
#                      'Age',
#                      'Education\nlevel',
#                      'Marital\nstatus',
#                      'Parental\nstatus', 
#                      'Family\nsize',
#                      'Working\ndays',
#                      'Delivery\norders',
#                      'Delivery\nmileage',
#                      'Driving\nspeed',]
custom_labels = ['Base', 
                     'Gender',
                     'Age',
                     'Education\nlevel',
                     'Marital\nstatus',
                     'Parental\nstatus', 
                     'Family\nsize',]

plt.xticks(ticks=range(len(custom_labels)), 
           labels = custom_labels
                     )
plt.ylim(0, 0.7)
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.tight_layout()
plt.gca().yaxis.set_major_formatter(ticker.PercentFormatter(xmax=1, decimals=0))


# 显示图像
plt.savefig('Marginal effect.pdf', bbox_inches='tight')


# 创建一个空的 DataFrame
columns = ['var', 'marketShare_acc', 'marketShare_acc_left', 'marketShare_acc_right', 'marketShare_safe', 'marketShare_safe_left', 'marketShare_safe_right']
result_df_0 = pd.DataFrame(columns=columns)

dforigin = pd.read_excel('new_412.xlsx')
df = dforigin[dforigin['QeTime'] >= 120]
df = df[~((df['Attitude1'] == df['Attitude6']) & (df['Attitude1'] != 3))]

all_vars = ['ori', 'gender','age', 'edu', 'marital', 'parental', 'family', 'days', 'orders', 'mileage', 'speed']

for index, var in enumerate(all_vars):
    origin1, origin2 = margin_effect_0(df, index, var)
    # print(origin1, origin2)

    # 创建一个新的 DataFrame 行
    new_row = pd.DataFrame([[var] + origin1 + origin2], columns=columns)
    
    # 将新行添加到结果 DataFrame 中
    result_df_0 = pd.concat([result_df_0, new_row], ignore_index=True)


import copy
result_df_p2 = copy.deepcopy(result_df_0)

import matplotlib.pyplot as plt
from scipy import stats
from matplotlib import ticker

plt.rcParams["font.family"] = "Times New Roman"
plt.rcParams["axes.labelsize"] = 14
plt.rcParams["xtick.labelsize"] = 14
plt.rcParams["ytick.labelsize"] = 14
plt.rcParams["axes.titlesize"] = 14
plt.rcParams["figure.titlesize"] = 14
plt.rcParams["axes.titlepad"] = 14

# 只考虑社会因素
result_df_0 = result_df_0.head(-4)

# 计算误差棒
result_df_0['error_lower'] = result_df_0['marketShare_acc'] - result_df_0['marketShare_acc_left']
result_df_0['error_upper'] = result_df_0['marketShare_acc_right'] - result_df_0['marketShare_acc']

# 更换x轴标签并指定颜色
colors = ['#BFBCDA' if var == 'ori' else '#99DAEE' for var in result_df_0['var']]

# 绘制柱状图
plt.figure(figsize=(9, 6), dpi = 300)
plt.bar(result_df_0['var'], result_df_0['marketShare_acc'], yerr=[result_df_0['error_lower'], result_df_0['error_upper']], capsize=5, color=colors, alpha=0.9)
# plt.xlabel('Variable')
plt.ylabel('Accident probability')
# plt.title('Market Share Acc by Variable with Error Bars')
# custom_labels = ['Base', 
#                      'Gender',
#                      'Age',
#                      'Education\nlevel',
#                      'Marital\nstatus',
#                      'Parental\nstatus', 
#                      'Family\nsize',
#                      'Working\ndays',
#                      'Delivery\norders',
#                      'Delivery\nmileage',
#                      'Driving\nspeed',]
custom_labels = ['Base', 
                     'Gender',
                     'Age',
                     'Education\nlevel',
                     'Marital\nstatus',
                     'Parental\nstatus', 
                     'Family\nsize',]
plt.xticks(ticks=range(len(custom_labels)), 
           labels = custom_labels
                     )
plt.ylim(0, 0.7)
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.tight_layout()
plt.gca().yaxis.set_major_formatter(ticker.PercentFormatter(xmax=1, decimals=0))


# 显示图像
plt.savefig('Marginal effect 0.pdf', bbox_inches='tight')

result_df_0['marketShare_acc']

result_df['marketShare_acc']

# 假设两个数据框都有相同的索引或一个共同的 'id' 列
result_df_combined = pd.concat([result_df_0['marketShare_acc'], result_df['marketShare_acc']], axis=1)

# 给列重新命名以避免冲突
result_df_combined.columns = ['marketShare_acc_0', 'marketShare_acc']

print(result_df_combined)

# 绘制图表
import copy
import os

import matplotlib.pyplot as plt
from matplotlib import ticker

margin = copy.deepcopy(result_df_combined)

# margin['var'] = ['Base', 
#                  'Gender (female)',
#                      'Age (less than 24)',
#                      'Education level (less than junior school)',
#                      'Marital status (single)',
#                      'Parental status (with children)', 
#                      'Family size (above 4 people)',
#                      'Working days (less than 5 days)',
#                      'Delivery orders (less than 20)',
#                      'Delivery mileage (above 80 km)',
#                      'Driving speed (above 40km/h)']

margin['var'] = ['Base', 
                 'Gender (female)',
                     'Age (less than 24)',
                     'Education level (less than junior school)',
                     'Marital status (single)',
                     'Parental status (with children)', 
                     'Family size (above 4 people)',]

margin['Marginal effect'] = margin['marketShare_acc'] -  margin['marketShare_acc_0']

margin = margin[-(margin['var']=='Base')]

margin.sort_values('Marginal effect', inplace=True) # 依据减掉均值后的积分进行升序排序

plt.figure(figsize=(13,6)) # 新建画布，尺寸为12*8

plt.rc('font',family='Times New Roman', size=14)
colors = [] #指定条形颜色

for index, value in enumerate(margin['Marginal effect']):
    if value > 0:
        colors.append('#FFBBBB') #超过0的数值为橙色
    else:
        colors.append('#C1F4C5') #低于0的数值为绿色

# 绘制横向条形图      
plt.barh( y = margin['var'] , width = margin['Marginal effect'], height = 0.5, color = colors)
pos = [] #指定要添加文本的x轴位置

for index, value in enumerate(margin['Marginal effect']):
    if value > 0:
        plt.text(x = value,y = index-0.1,s = format(value, '.2%'))
    else:
        plt.text(x = value - 0.05,y = index-0.1,s =  format(value, '.2%'))

plt.xlim(-0.3, 0.33)

plt.gca().xaxis.set_major_formatter(ticker.PercentFormatter(xmax=1, decimals=0))

plt.grid(linestyle='--', alpha=0.5); #配置网格线

plt.savefig('Marginal.pdf',dpi =300, bbox_inches ='tight')

